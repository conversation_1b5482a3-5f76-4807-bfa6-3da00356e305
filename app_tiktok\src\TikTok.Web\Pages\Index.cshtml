﻿@page
@{
    Layout = null;
    ViewData["Title"] = "<PERSON>ệ thống Quản lý Tài khoản Quảng cáo TikTok";
}

<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="UTF-8" />
        <title><PERSON>ệ thống Quản lý Tài khoản Quảng cáo TikTok</title>
        <meta http-equiv="Cache-Control" content="no-cache" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Expires" content="-1" />
        <meta
            name="keywords"
            content="COMME, Affiliate, TikTok, Affiliate TikTok, GMV Max"
        />
        <meta name="description" content="comme.asia" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <script type="text/javascript">
            window.ladi_viewport = function (b) {
                var a = document;
                b = b ? b : 'innerWidth';
                var c = window[b];
                var d = c < 768;
                if (
                    typeof window.ladi_is_desktop == 'undefined' ||
                    window.ladi_is_desktop == undefined
                ) {
                    window.ladi_is_desktop = !d;
                }
                var e = 1200;
                var f = 420;
                var g = '';
                if (!d) {
                    g = 'width=' + e + ',user-scalable=no,initial-scale=1.0';
                } else {
                    var h = 1;
                    var i = f;
                    if (i != c) {
                        h = c / i;
                    }
                    g =
                        'width=' +
                        i +
                        ',user-scalable=no,initial-scale=' +
                        h +
                        ',minimum-scale=' +
                        h +
                        ',maximum-scale=' +
                        h;
                }
                var j = a.getElementById('viewport');
                if (!j) {
                    j = a.createElement('meta');
                    j.id = 'viewport';
                    j.name = 'viewport';
                    a.head.appendChild(j);
                }
                j.setAttribute('content', g);
            };
            window.ladi_viewport();
            window.ladi_fbq_data = [];
            window.ladi_fbq = function () {
                window.ladi_fbq_data.push(arguments);
            };
            window.ladi_ttq_data = [];
            window.ladi_ttq = function () {
                window.ladi_ttq_data.push(arguments);
            };
        </script>
        <link rel="canonical" href="https://www.comme.asia/homepage" />
        <meta property="og:url" content="https://www.comme.asia/homepage" />
        <meta
            property="og:title"
            content="Hệ thống Quản lý Tài khoản Quảng cáo TikTok"
        />
        <meta property="og:type" content="website" />
        <meta
            property="og:image"
            content="https://static.ladipage.net/57e4d138957904ae180e0544/comme-20221209074707-mzewr.jpg"
        />
        <meta property="og:description" content="comme.asia" />
        <meta name="format-detection" content="telephone=no" />
        <link
            rel="icon"
            type="image/x-icon"
            href="https://static.ladipage.net/57e4d138957904ae180e0544/favicon-comme-20221209082149-glffe.svg"
        />
        <link
            rel="shortcut icon"
            href="https://static.ladipage.net/57e4d138957904ae180e0544/favicon-comme-20221209082149-glffe.svg"
        />
        <link
            rel="apple-touch-icon"
            href="https://static.ladipage.net/57e4d138957904ae180e0544/favicon-comme-20221209082149-glffe.svg"
        />
        <link
            rel="apple-touch-icon-precomposed"
            href="https://static.ladipage.net/57e4d138957904ae180e0544/favicon-comme-20221209082149-glffe.svg"
        />
        <meta
            name="msapplication-TileImage"
            content="https://static.ladipage.net/57e4d138957904ae180e0544/favicon-comme-20221209082149-glffe.svg"
        />
        <meta name="revisit-after" content="days" />
        <link rel="dns-prefetch" />
        <link
            rel="preconnect"
            href="https://fonts.googleapis.com/"
            crossorigin
        />
        <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin />
        <link rel="preconnect" href="https://w.ladicdn.com/" crossorigin />
        <link rel="preconnect" href="https://s.ladicdn.com/" crossorigin />
        <link rel="preconnect" href="https://api1.ldpform.com/" crossorigin />
        <link rel="preconnect" href="https://a.ladipage.com/" crossorigin />
        <link
            rel="preconnect"
            href="https://api.sales.ldpform.net/"
            crossorigin
        />
        <link
            rel="preload"
            href="https://w.ladicdn.com/v5/source/ladipagev3.min.js?v=1757673195046"
            as="script"
        />
        <link rel="stylesheet" href="~/css/homepage.css" />
        <script>
            !(function (f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function () {
                    n.callMethod
                        ? n.callMethod.apply(n, arguments)
                        : n.queue.push(arguments);
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s);
            })(
                window,
                document,
                'script',
                'https://connect.facebook.net/en_US/fbevents.js'
            );
            fbq('init', '6168625299889456');
            console.log(`fbq("init", "6168625299889456")`);
            fbq('init', '169153452703462');
            console.log(`fbq("init", "169153452703462")`);
            fbq('init', '991798752130188');
            console.log(`fbq("init", "991798752130188")`);
            window.ladi_conversion_api = window.ladi_conversion_api || {};
            window.ladi_conversion_api.facebook = window.ladi_conversion_api
                .facebook || {
                pixels: [],
            };
            window.ladi_conversion_api.facebook.pixels.push('6168625299889456');
            window.ladi_conversion_api.facebook.pixels.push('169153452703462');
            window.ladi_conversion_api.facebook.pixels.push('991798752130188');
            window.ladi_fbq('track', 'PageView');
            window.ladi_fbq('track', 'ViewContent');
        </script>
        <noscript>
            <img
                height="1"
                width="1"
                style="display: none"
                src="https://www.facebook.com/tr?id=6168625299889456&ev=PageView&noscript=1"
            />
        </noscript>
        <noscript>
            <img
                height="1"
                width="1"
                style="display: none"
                src="https://www.facebook.com/tr?id=169153452703462&ev=PageView&noscript=1"
            />
        </noscript>
        <noscript>
            <img
                height="1"
                width="1"
                style="display: none"
                src="https://www.facebook.com/tr?id=991798752130188&ev=PageView&noscript=1"
            />
        </noscript>
        <script
            async
            src="https://www.googletagmanager.com/gtag/js?id=UA-201041283-1"
        ></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'UA-201041283-1', {
                allow_enhanced_conversions: true,
            });
            gtag('config', 'AW-10957222709', {
                allow_enhanced_conversions: true,
            });
        </script>
        <script>
            !(function (e, t, r, n, c) {
                if (!e.ztrq) {
                    (c = e.ztrq =
                        function () {
                            c.queue
                                ? c.queue.push(arguments)
                                : c.call(c, arguments);
                        }),
                        e._ztrk || (e._ztrk = c),
                        (c.queue = []);
                    var u = t.createElement(r);
                    (u.async = !0), (u.src = n);
                    var a = t.getElementsByTagName(r)[0];
                    a.parentNode.insertBefore(u, a);
                }
            })(
                window,
                document,
                'script',
                'https://s.zzcdn.me/ztr/ztracker.js?id=7056840457216708608'
            );
            window.LadiPageZaloAds = {
                auto_tracking: true,
            };
            ztrq('track', 'ViewContent');
        </script>
        <meta
            name="facebook-domain-verification"
            content="8a3xtb088kdfglkoq0bgrr68mtotgy"
        />
    </head>
    <body class="lazyload">
        <svg
            xmlns="http://www.w3.org/2000/svg"
            style="
                width: 0px;
                height: 0px;
                position: absolute;
                overflow: hidden;
                display: none;
            "
        >
            <symbol id="shape_QcmlqGifrg" viewBox="0 -960 960 960">
                <path
                    d="M480-345 240-585l43-43 197 198 197-197 43 43-240 239Z"
                ></path>
            </symbol>
        </svg>
        <div class="ladi-wraper">
         <!-- Admin Button at Top -->
            <div class="admin-button-container">
                <a href="/Dashboard" class="admin-button">
                    <span class="admin-text">Quản trị</span>
                    <div class="admin-button-glow"></div>
                </a>
            </div>
            <div id="SECTION43" class="ladi-section">
                <div class="ladi-section-background"></div>
                <div class="ladi-container">
                    <div id="GROUP313" class="ladi-element">
                        <div class="ladi-group">
                            <div id="IMAGE227" class="ladi-element">
                                <div class="ladi-image">
                                    <div class="ladi-image-background"></div>
                                </div>
                            </div>
                            <div id="IMAGE262" class="ladi-element">
                                <div class="ladi-image">
                                    <div class="ladi-image-background"></div>
                                </div>
                            </div>
                            <div id="IMAGE263" class="ladi-element">
                                <div class="ladi-image">
                                    <div class="ladi-image-background"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-action="true" id="GROUP270" class="ladi-element">
                        <div class="ladi-group">
                            <div id="BOX130" class="ladi-element">
                                <div class="ladi-box ladi-transition"></div>
                            </div>
                            <div id="BUTTON34" class="ladi-element">
                                <div class="ladi-button">
                                    <div class="ladi-button-background"></div>
                                    <div
                                        id="BUTTON_TEXT34"
                                        class="ladi-element ladi-button-headline"
                                    >
                                        <p class="ladi-headline">
                                            Đăng ký ngay
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="GROUP268" class="ladi-element">
                        <div class="ladi-group">
                            <div id="BOX129" class="ladi-element">
                                <div class="ladi-box ladi-transition"></div>
                            </div>
                            <div id="PARAGRAPH19" class="ladi-element">
                                <div class="ladi-paragraph">#1 Việt Nam</div>
                            </div>
                        </div>
                    </div>
                    <div id="GROUP319" class="ladi-element">
                        <div class="ladi-group">
                            <div id="HEADLINE374" class="ladi-element">
                                <h1 class="ladi-headline">
                                    Tối ưu chiến dịch<br />
                                </h1>
                            </div>
                            <div id="HEADLINE389" class="ladi-element">
                                <h1 class="ladi-headline">GMV MAX</h1>
                            </div>
                        </div>
                    </div>
                    <div id="LIST_PARAGRAPH82" class="ladi-element">
                        <div class="ladi-list-paragraph">
                            <ul>
                                <li>
                                    Tổng quan chi tiêu, ROI, CPA của tất cả các
                                    shop, tài khoản, chiến dịch trên 1
                                    dashboard.
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div id="LIST_PARAGRAPH84" class="ladi-element">
                        <div class="ladi-list-paragraph">
                            <ul>
                                <li>
                                    Phân tích hiệu quả của từng chiến dịch, sản
                                    phẩm, video theo các chỉ số ROI, CPA.
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div id="LIST_PARAGRAPH85" class="ladi-element">
                        <div class="ladi-list-paragraph">
                            <ul>
                                <li>
                                    So sánh hiệu quả trực quan giữa các tài
                                    khoản, chiến dịch, sản phẩm.
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div id="LIST_PARAGRAPH86" class="ladi-element">
                        <div class="ladi-list-paragraph">
                            <ul>
                                <li>
                                    Bộ lọc phân tích nhanh: ngân sách, hiệu quả,
                                    trạng thái chiến dịch, video, livestream.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div id="SECTION46" class="ladi-section">
                <div class="ladi-section-background"></div>
                <div class="ladi-container">
                    <div id="HEADLINE377" class="ladi-element">
                        <h2 class="ladi-headline">
                            Tại sao các nhà quảng cáo nên sử dụng hệ thống của
                            Comme?
                        </h2>
                    </div>
                    <div id="LINE29" class="ladi-element">
                        <div class="ladi-line">
                            <div class="ladi-line-container"></div>
                        </div>
                    </div>
                    <div id="LINE31" class="ladi-element">
                        <div class="ladi-line">
                            <div class="ladi-line-container"></div>
                        </div>
                    </div>
                    <div id="GROUP316" class="ladi-element">
                        <div class="ladi-group">
                            <div id="GROUP272" class="ladi-element">
                                <div class="ladi-group">
                                    <div id="BOX131" class="ladi-element">
                                        <div
                                            class="ladi-box ladi-transition"
                                        ></div>
                                    </div>
                                    <div id="PARAGRAPH22" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Quản lý tập trung
                                        </div>
                                    </div>
                                    <div id="PARAGRAPH23" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Toàn bộ tài khoản TikTok Ads hiển
                                            thị trong một dashboard duy nhất.
                                        </div>
                                    </div>
                                    <div id="IMAGE228" class="ladi-element">
                                        <div class="ladi-image">
                                            <div
                                                class="ladi-image-background"
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="GROUP273" class="ladi-element">
                                <div class="ladi-group">
                                    <div id="BOX134" class="ladi-element">
                                        <div
                                            class="ladi-box ladi-transition"
                                        ></div>
                                    </div>
                                    <div id="PARAGRAPH24" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Tối ưu hiệu suất
                                        </div>
                                    </div>
                                    <div id="PARAGRAPH25" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Auto-Budget phân bổ ngân sách thông
                                            minh, tập trung vào chiến dịch hiệu
                                            quả.
                                        </div>
                                    </div>
                                    <div id="IMAGE229" class="ladi-element">
                                        <div class="ladi-image">
                                            <div
                                                class="ladi-image-background"
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="GROUP274" class="ladi-element">
                                <div class="ladi-group">
                                    <div id="BOX135" class="ladi-element">
                                        <div
                                            class="ladi-box ladi-transition"
                                        ></div>
                                    </div>
                                    <div id="PARAGRAPH26" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Cảnh báo rủi ro
                                        </div>
                                    </div>
                                    <div id="PARAGRAPH27" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Phát hiện sớm vi phạm chính sách,
                                            giảm nguy cơ khóa tài khoản.
                                        </div>
                                    </div>
                                    <div id="IMAGE230" class="ladi-element">
                                        <div class="ladi-image">
                                            <div
                                                class="ladi-image-background"
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="GROUP275" class="ladi-element">
                                <div class="ladi-group">
                                    <div id="BOX136" class="ladi-element">
                                        <div
                                            class="ladi-box ladi-transition"
                                        ></div>
                                    </div>
                                    <div id="PARAGRAPH28" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Phân quyền an toàn
                                        </div>
                                    </div>
                                    <div id="PARAGRAPH29" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Kiểm soát truy cập theo vai trò, lưu
                                            vết mọi thao tác.
                                        </div>
                                    </div>
                                    <div id="IMAGE231" class="ladi-element">
                                        <div class="ladi-image">
                                            <div
                                                class="ladi-image-background"
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="GROUP276" class="ladi-element">
                                <div class="ladi-group">
                                    <div id="BOX137" class="ladi-element">
                                        <div
                                            class="ladi-box ladi-transition"
                                        ></div>
                                    </div>
                                    <div id="PARAGRAPH30" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Báo cáo nhanh chóng
                                        </div>
                                    </div>
                                    <div id="PARAGRAPH31" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Hợp nhất dữ liệu đa tài khoản, xuất
                                            file chỉ với một cú nhấp chuột.
                                        </div>
                                    </div>
                                    <div id="IMAGE232" class="ladi-element">
                                        <div class="ladi-image">
                                            <div
                                                class="ladi-image-background"
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="GROUP277" class="ladi-element">
                                <div class="ladi-group">
                                    <div id="BOX138" class="ladi-element">
                                        <div
                                            class="ladi-box ladi-transition"
                                        ></div>
                                    </div>
                                    <div id="PARAGRAPH32" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Tiết kiệm thời gian, chi phí
                                        </div>
                                    </div>
                                    <div id="PARAGRAPH33" class="ladi-element">
                                        <div class="ladi-paragraph">
                                            Cắt giảm 30–50% khối lượng công việc
                                            quản trị, nâng cao ROI.
                                        </div>
                                    </div>
                                    <div id="IMAGE233" class="ladi-element">
                                        <div class="ladi-image">
                                            <div
                                                class="ladi-image-background"
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="SHAPE34" class="ladi-element">
                        <div class="ladi-shape">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="100%"
                                height="100%"
                                preserveAspectRatio="none"
                                viewBox="0 0 1792 1896.0833"
                                class=""
                                fill="rgba(222, 118, 0, 1)"
                            >
                                <path
                                    d="M640 128q-53 0-90.5 37.5T512 256v896L361 950q-41-54-107-54-52 0-89 38t-37 90q0 43 26 77l384 512q38 51 102 51h718q22 0 39.5-13.5t22.5-34.5l92-368q24-96 24-194V837q0-41-28-71t-68-30-68 28-28 68h-32v-61q0-48-32-81.5t-80-33.5q-46 0-79 33t-33 79v64h-32v-90q0-55-37-94.5T928 608q-53 0-90.5 37.5T800 736v96h-32V262q0-55-37-94.5T640 128zm0-128q107 0 181.5 77.5T896 262v220q22-2 32-2 99 0 173 69 47-21 99-21 113 0 184 87 27-7 56-7 94 0 159 67.5t65 161.5v217q0 116-28 225l-92 368q-16 64-68 104.5t-118 40.5H640q-60 0-114.5-27.5T435 1690L51 1178q-51-68-51-154 0-105 74.5-180.5T254 768q71 0 130 35V256q0-106 75-181T640 0zm128 1408v-384h-32v384h32zm256 0v-384h-32v384h32zm256 0v-384h-32v384h32z"
                                ></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div id="hethong" class="ladi-section">
                <div class="ladi-section-background"></div>
                <div class="ladi-container">
                    <div id="HEADLINE378" class="ladi-element">
                        <h2 class="ladi-headline">Hệ thống quản lý bao gồm?</h2>
                    </div>
                    <div id="LINE30" class="ladi-element">
                        <div class="ladi-line">
                            <div class="ladi-line-container"></div>
                        </div>
                    </div>
                    <div data-action="true" id="GROUP314" class="ladi-element">
                        <div class="ladi-group">
                            <div id="BOX167" class="ladi-element">
                                <div class="ladi-box ladi-transition"></div>
                            </div>
                            <div id="BUTTON38" class="ladi-element">
                                <div class="ladi-button">
                                    <div class="ladi-button-background"></div>
                                    <div
                                        id="BUTTON_TEXT38"
                                        class="ladi-element ladi-button-headline"
                                    >
                                        <p class="ladi-headline">
                                            Đăng ký ngay
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="BOX139" class="ladi-element">
                        <div class="ladi-box ladi-transition"></div>
                    </div>
                    <div id="IMAGE240" class="ladi-element">
                        <div class="ladi-image">
                            <div class="ladi-image-background"></div>
                        </div>
                    </div>
                    <div id="PARAGRAPH34" class="ladi-element">
                        <div class="ladi-paragraph">Dashboard tập trung</div>
                    </div>
                    <div id="LIST_PARAGRAPH78" class="ladi-element">
                        <div class="ladi-list-paragraph">
                            <ul>
                                <li>
                                    Tổng quan chi tiêu, doanh thu, ROAS, CPA
                                    theo ngày/tuần/tháng
                                </li>
                                <li>
                                    Biểu đồ trực quan theo tài khoản / campaign
                                    / ad group
                                </li>
                                <li>
                                    Bộ lọc nhanh: ngân sách, hiệu quả, trạng
                                    thái chiến dịch
                                </li>
                                <li>
                                    Theo dõi KPI theo mục tiêu định sẵn (ROAS,
                                    CTR, CVR)
                                </li>
                                <li>
                                    So sánh hiệu suất giữa các tài khoản/nhãn
                                    hàng
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div id="BOX146" class="ladi-element">
                        <div class="ladi-box ladi-transition"></div>
                    </div>
                    <div id="IMAGE242" class="ladi-element">
                        <div class="ladi-image">
                            <div class="ladi-image-background"></div>
                        </div>
                    </div>
                    <div id="PARAGRAPH47" class="ladi-element">
                        <div class="ladi-paragraph">
                            Tối ưu &amp;cảnh báo tự động
                        </div>
                    </div>
                    <div id="LIST_PARAGRAPH80" class="ladi-element">
                        <div class="ladi-list-paragraph">
                            <ul>
                                <li>
                                    Auto-Budget phân bổ ngân sách thông minh
                                </li>
                                <li>Tạm dừng nhóm quảng cáo kém hiệu quả</li>
                                <li>Cảnh báo vi phạm chính sách TikTok</li>
                                <li>
                                    Cảnh báo bất thường về chi phí &amp;hiệu
                                    suất
                                </li>
                                <li>Gợi ý tối ưu dựa trên dữ liệu lịch sử</li>
                            </ul>
                        </div>
                    </div>
                    <div id="BOX147" class="ladi-element">
                        <div class="ladi-box ladi-transition"></div>
                    </div>
                    <div id="IMAGE243" class="ladi-element">
                        <div class="ladi-image">
                            <div class="ladi-image-background"></div>
                        </div>
                    </div>
                    <div id="PARAGRAPH48" class="ladi-element">
                        <div class="ladi-paragraph">Báo cáo &amp;bảo mật</div>
                    </div>
                    <div id="LIST_PARAGRAPH81" class="ladi-element">
                        <div class="ladi-list-paragraph">
                            <ul>
                                <li>Hợp nhất dữ liệu đa tài khoản</li>
                                <li>
                                    Xuất file CSV/PDF/XLSX, gửi mail tự động
                                </li>
                                <li>Quản lý vai trò người dùng rõ ràng</li>
                                <li>Bảo mật 2FA &amp;whitelist IP</li>
                                <li>Nhật ký thao tác và truy cập đầy đủ</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div id="SECTION48" class="ladi-section">
                <div class="ladi-section-background"></div>
                <div class="ladi-container">
                    <div id="HEADLINE379" class="ladi-element">
                        <h2 class="ladi-headline">
                            Các chuyên gia hàng đầu trong ngành nói gì về Comme
                        </h2>
                    </div>
                    <div id="LINE32" class="ladi-element">
                        <div class="ladi-line">
                            <div class="ladi-line-container"></div>
                        </div>
                    </div>
                    <div id="GROUP301" class="ladi-element">
                        <div class="ladi-group">
                            <div id="BOX148" class="ladi-element">
                                <div class="ladi-box ladi-transition"></div>
                            </div>
                            <div id="PARAGRAPH49" class="ladi-element">
                                <div class="ladi-paragraph">Ngô Duy Linh</div>
                            </div>
                            <div id="PARAGRAPH50" class="ladi-element">
                                <div class="ladi-paragraph">
                                    Chief of E-Commerce, Busines Head tại
                                    LiveSpo
                                </div>
                            </div>
                            <div id="IMAGE244" class="ladi-element">
                                <div class="ladi-image">
                                    <div class="ladi-image-background"></div>
                                </div>
                            </div>
                            <div id="PARAGRAPH61" class="ladi-element">
                                <div class="ladi-paragraph">
                                    Comme đã giúp agency của chúng tôi giảm 40%
                                    thời gian quản trị quảng cáo, đồng thời cải
                                    thiện ROAS trung bình lên 15% chỉ sau 1
                                    tháng.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="GROUP302" class="ladi-element">
                        <div class="ladi-group">
                            <div id="BOX156" class="ladi-element">
                                <div class="ladi-box ladi-transition"></div>
                            </div>
                            <div id="PARAGRAPH68" class="ladi-element">
                                <div class="ladi-paragraph">
                                    Nguyễn Tư Chuẩn
                                </div>
                            </div>
                            <div id="PARAGRAPH69" class="ladi-element">
                                <div class="ladi-paragraph">CEO Hecatech</div>
                            </div>
                            <div id="IMAGE252" class="ladi-element">
                                <div class="ladi-image">
                                    <div class="ladi-image-background"></div>
                                </div>
                            </div>
                            <div id="PARAGRAPH70" class="ladi-element">
                                <div class="ladi-paragraph">
                                    Với hàng chục nhân sự marketing và hàng trăm
                                    tài khoản quảng cáo triển khai cùng lúc,
                                    việc đo lường, đánh giá hiệu quả và tối ưu
                                    hóa qua công cụ GMV Max của comme đã giúp
                                    công ty mình "nhàn" hơn rất nhiều
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="GROUP303" class="ladi-element">
                        <div class="ladi-group">
                            <div id="BOX157" class="ladi-element">
                                <div class="ladi-box ladi-transition"></div>
                            </div>
                            <div id="PARAGRAPH71" class="ladi-element">
                                <div class="ladi-paragraph">
                                    Quan Hong Nguyen &nbsp;
                                </div>
                            </div>
                            <div id="PARAGRAPH72" class="ladi-element">
                                <div class="ladi-paragraph">CMO Vua Nệm</div>
                            </div>
                            <div id="IMAGE253" class="ladi-element">
                                <div class="ladi-image">
                                    <div class="ladi-image-background"></div>
                                </div>
                            </div>
                            <div id="PARAGRAPH73" class="ladi-element">
                                <div class="ladi-paragraph">
                                    Tính năng cảnh báo rủi ro chính là cứu cánh,
                                    giúp chúng tôi tránh nhiều lần tài khoản bị
                                    khóa vô cớ.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="dangky" class="ladi-section">
                <div class="ladi-section-background"></div>
                <div class="ladi-container">
                    <div id="BOX158" class="ladi-element">
                        <div class="ladi-box ladi-transition"></div>
                    </div>
                    <div id="IMAGE257" class="ladi-element">
                        <div class="ladi-image">
                            <div class="ladi-image-background"></div>
                        </div>
                    </div>
                    <div id="PARAGRAPH76" class="ladi-element">
                        <div class="ladi-paragraph">
                            Đăng ký và tham gia ngay để tối ưu hoá quảng cáo
                            TikTok.<br />
                        </div>
                    </div>
                    <div id="PARAGRAPH74" class="ladi-element">
                        <div class="ladi-paragraph">
                            Với hơn 1.000+ khách hàng<br />sử dụng dịch vụ của
                            Comme
                        </div>
                    </div>
                    <div
                        id="FORM10"
                        data-config-id="68c27e0172e7270012267cca"
                        class="ladi-element"
                    >
                        <form
                            autocomplete="off"
                            method="post"
                            class="ladi-form"
                        >
                            <div id="BUTTON35" class="ladi-element">
                                <div class="ladi-button">
                                    <div class="ladi-button-background"></div>
                                    <div
                                        id="BUTTON_TEXT35"
                                        class="ladi-element ladi-button-headline"
                                    >
                                        <p class="ladi-headline">Đăng ký</p>
                                    </div>
                                </div>
                            </div>
                            <div id="FORM_ITEM43" class="ladi-element">
                                <div class="ladi-form-item-container">
                                    <div
                                        class="ladi-form-item-background"
                                    ></div>
                                    <div class="ladi-form-item">
                                        <input
                                            autocomplete="off"
                                            tabindex="2"
                                            name="email"
                                            required
                                            class="ladi-form-control"
                                            type="email"
                                            placeholder="Email"
                                            value=""
                                        />
                                    </div>
                                </div>
                            </div>
                            <div id="FORM_ITEM44" class="ladi-element">
                                <div class="ladi-form-item-container">
                                    <div
                                        class="ladi-form-item-background"
                                    ></div>
                                    <div class="ladi-form-item">
                                        <input
                                            autocomplete="off"
                                            tabindex="3"
                                            name="phone"
                                            required
                                            class="ladi-form-control"
                                            type="tel"
                                            placeholder="Số điện thoại"
                                            pattern="(\+84|0)(9|8|7|5|3)[0-9]{8}"
                                            value=""
                                        />
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="ladi-hidden"></button>
                        </form>
                    </div>
                </div>
            </div>
            <div id="SECTION50" class="ladi-section">
                <div class="ladi-section-background"></div>
                <div class="ladi-container">
                    <div id="HEADLINE381" class="ladi-element">
                        <h2 class="ladi-headline">Câu hỏi thường gặp (FAQ)</h2>
                    </div>
                    <div id="LINE35" class="ladi-element">
                        <div class="ladi-line">
                            <div class="ladi-line-container"></div>
                        </div>
                    </div>
                    <div id="ACCORDION9" class="ladi-element">
                        <div class="ladi-accordion">
                            <div id="ACCORDION_CONTENT24" class="ladi-element">
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div id="PARAGRAPH96" class="ladi-element">
                                        <div
                                            class="ladi-paragraph ladi-transition"
                                        >
                                            Không. Comme tối ưu dựa trên biên
                                            hiệu quả và giữ nguyên chu kỳ học
                                            máy.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                data-action="true"
                                id="ACCORDION_MENU25"
                                class="ladi-element accordion-menu"
                            >
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div
                                        id="ACCORDION_SHAPE31"
                                        class="ladi-element ladi-accordion-shape"
                                    >
                                        <div class="ladi-shape ladi-transition">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="100%"
                                                viewBox="0 0 960 960"
                                                width="100%"
                                                preserveAspectRatio="none"
                                                class=""
                                                fill="rgba(222, 118, 0, 1)"
                                            >
                                                <use
                                                    xlink:href="#shape_QcmlqGifrg"
                                                ></use>
                                            </svg>
                                        </div>
                                    </div>
                                    <div id="HEADLINE386" class="ladi-element">
                                        <h6
                                            class="ladi-headline ladi-transition"
                                        >
                                            3. Auto-Budget có ảnh hưởng đến quá
                                            trình học máy của quảng cáo
                                            không?<br />
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="ACCORDION10" class="ladi-element">
                        <div class="ladi-accordion">
                            <div id="ACCORDION_CONTENT26" class="ladi-element">
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div id="PARAGRAPH97" class="ladi-element">
                                        <div
                                            class="ladi-paragraph ladi-transition"
                                        >
                                            Có. Hệ thống hỗ trợ workspace riêng
                                            biệt, đa tiền tệ và múi giờ.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                data-action="true"
                                id="ACCORDION_MENU27"
                                class="ladi-element accordion-menu"
                            >
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div
                                        id="ACCORDION_SHAPE32"
                                        class="ladi-element ladi-accordion-shape"
                                    >
                                        <div class="ladi-shape ladi-transition">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="100%"
                                                viewBox="0 0 960 960"
                                                width="100%"
                                                preserveAspectRatio="none"
                                                class=""
                                                fill="rgba(222, 118, 0, 1)"
                                            >
                                                <use
                                                    xlink:href="#shape_QcmlqGifrg"
                                                ></use>
                                            </svg>
                                        </div>
                                    </div>
                                    <div id="HEADLINE387" class="ladi-element">
                                        <h6
                                            class="ladi-headline ladi-transition"
                                        >
                                            4. Có thể dùng cho nhiều brand hoặc
                                            quốc gia không?<br />
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="ACCORDION6" class="ladi-element">
                        <div class="ladi-accordion">
                            <div id="ACCORDION_CONTENT18" class="ladi-element">
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div id="PARAGRAPH93" class="ladi-element">
                                        <div
                                            class="ladi-paragraph ladi-transition"
                                        >
                                            Không. Comme kết nối qua API chính
                                            thức, hoàn toàn tuân thủ điều khoản
                                            TikTok.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                data-action="true"
                                id="ACCORDION_MENU19"
                                class="ladi-element accordion-menu"
                            >
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div
                                        id="ACCORDION_SHAPE28"
                                        class="ladi-element ladi-accordion-shape"
                                    >
                                        <div class="ladi-shape ladi-transition">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="100%"
                                                viewBox="0 0 960 960"
                                                width="100%"
                                                preserveAspectRatio="none"
                                                class=""
                                                fill="rgba(222, 118, 0, 1)"
                                            >
                                                <use
                                                    xlink:href="#shape_QcmlqGifrg"
                                                ></use>
                                            </svg>
                                        </div>
                                    </div>
                                    <div id="HEADLINE383" class="ladi-element">
                                        <h6
                                            class="ladi-headline ladi-transition"
                                        >
                                            1. Hệ thống của Comme có vi phạm
                                            chính sách TikTok không?<br />
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="ACCORDION7" class="ladi-element">
                        <div class="ladi-accordion">
                            <div id="ACCORDION_CONTENT20" class="ladi-element">
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div id="PARAGRAPH94" class="ladi-element">
                                        <div
                                            class="ladi-paragraph ladi-transition"
                                        >
                                            100%. Dữ liệu được mã hóa, lưu trữ
                                            bảo mật và bạn kiểm soát toàn bộ
                                            quyền truy cập.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                data-action="true"
                                id="ACCORDION_MENU21"
                                class="ladi-element accordion-menu"
                            >
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div
                                        id="ACCORDION_SHAPE29"
                                        class="ladi-element ladi-accordion-shape"
                                    >
                                        <div class="ladi-shape ladi-transition">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="100%"
                                                viewBox="0 0 960 960"
                                                width="100%"
                                                preserveAspectRatio="none"
                                                class=""
                                                fill="rgba(222, 118, 0, 1)"
                                            >
                                                <use
                                                    xlink:href="#shape_QcmlqGifrg"
                                                ></use>
                                            </svg>
                                        </div>
                                    </div>
                                    <div id="HEADLINE384" class="ladi-element">
                                        <h6
                                            class="ladi-headline ladi-transition"
                                        >
                                            2. Dữ liệu có an toàn không?<br />
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="ACCORDION11" class="ladi-element">
                        <div class="ladi-accordion">
                            <div id="ACCORDION_CONTENT28" class="ladi-element">
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div id="PARAGRAPH98" class="ladi-element">
                                        <div
                                            class="ladi-paragraph ladi-transition"
                                        >
                                            Có. Bạn hoàn toàn chủ động hủy hoặc
                                            nâng cấp gói dịch vụ bất kỳ lúc nào.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                data-action="true"
                                id="ACCORDION_MENU29"
                                class="ladi-element accordion-menu"
                            >
                                <div
                                    class="ladi-frame ladi-frame-bg ladi-transition"
                                >
                                    <div class="ladi-frame-background"></div>
                                    <div
                                        id="ACCORDION_SHAPE33"
                                        class="ladi-element ladi-accordion-shape"
                                    >
                                        <div class="ladi-shape ladi-transition">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="100%"
                                                viewBox="0 0 960 960"
                                                width="100%"
                                                preserveAspectRatio="none"
                                                class=""
                                                fill="rgba(222, 118, 0, 1)"
                                            >
                                                <use
                                                    xlink:href="#shape_QcmlqGifrg"
                                                ></use>
                                            </svg>
                                        </div>
                                    </div>
                                    <div id="HEADLINE388" class="ladi-element">
                                        <h6
                                            class="ladi-headline ladi-transition"
                                        >
                                            5. Tôi có thể hủy bất kỳ lúc nào
                                            không?<br />
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                id="G1705289934678_chantrang"
                data-global-id="65a4a8ceb1287100209fa35e"
                data-store-id="57e4d138957904ae180e0544"
                class="ladi-section"
            ></div>

            <div id="SECTION_POPUP" class="ladi-section">
                <div class="ladi-section-background"></div>
                <div class="ladi-container">
                    <div id="POPUP2" class="ladi-element">
                        <div class="ladi-popup">
                            <div class="ladi-popup-background"></div>
                            <div class="ladi-overlay"></div>
                            <div id="BOX128" class="ladi-element">
                                <div class="ladi-box ladi-transition"></div>
                            </div>
                            <div id="IMAGE225" class="ladi-element">
                                <div class="ladi-image">
                                    <div class="ladi-image-background"></div>
                                </div>
                            </div>
                            <div id="PARAGRAPH16" class="ladi-element">
                                <div class="ladi-paragraph">
                                    ĐĂNG KÝ THÀNH CÔNG!
                                </div>
                            </div>
                            <div id="PARAGRAPH17" class="ladi-element">
                                <div class="ladi-paragraph">
                                    Vui lòng tham gia nhóm Zalo để cập nhật<br />
                                    những thông tin, tài liệu, lịch tham dự
                                    Webinar.<br />
                                </div>
                            </div>
                            <a
                                href="https://zalo.me/g/udbjwz250"
                                target="_blank"
                                id="BUTTON33"
                                class="ladi-element"
                            >
                                <div class="ladi-button ladi-transition">
                                    <div class="ladi-button-background"></div>
                                    <div
                                        id="BUTTON_TEXT33"
                                        class="ladi-element ladi-button-headline"
                                    >
                                        <p
                                            class="ladi-headline ladi-transition"
                                        >
                                            bấm tham gia group zalo
                                        </p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="backdrop-popup" class="backdrop-popup"></div>
        <div id="backdrop-dropbox" class="backdrop-dropbox"></div>
        <div id="lightbox-screen" class="lightbox-screen"></div>
        <script id="script_lazyload" type="text/javascript">
            window.lazyload_run = function (dom, is_first, check_dom_rect) {
                if (
                    check_dom_rect &&
                    (document.body.clientWidth <= 0 ||
                        document.body.clientheight <= 0)
                ) {
                    return setTimeout(function () {
                        window.lazyload_run(dom, is_first, check_dom_rect);
                    }, 1);
                }
                var style_lazyload = document.getElementById('style_lazyload');
                var list_element_lazyload = dom.querySelectorAll(
                    'body.lazyload .ladi-overlay, body.lazyload .ladi-box, body.lazyload .ladi-button-background, body.lazyload .ladi-collection-item, body.lazyload .ladi-countdown-background, body.lazyload .ladi-form-item-background, body.lazyload .ladi-form-label-container .ladi-form-label-item.image, body.lazyload .ladi-frame-background, body.lazyload .ladi-gallery-view-item, body.lazyload .ladi-gallery-control-item, body.lazyload .ladi-headline, body.lazyload .ladi-image-background, body.lazyload .ladi-image-compare, body.lazyload .ladi-list-paragraph ul li, body.lazyload .ladi-section-background, body.lazyload .ladi-survey-option-background, body.lazyload .ladi-survey-option-image, body.lazyload .ladi-tabs-background, body.lazyload .ladi-video-background, body.lazyload .ladi-banner, body.lazyload .ladi-spin-lucky-screen, body.lazyload .ladi-spin-lucky-start'
                );
                var docEventScroll = window;
                for (var i = 0; i < list_element_lazyload.length; i++) {
                    var rect = list_element_lazyload[i].getBoundingClientRect();
                    if (
                        rect.x == 'undefined' ||
                        rect.x == undefined ||
                        rect.y == 'undefined' ||
                        rect.y == undefined
                    ) {
                        rect.x = rect.left;
                        rect.y = rect.top;
                    }
                    var offset_top = rect.y + window.scrollY;
                    if (
                        offset_top >= window.scrollY + window.innerHeight ||
                        window.scrollY >=
                            offset_top + list_element_lazyload[i].offsetHeight
                    ) {
                        list_element_lazyload[i].classList.add('ladi-lazyload');
                    }
                }
                if (
                    typeof style_lazyload != 'undefined' &&
                    style_lazyload != undefined
                ) {
                    style_lazyload.parentElement.removeChild(style_lazyload);
                }
                document.body.classList.remove('lazyload');
                var currentScrollY = window.scrollY;
                var stopLazyload = function (event) {
                    if (
                        event.type == 'scroll' &&
                        window.scrollY == currentScrollY
                    ) {
                        currentScrollY = -1;
                        return;
                    }
                    docEventScroll.removeEventListener('scroll', stopLazyload);
                    list_element_lazyload =
                        document.getElementsByClassName('ladi-lazyload');
                    while (list_element_lazyload.length > 0) {
                        list_element_lazyload[0].classList.remove(
                            'ladi-lazyload'
                        );
                    }
                };
                if (is_first) {
                    var scrollEventPassive = null;
                    try {
                        var opts = Object.defineProperty({}, 'passive', {
                            get: function () {
                                scrollEventPassive = {
                                    passive: true,
                                };
                            },
                        });
                        window.addEventListener('testPassive', null, opts);
                        window.removeEventListener('testPassive', null, opts);
                    } catch (e) {}
                    docEventScroll.addEventListener(
                        'scroll',
                        stopLazyload,
                        scrollEventPassive
                    );
                }
                return dom;
            };
            window.lazyload_run(document, true, true);
        </script>
        <script
            src="https://w.ladicdn.com/v5/source/ladipagev3.min.js?v=1757673195046"
            type="text/javascript"
        ></script>
        <script id="script_event_data" type="application/json">
            {
                "POPUP2": {
                    "a": "popup",
                    "X": "default",
                    "U": "background-color: rgba(0, 0, 0, 0.5);"
                },
                "IMAGE225": {
                    "a": "image",
                    "F": "tada",
                    "C": "0s"
                },
                "PARAGRAPH16": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH17": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "BUTTON_TEXT33": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "BUTTON33": {
                    "a": "button",
                    "cs": [
                        {
                            "dr": "action",
                            "dv": "_blank",
                            "dw": "https://zalo.me/g/udbjwz250",
                            "a": "link"
                        }
                    ],
                    "F": "pulse",
                    "C": "0s"
                },
                "HEADLINE374": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline",
                    "F": "fadeInLeft",
                    "C": "0s"
                },
                "PARAGRAPH19": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "GROUP268": {
                    "a": "group",
                    "F": "fadeInUp",
                    "C": "0s"
                },
                "BUTTON_TEXT34": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "GROUP270": {
                    "a": "group",
                    "cs": [
                        {
                            "dr": "action",
                            "dw": "dangky",
                            "a": "section"
                        }
                    ]
                },
                "HEADLINE377": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline",
                    "F": "fadeInLeft"
                },
                "LINE29": {
                    "a": "line",
                    "F": "fadeInLeft",
                    "C": "0s"
                },
                "PARAGRAPH22": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH23": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH24": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH25": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH26": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH27": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH28": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH29": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH30": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH31": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH32": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH33": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "HEADLINE378": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline",
                    "F": "fadeInLeft",
                    "C": "0s"
                },
                "LINE30": {
                    "a": "line",
                    "F": "fadeInLeft",
                    "C": "0s"
                },
                "PARAGRAPH34": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH47": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH48": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "HEADLINE379": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline",
                    "F": "fadeInLeft",
                    "C": "0s"
                },
                "LINE32": {
                    "a": "line",
                    "F": "fadeInLeft",
                    "C": "0s"
                },
                "PARAGRAPH49": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH50": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH61": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH68": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH69": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH70": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH71": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH72": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH73": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH74": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "PARAGRAPH76": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "BUTTON_TEXT35": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "FORM_ITEM43": {
                    "a": "form_item",
                    "bS": "email",
                    "bQ": 2
                },
                "FORM_ITEM44": {
                    "a": "form_item",
                    "bS": "tel",
                    "bQ": 3
                },
                "FORM10": {
                    "option.dynamic_form_config": "&#123;&#34;configs&#34;:[],&#34;default_actions&#34;:[&#123;&#34;text_thankyou_value&#34;:&#34;Cảm ơn bạn đã quan tâm!&#34;,&#34;type&#34;:&#34;form_show_popup_default&#34;,&#34;no_delete&#34;:true&#125;]&#125;",
                    "a": "form",
                    "bP": "68c27e0172e7270012267cca",
                    "bM": true,
                    "by": true,
                    "bx": true
                },
                "HEADLINE381": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "PARAGRAPH93": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "ACCORDION_SHAPE28": {
                    "a": "shape",
                    "aO": true,
                    "g": "&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; height=&#34;100%&#34; viewBox=&#34;0 -960 960 960&#34; width=&#34;100%&#34; preserveAspectRatio=&#34;none&#34; class=&#34;&#34; fill=&#34;rgba(222, 118, 0, 1)&#34;&#62;&#60;path d=&#34;m283-345-43-43 240-240 240 239-43 43-197-197-197 198Z&#34;&#62;&#60;/path&#62;&#60;/svg&#62;"
                },
                "HEADLINE383": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "ACCORDION_MENU19": {
                    "a": "frame",
                    "cs": [
                        {
                            "dr": "action",
                            "dw": "ACCORDION_CONTENT18",
                            "dG": true,
                            "a": "collapse"
                        }
                    ]
                },
                "PARAGRAPH94": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "ACCORDION_SHAPE29": {
                    "a": "shape",
                    "aO": true,
                    "g": "&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; height=&#34;100%&#34; viewBox=&#34;0 -960 960 960&#34; width=&#34;100%&#34; preserveAspectRatio=&#34;none&#34; class=&#34;&#34; fill=&#34;rgba(222, 118, 0, 1)&#34;&#62;&#60;path d=&#34;m283-345-43-43 240-240 240 239-43 43-197-197-197 198Z&#34;&#62;&#60;/path&#62;&#60;/svg&#62;"
                },
                "HEADLINE384": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "ACCORDION_MENU21": {
                    "a": "frame",
                    "cs": [
                        {
                            "dr": "action",
                            "dw": "ACCORDION_CONTENT20",
                            "dG": true,
                            "a": "collapse"
                        }
                    ]
                },
                "PARAGRAPH96": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "ACCORDION_SHAPE31": {
                    "a": "shape",
                    "aO": true,
                    "g": "&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; height=&#34;100%&#34; viewBox=&#34;0 -960 960 960&#34; width=&#34;100%&#34; preserveAspectRatio=&#34;none&#34; class=&#34;&#34; fill=&#34;rgba(222, 118, 0, 1)&#34;&#62;&#60;path d=&#34;m283-345-43-43 240-240 240 239-43 43-197-197-197 198Z&#34;&#62;&#60;/path&#62;&#60;/svg&#62;"
                },
                "HEADLINE386": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "ACCORDION_MENU25": {
                    "a": "frame",
                    "cs": [
                        {
                            "dr": "action",
                            "dw": "ACCORDION_CONTENT24",
                            "dG": true,
                            "a": "collapse"
                        }
                    ]
                },
                "PARAGRAPH97": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "ACCORDION_SHAPE32": {
                    "a": "shape",
                    "aO": true,
                    "g": "&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; height=&#34;100%&#34; viewBox=&#34;0 -960 960 960&#34; width=&#34;100%&#34; preserveAspectRatio=&#34;none&#34; class=&#34;&#34; fill=&#34;rgba(222, 118, 0, 1)&#34;&#62;&#60;path d=&#34;m283-345-43-43 240-240 240 239-43 43-197-197-197 198Z&#34;&#62;&#60;/path&#62;&#60;/svg&#62;"
                },
                "HEADLINE387": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "ACCORDION_MENU27": {
                    "a": "frame",
                    "cs": [
                        {
                            "dr": "action",
                            "dw": "ACCORDION_CONTENT26",
                            "dG": true,
                            "a": "collapse"
                        }
                    ]
                },
                "PARAGRAPH98": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "paragraph"
                },
                "ACCORDION_SHAPE33": {
                    "a": "shape",
                    "aO": true,
                    "g": "&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; height=&#34;100%&#34; viewBox=&#34;0 -960 960 960&#34; width=&#34;100%&#34; preserveAspectRatio=&#34;none&#34; class=&#34;&#34; fill=&#34;rgba(222, 118, 0, 1)&#34;&#62;&#60;path d=&#34;m283-345-43-43 240-240 240 239-43 43-197-197-197 198Z&#34;&#62;&#60;/path&#62;&#60;/svg&#62;"
                },
                "HEADLINE388": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "ACCORDION_MENU29": {
                    "a": "frame",
                    "cs": [
                        {
                            "dr": "action",
                            "dw": "ACCORDION_CONTENT28",
                            "dG": true,
                            "a": "collapse"
                        }
                    ]
                },
                "IMAGE262": {
                    "a": "image",
                    "F": "fadeInUp",
                    "C": "0s"
                },
                "IMAGE263": {
                    "a": "image",
                    "F": "fadeInDown",
                    "C": "0s"
                },
                "BUTTON_TEXT38": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline"
                },
                "GROUP314": {
                    "a": "group",
                    "cs": [
                        {
                            "dr": "action",
                            "dw": "dangky",
                            "a": "section"
                        }
                    ]
                },
                "GROUP316": {
                    "a": "group",
                    "aE": true
                },
                "SHAPE34": {
                    "a": "shape",
                    "F": "shake",
                    "C": "0s"
                },
                "HEADLINE389": {
                    "option.is_product_mapping_name_custom": true,
                    "a": "headline",
                    "F": "fadeInLeft",
                    "C": "0s"
                }
            }
        </script>
        <script id="script_ladipage_run" type="text/javascript">
            (function () {
                var run = function () {
                    if (
                        typeof window.LadiPageScript == 'undefined' ||
                        typeof window.ladi == 'undefined' ||
                        window.ladi == undefined
                    ) {
                        setTimeout(run, 100);
                        return;
                    }
                    window.LadiPageApp =
                        window.LadiPageApp || new window.LadiPageAppV2();
                    window.LadiPageScript.runtime.ladipage_id =
                        '68c23a9272e72700121e464e';
                    window.LadiPageScript.runtime.publish_platform =
                        'LADIPAGEDNS';
                    window.LadiPageScript.runtime.version = '1757673195046';
                    window.LadiPageScript.runtime.cdn_url =
                        'https://w.ladicdn.com/v5/source/';
                    window.LadiPageScript.runtime.DOMAIN_SET_COOKIE = [
                        'comme.asia',
                    ];
                    window.LadiPageScript.runtime.DOMAIN_FREE = [
                        'preview.ldpdemo.com',
                        'ldp.page',
                    ];
                    window.LadiPageScript.runtime.bodyFontSize = 12;
                    window.LadiPageScript.runtime.store_id = '';
                    window.LadiPageScript.runtime.store_ladiuid =
                        '57e4d138957904ae180e0544';
                    window.LadiPageScript.runtime.time_zone = 7;
                    window.LadiPageScript.runtime.currency = 'VND';
                    window.LadiPageScript.runtime.convert_replace_str = true;
                    window.LadiPageScript.runtime.desktop_width = 1200;
                    window.LadiPageScript.runtime.mobile_width = 420;
                    window.LadiPageScript.runtime.formdata = true;
                    window.LadiPageScript.runtime.tracking_button_click = true;
                    window.LadiPageScript.runtime.publish_time = 1757904212199;
                    window.LadiPageScript.runtime.lang = 'vi';
                    window.LadiPageScript.run(true);
                    window.LadiPageScript.runEventScroll();
                };
                run();
            })();
        </script>
    </body>
</html>
<!--Publish time: Mon, 15 Sep 2025 02:43:32 GMT-->
<!--LadiPage build time: Fri, 12 Sep 2025 10:33:15 GMT-->
