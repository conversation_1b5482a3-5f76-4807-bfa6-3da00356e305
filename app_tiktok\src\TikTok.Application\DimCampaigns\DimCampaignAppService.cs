using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Authorization.Permissions;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.FactGmvMaxCampaigns;
using Volo.Abp.Authorization;

namespace TikTok.DimCampaigns
{
    /// <summary>
    /// Application service for DimCampaign operations
    /// </summary>
    public class DimCampaignAppService : ApplicationService, IDimCampaignAppService
    {
        private readonly IRepository<DimCampaignEntity, Guid> _dimCampaignRepository;
        private readonly IPermissionChecker _permissionChecker;
        private readonly PermissionFieldHelper _permissionFieldHelper;

        public DimCampaignAppService(
            IRepository<DimCampaignEntity, Guid> dimCampaignRepository,
            IPermissionChecker permissionChecker,
            PermissionFieldHelper permissionFieldHelper)
        {
            _dimCampaignRepository = dimCampaignRepository;
            _permissionChecker = permissionChecker;
            _permissionFieldHelper = permissionFieldHelper;
        }

        /// <summary>
        /// Get all active campaigns for dropdown/multiselect
        /// </summary>
        public async Task<List<DimCampaignDto>> GetActiveCampaignsAsync()
        {
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new Volo.Abp.Authorization.AbpAuthorizationException("Bạn không có quyền truy cập danh sách campaign");
            }

            var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);
            
            var query = await _dimCampaignRepository.GetQueryableAsync();
            
            var campaignQuery = query.Where(c => c.OperationStatus == "ENABLE" && c.IsCurrent == true); // Current and active records only
            
            // ✅ SECURITY FIX: Filter by user's allowed advertiser IDs
            if (allowedAdvertiserIds != null && allowedAdvertiserIds.Any())
            {
                campaignQuery = campaignQuery.Where(c => allowedAdvertiserIds.Contains(c.AdvertiserId));
            }
            
            var activeCampaigns = campaignQuery.OrderBy(c => c.CampaignName)
                .Select(c => new DimCampaignDto
                {
                    Id = c.Id,
                    CampaignId = c.CampaignId,
                    DimAdAccountId = c.DimAdAccountId,
                    AdvertiserId = c.AdvertiserId,
                    CampaignName = c.CampaignName,
                    ObjectiveType = c.ObjectiveType,
                    CampaignType = c.CampaignType,
                    BudgetMode = c.BudgetMode,
                    Budget = c.Budget,
                    OperationStatus = c.OperationStatus,
                    CreateTime = c.CreateTime,
                    ModifyTime = c.ModifyTime,
                    IsCurrent = c.IsCurrent,
                    EffectiveDate = c.EffectiveDate,
                    ExpirationDate = c.ExpirationDate,
                    RowVersion = c.RowVersion
                })
                .ToList();

            return activeCampaigns;
        }

        /// <summary>
        /// Get campaigns by type
        /// </summary>
        public async Task<List<DimCampaignDto>> GetCampaignsByTypeAsync(string campaignType)
        {
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new Volo.Abp.Authorization.AbpAuthorizationException("Bạn không có quyền truy cập danh sách campaign theo loại");
            }

            if (string.IsNullOrWhiteSpace(campaignType))
            {
                throw new ArgumentException("Campaign type is required");
            }

            var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);
            
            var query = await _dimCampaignRepository.GetQueryableAsync();
            
            var campaignQuery = query.Where(c => c.OperationStatus == "ENABLE" && 
                           c.IsCurrent == true && 
                           c.CampaignType == campaignType);
            
            // ✅ SECURITY FIX: Filter by user's allowed advertiser IDs
            if (allowedAdvertiserIds != null && allowedAdvertiserIds.Any())
            {
                campaignQuery = campaignQuery.Where(c => allowedAdvertiserIds.Contains(c.AdvertiserId));
            }
            
            var campaigns = campaignQuery.OrderBy(c => c.CampaignName)
                .Select(c => new DimCampaignDto
                {
                    Id = c.Id,
                    CampaignId = c.CampaignId,
                    DimAdAccountId = c.DimAdAccountId,
                    AdvertiserId = c.AdvertiserId,
                    CampaignName = c.CampaignName,
                    ObjectiveType = c.ObjectiveType,
                    CampaignType = c.CampaignType,
                    BudgetMode = c.BudgetMode,
                    Budget = c.Budget,
                    OperationStatus = c.OperationStatus,
                    CreateTime = c.CreateTime,
                    ModifyTime = c.ModifyTime,
                    IsCurrent = c.IsCurrent,
                    EffectiveDate = c.EffectiveDate,
                    ExpirationDate = c.ExpirationDate,
                    RowVersion = c.RowVersion
                })
                .ToList();

            return campaigns;
        }

        /// <summary>
        /// Get campaigns by advertiser
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>List of campaigns for specified advertiser</returns>
        public async Task<List<DimCampaignDto>> GetCampaignsByAdvertiserAsync(string advertiserId)
        {
            // Check permissions - requires ViewAll or ViewAllAdvertisers for advertiser-specific data
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập danh sách campaign theo advertiser");
            }

            if (string.IsNullOrWhiteSpace(advertiserId))
            {
                throw new ArgumentException("Advertiser ID is required");
            }

            // Get user's allowed advertiser IDs for additional validation
            var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);
            
            // SECURITY CHECK: Validate user has access to this specific advertiser
            if (allowedAdvertiserIds != null && !allowedAdvertiserIds.Contains(advertiserId))
            {
                throw new Volo.Abp.Authorization.AbpAuthorizationException("Bạn không có quyền truy cập campaign của advertiser này");
            }

            var query = await _dimCampaignRepository.GetQueryableAsync();
            
            var campaigns = query
                .Where(c => c.OperationStatus == "ENABLE" && 
                           c.IsCurrent == true && 
                           c.AdvertiserId == advertiserId)
                .OrderBy(c => c.CampaignName)
                .Select(c => new DimCampaignDto
                {
                    Id = c.Id,
                    CampaignId = c.CampaignId,
                    DimAdAccountId = c.DimAdAccountId,
                    AdvertiserId = c.AdvertiserId,
                    CampaignName = c.CampaignName,
                    ObjectiveType = c.ObjectiveType,
                    CampaignType = c.CampaignType,
                    BudgetMode = c.BudgetMode,
                    Budget = c.Budget,
                    OperationStatus = c.OperationStatus,
                    CreateTime = c.CreateTime,
                    ModifyTime = c.ModifyTime,
                    IsCurrent = c.IsCurrent,
                    EffectiveDate = c.EffectiveDate,
                    ExpirationDate = c.ExpirationDate,
                    RowVersion = c.RowVersion
                })
                .ToList();

            return campaigns;
        }
    }
}
