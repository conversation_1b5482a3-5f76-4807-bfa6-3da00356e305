﻿namespace TikTok.Web.Menus;

public class TikTokMenus
{
    private const string Prefix = "TikTok";
    public const string Home = Prefix + ".Home";
    public const string BusinessCenters = Prefix + ".BusinessCenters";
    public const string BalanceBusinessCenters = Prefix + ".BalanceBusinessCenters";
    public const string AdAccounts = Prefix + ".AdAccounts";
    public const string Assets = Prefix + ".Assets";
    public const string Transactions = Prefix + ".Transactions";
    public const string BusinessApplications = Prefix + ".BusinessApplications";
    public const string BalanceAdAccounts = Prefix + ".BalanceAdAccounts";
    public const string RecordTransactionAdAccounts = Prefix + ".RecordTransactionAdAccounts";
    public const string RecordTransactionBcs = Prefix + ".RecordTransactionBcs";
    public const string JobManagement = Prefix + ".JobManagement";
    public const string JobManagementDashboard = Prefix + ".JobManagement.Dashboard";
    public const string Campaigns = Prefix + ".Campaigns";
    public const string CostProfiles = Prefix + ".CostProfiles";
    public const string ReportIntegratedBcs = Prefix + ".ReportIntegratedBcs";
    public const string Customers = Prefix + ".Customers";
    public const string SupportManagement = Prefix + ".SupportManagement";
    public const string UserAccessManagement = Prefix + ".UserAccessManagement";
    public const string FactGmvMaxCampaign = Prefix + ".FactGmvMaxCampaign";
    public const string FactGmvMaxProduct = Prefix + ".FactGmvMaxProduct";
    public const string GmvMax = Prefix + ".GmvMax";

}
