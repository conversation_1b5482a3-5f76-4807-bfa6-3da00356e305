/**
 * 🎯 Summary Section - Đơn gi<PERSON>n
 * Tự call API + tự render
 */

class SummarySection {
    constructor() {
        this.container = '#dashboard-summary-cards-container';
        this.initialized = false;
    }

    /**
     * 🎯 Khởi tạo section
     */
    async init() {
        if (this.initialized) {
            return;
        }

        try {
            await this.loadData();
            this.initialized = true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 🎯 Load data từ API
     */
    async loadData() {
        try {
            // Show loading
            this.showLoading();

            // Get currency and call API
            const currency = localStorage.getItem('tiktok_currency') || 'USD';
            const response = await fetch(
                `/api/fact-gmv-max-product/summary-cards?currency=${currency}`
            );
            if (!response.ok) {
                throw new Error(
                    `HTTP ${response.status}: ${response.statusText}`
                );
            }

            const data = await response.json();

            // Render data
            await this.render(data);
        } catch (error) {
            this.showError(error);
            throw error;
        }
    }

    /**
     * 🎯 Render summary cards
     */
    async render(data) {
        const container = document.querySelector(this.container);
        if (!container) {
            return;
        }

        const summary = data.summaryCards || data;

        // Get currency from localStorage or default to USD
        const currency =
            (typeof localStorage !== 'undefined'
                ? localStorage.getItem('tiktok_currency')
                : null) || 'USD';

        const formatCurrency = (value) => {
            if (
                window.sharedCurrencyManager &&
                window.sharedCurrencyManager.formatCurrency
            ) {
                return window.sharedCurrencyManager.formatCurrency(
                    value || 0,
                    currency
                );
            }
            if (!value) return currency === 'VND' ? '₫0' : '$0';
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: currency,
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
            }).format(value);
        };

        const formatNumber = (value) => {
            if (!value) return '0';
            return new Intl.NumberFormat('vi-VN').format(value);
        };

        const formatROI = (value) => {
            if (!value) return '0x';
            return `${value.toFixed(2)}x`;
        };

        const formatTACOS = (value) => {
            if (!value) return '0%';
            return `${(value * 100).toFixed(1)}%`;
        };

        container.innerHTML = `
            <div class="dashboard-summary-card revenue">
                <div class="card-icon"><i class="fas fa-dollar-sign"></i></div>
                <div class="card-title">Tổng doanh thu</div>
                <div class="card-value">${formatCurrency(
                    summary.totalGrossRevenue || 0
                )}</div>
            </div>
            <div class="dashboard-summary-card total-spent">
                <div class="card-icon"><i class="fas fa-credit-card"></i></div>
                <div class="card-title">Tổng chi phí</div>
                <div class="card-value">${formatCurrency(
                    summary.totalCost || 0
                )}</div>
            </div>
            <div class="dashboard-summary-card roi">
                <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                <div class="card-title">ROI trung bình</div>
                <div class="card-value">${formatROI(
                    summary.averageROAS || 0
                )}</div>
            </div>
            <div class="dashboard-summary-card campaigns">
                <div class="card-icon"><i class="fas fa-percentage"></i></div>
                <div class="card-title">TACOS trung bình</div>
                <div class="card-value">${formatTACOS(
                    summary.averageTACOS || 0
                )}</div>
            </div>
            <div class="dashboard-summary-card orders">
                <div class="card-icon"><i class="fas fa-shopping-cart"></i></div>
                <div class="card-title">Tổng đơn hàng</div>
                <div class="card-value">${formatNumber(
                    summary.totalOrders || 0
                )}</div>
            </div>
            <div class="dashboard-summary-card spent-estimate">
                <div class="card-icon"><i class="fas fa-box"></i></div>
                <div class="card-title">Sản phẩm hoạt động</div>
                <div class="card-value">${formatNumber(
                    summary.activeProducts || summary.campaignCount || 0
                )}</div>
            </div>
        `;
    }

    /**
     * 🎯 Show loading indicator
     */
    showLoading() {
        const container = document.querySelector(this.container);
        if (container) {
            container.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải dữ liệu tổng hợp...</p>
                </div>
            `;
        }
    }

    /**
     * 🎯 Show error message
     */
    showError(error) {
        const container = document.querySelector(this.container);
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Lỗi tải Summary</h6>
                    <p class="mb-0">${error.message}</p>
                </div>
            `;
        }
    }

    /**
     * 🎯 Format currency
     */
    formatCurrency(amount) {
        if (!amount) return '$0';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    }

    /**
     * 🎯 Format number
     */
    formatNumber(number) {
        return new Intl.NumberFormat('vi-VN').format(number);
    }

    /**
     * 🎯 Refresh data
     */
    async refresh() {
        await this.loadData();
    }
}

// Export for global use
window.SummarySection = SummarySection;
