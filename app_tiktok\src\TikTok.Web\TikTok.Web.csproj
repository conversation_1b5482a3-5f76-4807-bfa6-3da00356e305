﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>TikTok.Web</RootNamespace>
		<AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
		<AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
		<GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
		<GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
		<MvcRazorExcludeRefAssembliesFromPublish>false</MvcRazorExcludeRefAssembliesFromPublish>
		<PreserveCompilationReferences>true</PreserveCompilationReferences>
		<UserSecretsId>TikTok-4681b4fd-151f-4221-84a4-929d86723e4c</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Logs\**" />
		<Content Remove="Logs\**" />
		<EmbeddedResource Remove="Logs\**" />
		<None Remove="Logs\**" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Tiktok.WDfs\**\*" />
	</ItemGroup>

	<ItemGroup>
		<None Update="Pages\**\*.js">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Pages\**\*.css">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup Condition="Exists('./openiddict.pfx')">
		<None Remove="openiddict.pfx" />
		<EmbeddedResource Include="openiddict.pfx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
		<PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="3.1.*-*" />
	</ItemGroup> 

	<ItemGroup>
		<ProjectReference Include="..\TikTok.Activities\TikTok.Activities.csproj" />
		<ProjectReference Include="..\TikTok.Application\TikTok.Application.csproj" />
		<ProjectReference Include="..\TikTok.HttpApi\TikTok.HttpApi.csproj" />
		<ProjectReference Include="..\TikTok.EntityFrameworkCore\TikTok.EntityFrameworkCore.csproj" />
		<PackageReference Include="Hangfire.SqlServer" Version="1.8.6" />
		<PackageReference Include="Volo.Abp.Autofac" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Swashbuckle" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Identity.Web" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.Web" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Web" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.HangFire" Version="8.1.4" />
		<PackageReference Include="Elsa.Designer.Components.Web" Version="2.14.1" />
		<PackageReference Include="Tsp.Custom.ElsaWorkflow" Version="1.0.10" />
		<PackageReference Include="Tsp.Zalo.Web" Version="1.0.0-prerelease-5916" />
		<PackageReference Include="Tsp.Module.Notifications.Web" Version="1.0.18-prerelease-6047" />
	</ItemGroup>

</Project>
