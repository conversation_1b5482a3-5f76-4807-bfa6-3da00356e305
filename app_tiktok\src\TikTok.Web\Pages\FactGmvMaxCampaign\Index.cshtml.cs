using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using TikTok.FactGmvMaxCampaigns;
using TikTok.FactGmvMaxCampaigns.Dtos;
using TikTok.Permissions;
using Volo.Abp.Authorization.Permissions;

namespace TikTok.Web.Pages.FactGmvMaxCampaign
{
    public class IndexModel : PageModel
    {
        private readonly IFactGmvMaxCampaignService _factGmvMaxCampaignService;
        private readonly ILogger<IndexModel> _logger;
        private readonly IPermissionChecker _permissionChecker;
        
        public GetFactGmvMaxCampaignDataResponse? Data { get; set; }
        public CampaignAlertThresholds CampaignAlertThresholds { get; set; }
        
        // ✅ Permission properties for UI visibility
        public bool HasViewSpending { get; set; }
        public bool HasViewMetrics { get; set; }
        public bool HasViewAll { get; set; }
        public bool HasViewAllAdvertisers { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? FromDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? ToDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? Type { get; set; }

        // Alternative parameter names for URL query
        [BindProperty(SupportsGet = true, Name = "from")]
        public DateTime? From { get; set; }

        [BindProperty(SupportsGet = true, Name = "to")]
        public DateTime? To { get; set; }

        public IndexModel(IFactGmvMaxCampaignService factGmvMaxCampaignService, ILogger<IndexModel> logger, IPermissionChecker permissionChecker)
        {
            _factGmvMaxCampaignService = factGmvMaxCampaignService;
            _logger = logger;
            _permissionChecker = permissionChecker;
            CampaignAlertThresholds = new CampaignAlertThresholds();
        }

        public async Task OnGet()
        {
            try
            {
                // ✅ Check permissions for UI visibility
                HasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                HasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                HasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                HasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // Get date parameters from URL query - support both naming conventions
                var startDateToDay = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                var endDateToDay = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);

                var from = FromDate ?? From ?? DateTime.Now.AddDays(-7);
                var to = ToDate ?? To ?? endDateToDay;
                this.From = from;
                this.To = to;

                // Set default type if not specified
                if (string.IsNullOrEmpty(Type))
                {
                    Type = "campaign"; // Default type for campaign analysis
                }

                // ✅ No server-side data loading - data will be fetched via API
                _logger.LogInformation("GMV Max Campaign page loaded with date range {From} to {To} and type {Type}", from, to, Type);
                
                // Initialize empty data - will be populated by JavaScript via API
                Data = new GetFactGmvMaxCampaignDataResponse();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing GMV Max Campaign page from {From} to {To}", From, To);
                ModelState.AddModelError("", $"Error initializing GMV Max Campaign page: {ex.Message}");
                
                // Fallback to empty response  
                Data = new GetFactGmvMaxCampaignDataResponse();
            }
        }


    }

    public class CampaignAlertThresholds
    {
        public string Currency { get; set; } = "USD";
        // Note: Only USD currency is supported
        public decimal RoasCritical { get; set; } = 1.5m; // ROI < 1.5
        public decimal RoasLow { get; set; } = 2.0m; // ROI < 2.0
        public decimal RoasGood { get; set; } = 3.0m; // ROI > 3.0
        public decimal TacosHigh { get; set; } = 30m; // TACOS > 30%
        public decimal TacosMedium { get; set; } = 20m; // TACOS > 20%
        public decimal BudgetUtilizationHigh { get; set; } = 80m; // Budget > 80%
        public decimal BudgetUtilizationMedium { get; set; } = 60m; // Budget > 60%
    }
}
