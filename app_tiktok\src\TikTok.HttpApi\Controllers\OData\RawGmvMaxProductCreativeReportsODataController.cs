using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;

namespace TikTok.HttpApi.Controllers.OData
{
    [Authorize]
    public class RawGmvMaxProductCreativeReportsController : ODataController
    {
        private readonly IRepository<RawGmvMaxProductCreativeReportEntity, System.Guid> _repository;

        public RawGmvMaxProductCreativeReportsController(
            IRepository<RawGmvMaxProductCreativeReportEntity, System.Guid> repository)
        {
            _repository = repository;
        }

        [EnableQuery]
        public async Task<IQueryable<RawGmvMaxProductCreativeReportEntity>> Get()
        {
            return await _repository.GetQueryableAsync();
        }
    }
}


