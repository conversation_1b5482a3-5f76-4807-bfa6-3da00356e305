using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.Assets;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp;

namespace TikTok.Assets
{
    /// <summary>
    /// Service implementation cho Asset
    /// </summary>
    public class AssetAppService :
        CrudAppService<
            RawAssetEntity,
            AssetDto,
            Guid,
            GetAssetListDto,
            CreateAssetDto,
            UpdateAssetDto>,
        IAssetAppService
    {
        private readonly IAssetRepository _assetRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="assetRepository">Asset Repository</param>
        public AssetAppService(
            IRepository<RawAssetEntity, Guid> repository,
            IAssetRepository assetRepository) : base(repository)
        {
            _assetRepository = assetRepository;
            CreatePolicyName = TikTokPermissions.Assets.Create;
            UpdatePolicyName = TikTokPermissions.Assets.Edit;
            DeletePolicyName = TikTokPermissions.Assets.Delete;
            GetPolicyName = TikTokPermissions.Assets.Default;
            GetListPolicyName = TikTokPermissions.Assets.Default;
        }

        /// <summary>
        /// Lấy tài sản theo Asset ID
        /// </summary>
        /// <param name="assetId">ID của tài sản</param>
        /// <returns>Tài sản</returns>
        [Authorize(TikTokPermissions.Assets.Default)]
        public async Task<AssetDto> GetByAssetIdAsync(string assetId)
        {
            var entity = await _assetRepository.GetByAssetIdAsync(assetId);
            return ObjectMapper.Map<RawAssetEntity, AssetDto>(entity);
        }

        /// <summary>
        /// Lấy danh sách tài sản theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách tài sản</returns>
        [Authorize(TikTokPermissions.Assets.Default)]
        public async Task<PagedResultDto<AssetDto>> GetByBcIdAsync(string bcId)
        {
            var entities = await _assetRepository.GetByBcIdAsync(bcId);
            var dtos = ObjectMapper.Map<RawAssetEntity[], AssetDto[]>(entities.ToArray());

            return new PagedResultDto<AssetDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách tài sản</returns>
        public override async Task<PagedResultDto<AssetDto>> GetListAsync(GetAssetListDto input)
        {
            // Sử dụng repository để lấy danh sách và tổng số
            var entities = await _assetRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.Filter,
                assetId: input.AssetId,
                assetName: input.AssetName,
                assetType: input.AssetType,
                bcId: input.BcId,
                advertiserAccountType: input.AdvertiserAccountType,
                advertiserRole: input.AdvertiserRole,
                catalogRole: input.CatalogRole,
                adCreationEligible: input.AdCreationEligible,
                storeRole: input.StoreRole,
                ownerBcName: input.OwnerBcName,
                isRemoved: input.IsRemoved,
                removedAt: input.RemovedAt,
                relationType: input.RelationType,
                relationStatus: input.RelationStatus,
                advertiserStatus: input.AdvertiserStatus);

            var totalCount = await _assetRepository.GetCountAsync(
                filter: input.Filter,
                assetId: input.AssetId,
                assetName: input.AssetName,
                assetType: input.AssetType,
                bcId: input.BcId,
                advertiserAccountType: input.AdvertiserAccountType,
                advertiserRole: input.AdvertiserRole,
                catalogRole: input.CatalogRole,
                adCreationEligible: input.AdCreationEligible,
                storeRole: input.StoreRole,
                ownerBcName: input.OwnerBcName,
                isRemoved: input.IsRemoved,
                removedAt: input.RemovedAt,
                relationType: input.RelationType,
                relationStatus: input.RelationStatus,
                advertiserStatus: input.AdvertiserStatus);

            var dtos = ObjectMapper.Map<RawAssetEntity[], AssetDto[]>(entities.ToArray());

            return new PagedResultDto<AssetDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức CreateAsync để thêm validation
        /// </summary>
        /// <param name="input">Input để tạo mới</param>
        /// <returns>Tài sản đã tạo</returns>
        [Authorize(TikTokPermissions.Assets.Create)]
        public override async Task<AssetDto> CreateAsync(CreateAssetDto input)
        {
            // Kiểm tra xem Asset ID đã tồn tại chưa
            if (await _assetRepository.IsAssetIdExistsAsync(input.AssetId))
            {
                throw new UserFriendlyException($"Asset ID '{input.AssetId}' đã tồn tại.");
            }

            return await base.CreateAsync(input);
        }
    }
}