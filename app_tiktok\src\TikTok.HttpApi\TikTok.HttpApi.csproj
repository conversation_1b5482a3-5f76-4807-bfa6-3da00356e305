﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>TikTok</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\TikTok.Application.Contracts\TikTok.Application.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Volo.Abp.Account.HttpApi" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Identity.HttpApi" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.HttpApi" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="8.1.4" />
		<PackageReference Include="Tsp.Zalo.HttpApi" Version="1.0.0-prerelease-5916" />
    <PackageReference Include="Tsp.Module.Notifications.HttpApi" Version="1.0.18-prerelease-6047" />
    <PackageReference Include="Microsoft.AspNetCore.OData" Version="8.2.5" />
	</ItemGroup>

</Project>
