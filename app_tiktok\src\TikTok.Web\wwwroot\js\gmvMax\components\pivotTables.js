/**
 * Pivot Tables Component
 * Handles Syncfusion Pivot Table creation and management
 * Copy configurations from existing FactGmvMaxProduct and FactGmvMaxCampaign
 */

class PivotTablesComponent {
    constructor() {
        this.productPivot = null;
        this.campaignPivot = null;
    }

    /**
     * Create Product Pivot Table (copy from FactGmvMaxProduct)
     */
    createProductPivot(data, containerId) {
        try {
            // Destroy existing pivot if any
            if (this.productPivot) {
                this.productPivot.destroy();
            }

            // Syncfusion Pivot configuration (copied from FactGmvMaxProduct)
            this.productPivot = new ej.pivotview.PivotView({
                dataSourceSettings: {
                    dataSource: data,
                    expandAll: false,
                    enableSorting: true,
                    sortSettings: [{ name: 'Date', order: 'Descending' }],
                    rows: [
                        { name: 'StoreName', caption: 'Shop' },
                        { name: 'ProductName', caption: 'Sản phẩm' },
                    ],
                    columns: [{ name: 'Date', caption: 'Ngày' }],
                    values: [
                        {
                            name: 'Cost',
                            caption: 'Chi phí ($)',
                            type: 'Sum',
                            format: 'C2',
                        },
                        {
                            name: 'GrossRevenue',
                            caption: 'Doanh thu ($)',
                            type: 'Sum',
                            format: 'C2',
                        },
                        {
                            name: 'Orders',
                            caption: 'Đơn hàng',
                            type: 'Sum',
                            format: 'N0',
                        },
                        {
                            name: 'ROAS',
                            caption: 'ROAS',
                            type: 'Avg',
                            format: 'N2',
                        },
                        {
                            name: 'TACOS',
                            caption: 'TACOS (%)',
                            type: 'Avg',
                            format: 'P2',
                        },
                    ],
                    filters: [
                        { name: 'CreativeType', caption: 'Loại Creative' },
                    ],
                },
                width: '100%',
                height: 500,
                showFieldList: true,
                showGroupingBar: true,
                allowExcelExport: true,
                allowPdfExport: true,
                allowCalculatedField: true,
                showToolbar: true,
                toolbar: [
                    'New',
                    'Save',
                    'SaveAs',
                    'Rename',
                    'Remove',
                    'Load',
                    'Grid',
                    'Chart',
                    'Export',
                    'SubTotal',
                    'GrandTotal',
                    'FieldList',
                ],
                gridSettings: {
                    columnWidth: 120,
                    allowSelection: true,
                    selectionSettings: { mode: 'Cell', type: 'Single' },
                },
                chartSettings: {
                    value: 'Cost',
                    enableExport: true,
                    chartSeries: {
                        type: 'Column',
                        animation: { enable: false },
                    },
                    enableMultipleAxis: false,
                },
                // Conditional formatting for ROAS
                conditionalFormatSettings: [
                    {
                        measure: 'ROAS',
                        value1: 3,
                        conditions: 'GreaterThan',
                        style: {
                            backgroundColor: '#d4edda',
                            color: '#155724',
                            fontFamily: 'Tahoma',
                            fontSize: '12px',
                        },
                    },
                    {
                        measure: 'ROAS',
                        value1: 1.5,
                        conditions: 'LessThan',
                        style: {
                            backgroundColor: '#f8d7da',
                            color: '#721c24',
                            fontFamily: 'Tahoma',
                            fontSize: '12px',
                        },
                    },
                ],
                locale: 'vi-VN',
            });

            this.productPivot.appendTo(`#${containerId}`);

            return this.productPivot;
        } catch (error) {
            console.error('❌ Error creating Product Pivot Table:', error);
            throw error;
        }
    }

    /**
     * Create Campaign Pivot Table (copy from FactGmvMaxCampaign with LIVE filter)
     */
    createCampaignPivot(data, containerId) {
        try {
            // Destroy existing pivot if any
            if (this.campaignPivot) {
                this.campaignPivot.destroy();
            }

            // Syncfusion Pivot configuration (copied from FactGmvMaxCampaign)
            this.campaignPivot = new ej.pivotview.PivotView({
                dataSourceSettings: {
                    dataSource: data,
                    expandAll: false,
                    enableSorting: true,
                    sortSettings: [{ name: 'Date', order: 'Descending' }],
                    rows: [
                        { name: 'StoreName', caption: 'Shop' },
                        { name: 'CampaignName', caption: 'Chiến dịch' },
                    ],
                    columns: [{ name: 'Date', caption: 'Ngày' }],
                    values: [
                        {
                            name: 'Cost',
                            caption: 'Chi phí ($)',
                            type: 'Sum',
                            format: 'C2',
                        },
                        {
                            name: 'GrossRevenue',
                            caption: 'Doanh thu ($)',
                            type: 'Sum',
                            format: 'C2',
                        },
                        {
                            name: 'Orders',
                            caption: 'Đơn hàng',
                            type: 'Sum',
                            format: 'N0',
                        },
                        {
                            name: 'ROAS',
                            caption: 'ROAS',
                            type: 'Avg',
                            format: 'N2',
                        },
                        {
                            name: 'TACOS',
                            caption: 'TACOS (%)',
                            type: 'Avg',
                            format: 'P2',
                        },
                        {
                            name: 'LiveViews',
                            caption: 'Lượt xem Live',
                            type: 'Sum',
                            format: 'N0',
                        },
                        {
                            name: 'LiveFollows',
                            caption: 'Lượt follow',
                            type: 'Sum',
                            format: 'N0',
                        },
                    ],
                    filters: [
                        { name: 'TtAccountName', caption: 'TikTok Account' },
                    ],
                },
                width: '100%',
                height: 500,
                showFieldList: true,
                showGroupingBar: true,
                allowExcelExport: true,
                allowPdfExport: true,
                allowCalculatedField: true,
                showToolbar: true,
                toolbar: [
                    'New',
                    'Save',
                    'SaveAs',
                    'Rename',
                    'Remove',
                    'Load',
                    'Grid',
                    'Chart',
                    'Export',
                    'SubTotal',
                    'GrandTotal',
                    'FieldList',
                ],
                gridSettings: {
                    columnWidth: 120,
                    allowSelection: true,
                    selectionSettings: { mode: 'Cell', type: 'Single' },
                },
                chartSettings: {
                    value: 'Cost',
                    enableExport: true,
                    chartSeries: {
                        type: 'Column',
                        animation: { enable: false },
                    },
                    enableMultipleAxis: false,
                },
                // Conditional formatting for ROAS
                conditionalFormatSettings: [
                    {
                        measure: 'ROAS',
                        value1: 3,
                        conditions: 'GreaterThan',
                        style: {
                            backgroundColor: '#d4edda',
                            color: '#155724',
                            fontFamily: 'Tahoma',
                            fontSize: '12px',
                        },
                    },
                    {
                        measure: 'ROAS',
                        value1: 1.5,
                        conditions: 'LessThan',
                        style: {
                            backgroundColor: '#f8d7da',
                            color: '#721c24',
                            fontFamily: 'Tahoma',
                            fontSize: '12px',
                        },
                    },
                ],
                locale: 'vi-VN',
            });

            this.campaignPivot.appendTo(`#${containerId}`);

            return this.campaignPivot;
        } catch (error) {
            console.error('❌ Error creating Campaign Pivot Table:', error);
            throw error;
        }
    }

    /**
     * Refresh pivot tables
     */
    refresh() {
        if (this.productPivot) {
            this.productPivot.refresh();
        }
        if (this.campaignPivot) {
            this.campaignPivot.refresh();
        }
    }

    /**
     * Export pivot to Excel
     */
    exportToExcel(pivotType = 'product') {
        try {
            const pivot =
                pivotType === 'product'
                    ? this.productPivot
                    : this.campaignPivot;
            if (pivot) {
                pivot.excelExport();
            }
        } catch (error) {
            console.error('❌ Error exporting to Excel:', error);
        }
    }

    /**
     * Export pivot to PDF
     */
    exportToPdf(pivotType = 'product') {
        try {
            const pivot =
                pivotType === 'product'
                    ? this.productPivot
                    : this.campaignPivot;
            if (pivot) {
                pivot.pdfExport();
            }
        } catch (error) {
            console.error('❌ Error exporting to PDF:', error);
        }
    }

    /**
     * Update pivot data
     */
    updateData(pivotType, newData) {
        try {
            const pivot =
                pivotType === 'product'
                    ? this.productPivot
                    : this.campaignPivot;
            if (pivot) {
                pivot.dataSourceSettings.dataSource = newData;
                pivot.refresh();
            }
        } catch (error) {
            console.error('❌ Error updating pivot data:', error);
        }
    }

    /**
     * Destroy pivot tables
     */
    destroy() {
        if (this.productPivot) {
            this.productPivot.destroy();
            this.productPivot = null;
        }
        if (this.campaignPivot) {
            this.campaignPivot.destroy();
            this.campaignPivot = null;
        }

        console.log('🧹 Pivot Tables component destroyed');
    }
}
