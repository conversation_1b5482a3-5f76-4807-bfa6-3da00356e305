/**
 * ROI Cards Component
 * Handles ROI analysis cards display and interactions
 */

class RoiCardsComponent {
    constructor() {
        this.currentData = null;
    }

    /**
     * Render ROI cards
     */
    async render(roiAnalysis, containerId) {
        try {
            this.currentData = roiAnalysis;
            const container = document.getElementById(containerId);

            if (!container) {
                throw new Error(`Container ${containerId} not found`);
            }

            // Generate ROI cards HTML
            const cardsHtml = this.generateCardsHtml(roiAnalysis);
            container.innerHTML = cardsHtml;

            // Setup card click handlers
            this.setupCardClickHandlers();
        } catch (error) {
            console.error('❌ Error rendering ROI cards:', error);
            throw error;
        }
    }

    /**
     * Generate ROI cards HTML with new layout: Row 1 (Summary metrics), Row 2 (ROI analysis)
     */
    generateCardsHtml(roiAnalysis, campaignType = '') {
        const { goodRoi, poorRoi, summary } = roiAnalysis;

        // Calculate summary metrics from the data
        const totalCost = summary.totalCost || 0;
        const totalRevenue = summary.totalRevenue || 0;
        const totalCampaigns = summary.total || 0;

        return `
            <!-- Single Row: 5 Cards (3 Summary + 2 ROI) -->
            <div id="summary-cards-container" style="margin-bottom: 1rem;">
                <!-- Summary Cards -->
                <div class="dashboard-summary-card total-spent">
                    <div class="card-icon"><i class="fas fa-credit-card"></i></div>
                    <div class="card-title">Tổng chi phí</div>
                    <div class="card-value">${this.formatCurrency(
                        totalCost
                    )}</div>
                </div>
                <div class="dashboard-summary-card revenue">
                    <div class="card-icon"><i class="fas fa-dollar-sign"></i></div>
                    <div class="card-title">Tổng doanh thu</div>
                    <div class="card-value">${this.formatCurrency(
                        totalRevenue
                    )}</div>
                </div>
                <div class="dashboard-summary-card campaigns">
                    <div class="card-icon"><i class="fas fa-bullhorn"></i></div>
                    <div class="card-title">Tổng Campaign</div>
                    <div class="card-value">${totalCampaigns}</div>
                </div>
                
                <!-- ROI Cards (Using same dashboard-summary-card style) -->
                <div class="dashboard-summary-card roi-good clickable-card" data-roi-type="good" data-shopping-ads-type="${
                    campaignType || 'UNKNOWN'
                }">
                    <div class="card-icon">
                        <i class="fas fa-arrow-up text-success"></i>
                        <i class="fas fa-eye roi-detail-icon" title="Click để xem chi tiết"></i>
                    </div>
                    <div class="card-title">Campaign ROI tốt</div>
                    <div class="card-value">${goodRoi.count}</div>
                    <div class="card-subtitle">${summary.goodPercentage}%</div>
                </div>
                <div class="dashboard-summary-card roi-poor clickable-card" data-roi-type="poor" data-shopping-ads-type="${
                    campaignType || 'UNKNOWN'
                }">
                    <div class="card-icon">
                        <i class="fas fa-arrow-down text-danger"></i>
                        <i class="fas fa-eye roi-detail-icon" title="Click để xem chi tiết"></i>
                    </div>
                    <div class="card-title">Campaign ROI kém</div>
                    <div class="card-value">${poorRoi.count}</div>
                    <div class="card-subtitle">${summary.poorPercentage}%</div>
                </div>
            </div>
        `;
    }

    /**
     * Render ROI cards to specific container (for Product/Live sections)
     */
    renderToContainer(containerId, roiData, campaignType = '') {
        try {
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`❌ Container ${containerId} not found`);
                return;
            }

            // Store data for modal access
            this.currentData = roiData;

            // Generate and insert HTML
            const html = this.generateCardsHtml(roiData, campaignType);
            container.innerHTML = html;

            // Setup event handlers
            this.setupCardClickHandlers();
        } catch (error) {
            console.error(
                `❌ Error rendering ${campaignType} ROI cards:`,
                error
            );

            // Show error state
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="col-12 text-center p-4">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Lỗi tải dữ liệu ROI ${campaignType}
                        </div>
                    </div>
                `;
            }
        }
    }

    /**
     * Setup card click handlers
     */
    setupCardClickHandlers() {
        // Remove existing event listeners to prevent duplicates
        const clickableCards = document.querySelectorAll('.clickable-card');
        clickableCards.forEach((card) => {
            // Clone node to remove all event listeners
            const newCard = card.cloneNode(true);
            card.parentNode.replaceChild(newCard, card);
        });

        // ROI clickable card handlers (new dashboard-summary-card style)
        const freshClickableCards =
            document.querySelectorAll('.clickable-card');
        freshClickableCards.forEach((card) => {
            card.addEventListener('click', async (e) => {
                e.preventDefault();
                e.stopPropagation();

                const roiType = card.dataset.roiType;
                const shoppingAdsType = card.getAttribute(
                    'data-shopping-ads-type'
                );

                if (roiType) {
                    await this.showRoiDetails(roiType, shoppingAdsType);
                }
            });

            // Add cursor pointer style
            card.style.cursor = 'pointer';
        });

        // Legacy card click handlers (for backward compatibility)
        const roiCards = document.querySelectorAll('.roi-card');
        roiCards.forEach((card) => {
            card.addEventListener('click', async (e) => {
                if (!e.target.closest('.btn-details')) {
                    const roiType = card.dataset.roiType;
                    const shoppingAdsType = card.getAttribute(
                        'data-shopping-ads-type'
                    );
                    await this.showRoiDetails(roiType, shoppingAdsType);
                }
            });
        });

        // Detail button handlers (legacy)
        const detailButtons = document.querySelectorAll('.btn-details');
        detailButtons.forEach((button) => {
            button.addEventListener('click', async (e) => {
                e.stopPropagation();
                const roiType = button.dataset.roiType;
                const shoppingAdsType = button
                    .closest('[data-shopping-ads-type]')
                    ?.getAttribute('data-shopping-ads-type');
                await this.showRoiDetails(roiType, shoppingAdsType);
            });
        });
    }

    /**
     * Show ROI details in modal (fetch fresh data from API)
     */
    async showRoiDetails(roiType, shoppingAdsType = null) {
        try {
            const modalTitle =
                roiType === 'good'
                    ? 'Chi tiết Chiến dịch có ROI tốt'
                    : 'Chi tiết Chiến dịch có ROI kém';

            // Show loading in modal first
            const modal = document.getElementById('roiDetailsModal');
            const modalTitleElement = modal.querySelector('.modal-title');
            const modalBodyElement = modal.querySelector(
                '#roi-details-content'
            );

            if (modalTitleElement) {
                modalTitleElement.innerHTML = `<i class="fas fa-chart-line"></i> ${modalTitle}`;
            }

            if (modalBodyElement) {
                modalBodyElement.innerHTML = `
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Đang tải danh sách campaigns...</p>
                    </div>
                `;
            }

            // Show modal (reuse existing instance if available)
            let bsModal = bootstrap.Modal.getInstance(modal);
            if (!bsModal) {
                bsModal = new bootstrap.Modal(modal, {
                    backdrop: true,
                    keyboard: true,
                    focus: true,
                });
            }

            // Add cleanup event listener to prevent overlay issues
            modal.addEventListener(
                'hidden.bs.modal',
                function () {
                    // Remove any leftover backdrops
                    const backdrops =
                        document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach((backdrop) => backdrop.remove());

                    // Reset body classes
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                },
                { once: true }
            );

            bsModal.show();

            // Fetch fresh campaign details from API
            if (
                window.campaignTabManager &&
                window.campaignTabManager.dataAggregator
            ) {
                const campaignFilters =
                    window.campaignTabManager.currentFilters || {};

                // ✅ Add shoppingAdsType to filters for API call
                const filtersWithShoppingAdsType = {
                    ...campaignFilters,
                    shoppingAdsType: shoppingAdsType,
                };

                const data =
                    await window.campaignTabManager.dataAggregator.fetchRoiCampaignDetails(
                        filtersWithShoppingAdsType,
                        roiType
                    );

                // Generate modal content with fresh data
                const modalContent = this.generateModalContent(
                    data,
                    modalTitle
                );

                if (modalBodyElement) {
                    modalBodyElement.innerHTML = modalContent;
                }
            } else {
                // Fallback to current data if dataAggregator not available
                const data =
                    roiType === 'good'
                        ? this.currentData?.goodRoi
                        : this.currentData?.poorRoi;
                const modalContent = this.generateModalContent(
                    data || { items: [] },
                    modalTitle
                );

                if (modalBodyElement) {
                    modalBodyElement.innerHTML = modalContent;
                }
            }
        } catch (error) {
            console.error('❌ Error showing ROI details:', error);

            // Show error in modal
            const modalBodyElement = document.querySelector(
                '#roi-details-content'
            );
            if (modalBodyElement) {
                modalBodyElement.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Lỗi tải dữ liệu: ${error.message}
                    </div>
                `;
            }
        }
    }

    /**
     * Generate modal content HTML
     */
    generateModalContent(data, title) {
        if (!data.items || data.items.length === 0) {
            return `
                <div class="text-center p-5">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h5>Không có dữ liệu</h5>
                    <p class="text-muted">Không tìm thấy chiến dịch nào trong khoảng thời gian này.</p>
                </div>
            `;
        }

        const tableRows = data.items
            .map((item) => {
                const campaignName = item.campaignName || 'Unknown Campaign';
                const storeInfo =
                    item.storeName || item.storeId || 'Unknown Store';
                const roas = (item.roas || 0).toFixed(2);
                const cost = this.formatCurrencyLegacy(
                    item.cost || item.costUSD || 0
                );
                const revenue = this.formatCurrencyLegacy(
                    item.grossRevenue || item.grossRevenueUSD || 0
                );
                const orders = item.orders || 0;

                const roasClass =
                    item.roas >= 3.0
                        ? 'text-success'
                        : item.roas < 1.5
                        ? 'text-danger'
                        : 'text-warning';

                return `
                <tr>
                    <td>
                        <div class="fw-bold">${campaignName}</div>
                        <small class="text-muted">${storeInfo}</small>
                    </td>
                    <td class="${roasClass} fw-bold">${roas}x</td>
                    <td>${cost}</td>
                    <td>${revenue}</td>
                    <td>${orders}</td>
                </tr>
            `;
            })
            .join('');

        return `
            <div class="p-3">
                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-hover mb-0">
                        <thead class="sticky-top" style="background: #f8f9fa;">
                            <tr>
                                <th class="border-0 py-3">Chiến dịch</th>
                                <th class="border-0 py-3">ROI</th>
                                <th class="border-0 py-3">Chi phí</th>
                                <th class="border-0 py-3">Doanh thu</th>
                                <th class="border-0 py-3">Đơn hàng</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${tableRows}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * Format currency value for display (using sharedCurrencyManager like FactGmvMaxCampaign)
     */
    formatCurrency(value) {
        // Use sharedCurrencyManager if available (same as FactGmvMaxCampaign)
        if (
            window.sharedCurrencyManager &&
            window.sharedCurrencyManager.formatCurrency
        ) {
            const currency = this.getCurrentCurrency();
            return window.sharedCurrencyManager.formatCurrency(
                value || 0,
                currency
            );
        }

        // Fallback formatting
        const currency = this.getCurrentCurrency();
        if (!value || value === 0) return currency === 'VND' ? '₫0' : '$0';

        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    }

    /**
     * Get current currency from localStorage (same as FactGmvMaxCampaign)
     */
    getCurrentCurrency() {
        return (
            (typeof localStorage !== 'undefined'
                ? localStorage.getItem('tiktok_currency')
                : null) || 'USD'
        );
    }

    /**
     * Legacy format currency method for modal content
     */
    formatCurrencyLegacy(amount) {
        if (!amount || amount === 0) return '$0';

        if (amount >= 1000000) {
            return '$' + (amount / 1000000).toFixed(1) + 'M';
        } else if (amount >= 1000) {
            return '$' + (amount / 1000).toFixed(1) + 'K';
        } else {
            return '$' + amount.toFixed(2);
        }
    }

    /**
     * Update ROI cards with new data
     */
    update(roiAnalysis) {
        this.currentData = roiAnalysis;

        // Update card counts
        const goodRoiCard = document.querySelector(
            '[data-roi-type="good"] .roi-count'
        );
        const poorRoiCard = document.querySelector(
            '[data-roi-type="poor"] .roi-count'
        );

        if (goodRoiCard) {
            goodRoiCard.textContent = roiAnalysis.goodRoi.count;
        }
        if (poorRoiCard) {
            poorRoiCard.textContent = roiAnalysis.poorRoi.count;
        }

        // Update percentages
        const goodRoiPercentage = document.querySelector(
            '[data-roi-type="good"] .roi-percentage small'
        );
        const poorRoiPercentage = document.querySelector(
            '[data-roi-type="poor"] .roi-percentage small'
        );

        if (goodRoiPercentage) {
            goodRoiPercentage.textContent = `${roiAnalysis.summary.goodPercentage}% tổng chiến dịch`;
        }
        if (poorRoiPercentage) {
            poorRoiPercentage.textContent = `${roiAnalysis.summary.poorPercentage}% tổng chiến dịch`;
        }
    }

    /**
     * Destroy component
     */
    destroy() {
        this.currentData = null;

        // Clean up any modal instances
        const modal = document.getElementById('roiDetailsModal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.dispose();
            }

            // Remove any leftover backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach((backdrop) => backdrop.remove());

            // Reset body state
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    }

    /**
     * Force close modal and cleanup (utility method)
     */
    forceCloseModal() {
        try {
            const modal = document.getElementById('roiDetailsModal');
            if (modal) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }

                // Force cleanup after a short delay
                setTimeout(() => {
                    const backdrops =
                        document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach((backdrop) => backdrop.remove());

                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                }, 100);
            }
        } catch (error) {
            console.error('❌ Error force closing modal:', error);
        }
    }
}
