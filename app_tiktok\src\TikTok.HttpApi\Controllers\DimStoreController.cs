using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using TikTok.DimStores;
using TikTok.Permissions;

namespace TikTok.HttpApi.Controllers
{
    /// <summary>
    /// Controller for DimStore operations - Reusable across multiple features
    /// </summary>
    [Route("api/dim-stores")]
    [Authorize]
    public class DimStoreController : AbpControllerBase
    {
        private readonly IDimStoreAppService _dimStoreAppService;

        public DimStoreController(IDimStoreAppService dimStoreAppService)
        {
            _dimStoreAppService = dimStoreAppService;
        }

        /// <summary>
        /// Get all stores for dropdowns/multiselects
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<object>> GetStoresAsync([FromQuery] string format = "full")
        {
            try
            {
                var stores = await _dimStoreAppService.GetActiveStoresAsync();
                
                if (format.Equals("simple", StringComparison.OrdinalIgnoreCase))
                {
                    // Return simplified format for UI components like Syncfusion MultiSelect
                    var simplifiedStores = stores.Select(s => new
                    {
                        text = s.StoreName ?? s.StoreId,
                        value = s.StoreId,
                        storeType = s.StoreType,
                        country = s.Country
                    }).ToList();
                    return Ok(simplifiedStores);
                }
                
                return Ok(stores);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get stores by country
        /// </summary>
        [HttpGet("by-country/{country}")]
        public async Task<ActionResult<List<DimStoreDto>>> GetStoresByCountryAsync(string country)
        {
            try
            {
                var stores = await _dimStoreAppService.GetStoresByCountryAsync(country.ToUpperInvariant());
                return Ok(stores);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

    }
}
