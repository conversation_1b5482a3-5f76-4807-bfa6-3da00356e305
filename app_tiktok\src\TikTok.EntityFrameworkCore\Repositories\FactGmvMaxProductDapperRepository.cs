using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;
using TikTok.Facts.FactGmvMaxProduct;
using TikTok.Repositories;
using TikTok.Facts.FactGmvMaxCampaign;

namespace TikTok.EntityFrameworkCore.Repositories
{
    public class FactGmvMaxProductDapperRepository : DapperRepository<FactGmvMaxProductEntity, Guid>, IFactGmvMaxProductDapperRepository
    {
        public FactGmvMaxProductDapperRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<IEnumerable<FactGmvMaxProductEntity>> GetListByDateRangeAsync(DateTime from, DateTime to)
        {
            return await GetListByDateRangeAsync(from, to, null);
        }

        public async Task<IEnumerable<FactGmvMaxProductEntity>> GetListByDateRangeAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds, string? currency = "USD")
        {
            // ✅ Xử lý filter AdvertiserId: null=admin (xem tất cả), empty=không có quyền, có data=filter theo quyền
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            string sql = $@"
                SELECT fp.*
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                WHERE fp.DimDateId IN (
                    SELECT Id FROM [dbo].[Dim_DimDates]
                    WHERE FullDate >= @From AND FullDate <= @To
                )
                {advertiserFilter}
                ORDER BY fp.Date";

            return await QueryAsync<FactGmvMaxProductEntity>(sql, new { From = from.Date, To = to.Date });
        }

        public async Task<IEnumerable<GmvMaxProductTrendDto>> GetTrendsAsync(DateTime from, DateTime to)
        {
            return await GetTrendsAsync(from, to, null);
        }

        public async Task<IEnumerable<GmvMaxProductTrendDto>> GetTrendsAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds)
        {
            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            string sql = $@"
                SELECT fp.DimDateId AS DateId,
                       SUM(ISNULL(fp.GrossRevenueUSD, 0)) AS TotalGrossRevenue,
                       AVG(CASE WHEN fp.ROAS IS NOT NULL THEN fp.ROAS ELSE NULL END) AS AverageROAS,
                       AVG(CASE WHEN fp.TACOS IS NOT NULL THEN fp.TACOS ELSE NULL END) AS AverageTACOS,
                       SUM(fp.Orders) AS TotalOrders,
                       COUNT(DISTINCT fp.ProductId) AS ProductCount,
                       COUNT(DISTINCT fp.StoreId) AS StoreCount
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @From AND dd.FullDate <= @To
                {advertiserFilter}
                GROUP BY fp.DimDateId
                ORDER BY fp.DimDateId";

            return await QueryAsync<GmvMaxProductTrendDto>(sql, new { From = from.Date, To = to.Date });
        }

        public async Task<IEnumerable<GmvMaxProductTopSellingDto>> GetTopSellingAsync(DateTime from, DateTime to, int limit)
        {
            return await GetTopSellingAsync(from, to, limit, null);
        }

        public async Task<IEnumerable<GmvMaxProductTopSellingDto>> GetTopSellingAsync(DateTime from, DateTime to, int limit, List<string>? allowedAdvertiserIds)
        {
            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            string sql = $@"
                SELECT TOP (@Limit)
                       fp.ProductId,
                       MAX(fp.ProductName) AS ProductName,
                       SUM(fp.Orders) AS TotalOrders,
                       SUM(ISNULL(fp.GrossRevenueUSD, 0)) AS TotalRevenue,
                       AVG(CASE WHEN fp.ROAS IS NOT NULL THEN fp.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @From AND dd.FullDate <= @To
                {advertiserFilter}
                GROUP BY fp.ProductId
                ORDER BY TotalOrders DESC";

            return await QueryAsync<GmvMaxProductTopSellingDto>(sql, new { From = from.Date, To = to.Date, Limit = limit });
        }

        public async Task<GmvMaxProductDashboardDto> GetDashboardAsync(string? currency = "USD")
        {
            return await GetDashboardAsync(currency, null);
        }

        public async Task<GmvMaxProductDashboardDto> GetDashboardAsync(string? currency, List<string>? allowedAdvertiserIds)
        {
            var now = DateTime.Now;
            var currentMonth = new DateTime(now.Year, now.Month, 1);
            var lastMonth = currentMonth.AddMonths(-1);
            var lastMonthEnd = currentMonth.AddDays(-1);

            // Tính toán tuần đúng theo thứ 2 đến chủ nhật
            // Tuần hiện tại: từ thứ 2 tuần này đến chủ nhật tuần này
            var currentWeekStart = GetWeekStart(now); // Thứ 2 tuần này
            var currentWeekEnd = currentWeekStart.AddDays(6); // Chủ nhật tuần này

            // Tính toán tuần tương ứng tháng trước
            var lastMonthWeekStart = GetWeekStartInMonth(lastMonth.AddDays(now.Day - 1));
            var lastMonthWeekEnd = lastMonthWeekStart.AddDays(6);

            // Tuần trước: từ thứ 2 tuần trước đến chủ nhật tuần trước
            var oneWeekAgoStart = currentWeekStart.AddDays(-7); // Thứ 2 tuần trước
            var oneWeekAgoEnd = oneWeekAgoStart.AddDays(6); // Chủ nhật tuần trước

            // 2 tuần trước: từ thứ 2 2 tuần trước đến chủ nhật 2 tuần trước
            var twoWeeksAgoStart = currentWeekStart.AddDays(-14); // Thứ 2 2 tuần trước
            var twoWeeksAgoEnd = twoWeeksAgoStart.AddDays(6); // Chủ nhật 2 tuần trước

            // ✅ Tạo advertiser filter clause
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            // SQL cho doanh thu và chi tiêu theo tháng (12 tháng gần nhất) - với currency support
            string monthlySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month], 
                       SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0) * fp.Orders) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @FromDate
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate)
                ORDER BY [Year], [Month];";

            // SQL cho doanh thu và chi tiêu theo tuần (tính theo tuần thực tế từ thứ 2 đến chủ nhật) - với currency support
            string weeklySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month],
                       -- Tính tuần thực tế: tuần bắt đầu từ thứ 2
                       DATEDIFF(WEEK, 
                           DATEADD(DAY, -(DATEPART(WEEKDAY, dd.FullDate) + 5) % 7, 
                               DATEADD(DAY, 1-DATEPART(DAY, dd.FullDate), dd.FullDate)), 
                           dd.FullDate) + 1 AS [Week],
                       SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0) * fp.Orders) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @FromDate
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate),
                         DATEDIFF(WEEK, 
                             DATEADD(DAY, -(DATEPART(WEEKDAY, dd.FullDate) + 5) % 7, 
                                 DATEADD(DAY, 1-DATEPART(DAY, dd.FullDate), dd.FullDate)), 
                             dd.FullDate) + 1
                ORDER BY [Year], [Month], [Week];";

            // SQL cho xếp hạng shop theo tuần (doanh thu) - với currency support
            string shopRankingSql = $@"
                SELECT TOP 10 fp.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(fp.Orders) AS TotalOrders,
                       AVG(CASE WHEN fp.ROAS IS NOT NULL THEN fp.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fp.DimStoreId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY fp.StoreId
                ORDER BY TotalRevenue DESC;";

            // ✅ NEW: SQL cho xếp hạng shop theo tuần (chi tiêu) - với currency support
            string shopCostRankingSql = $@"
                SELECT TOP 10 fp.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0) * fp.Orders) AS TotalCost,
                       SUM(fp.Orders) AS TotalOrders,
                       AVG(CASE WHEN fp.ROAS IS NOT NULL THEN fp.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fp.DimStoreId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY fp.StoreId
                ORDER BY TotalCost DESC;";

            // Lấy dữ liệu 12 tháng gần nhất
            var fromDate = now.AddMonths(-12);
            var monthly = await QueryAsync<DashboardMonthly>(monthlySql, new { FromDate = fromDate.Date });
            var weekly = await QueryAsync<DashboardWeekly>(weeklySql, new { FromDate = fromDate.Date });

            // Lấy xếp hạng shop tuần hiện tại (doanh thu)
            var currentWeekRanking = await QueryAsync<DashboardShopRanking>(shopRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // Lấy xếp hạng shop 2 tuần trước (doanh thu)
            var twoWeeksAgoRanking = await QueryAsync<DashboardShopRanking>(shopRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // Lấy xếp hạng shop 1 tuần trước (doanh thu)
            var oneWeekAgoRanking = await QueryAsync<DashboardShopRanking>(shopRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            // ✅ NEW: Lấy xếp hạng shop tuần hiện tại (chi tiêu)
            var currentWeekCostRanking = await QueryAsync<DashboardShopCostRanking>(shopCostRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // ✅ NEW: Lấy xếp hạng shop 2 tuần trước (chi tiêu)
            var twoWeeksAgoCostRanking = await QueryAsync<DashboardShopCostRanking>(shopCostRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // ✅ NEW: Lấy xếp hạng shop 1 tuần trước (chi tiêu)
            var oneWeekAgoCostRanking = await QueryAsync<DashboardShopCostRanking>(shopCostRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            // Tính toán doanh thu tháng hiện tại và tháng trước
            decimal currentMonthTotalRevenue = 0, lastMonthTotalRevenue = 0;
            decimal currentMonthTotalCost = 0, lastMonthTotalCost = 0;
            foreach (var m in monthly)
            {
                if (m.Year == now.Year && m.Month == now.Month) 
                {
                    currentMonthTotalRevenue = m.TotalRevenue;
                    currentMonthTotalCost = m.TotalCost;
                }
                if (m.Year == lastMonth.Year && m.Month == lastMonth.Month) 
                {
                    lastMonthTotalRevenue = m.TotalRevenue;
                    lastMonthTotalCost = m.TotalCost;
                }
            }

            return new GmvMaxProductDashboardDto
            {
                CurrentMonth = new DashboardMonthInfo { Year = now.Year, Month = now.Month, TotalRevenue = currentMonthTotalRevenue, TotalCost = currentMonthTotalCost },
                LastMonth = new DashboardMonthInfo { Year = lastMonth.Year, Month = lastMonth.Month, TotalRevenue = lastMonthTotalRevenue, TotalCost = lastMonthTotalCost },
                WeeklyData = new List<DashboardWeekly>(weekly),
                MonthlyData = new List<DashboardMonthly>(monthly),
                CurrentWeekShopRanking = new List<DashboardShopRanking>(currentWeekRanking),
                TwoWeeksAgoShopRanking = new List<DashboardShopRanking>(twoWeeksAgoRanking),
                OneWeekAgoShopRanking = new List<DashboardShopRanking>(oneWeekAgoRanking),
                // ✅ NEW: Cost rankings
                CurrentWeekShopCostRanking = new List<DashboardShopCostRanking>(currentWeekCostRanking),
                TwoWeeksAgoShopCostRanking = new List<DashboardShopCostRanking>(twoWeeksAgoCostRanking),
                OneWeekAgoShopCostRanking = new List<DashboardShopCostRanking>(oneWeekAgoCostRanking)
            };
        }

        private DateTime GetWeekStartInMonth(DateTime date)
        {
            var firstDayOfMonth = new DateTime(date.Year, date.Month, 1);
            var dayOfWeek = (int)firstDayOfMonth.DayOfWeek;
            var weekStart = firstDayOfMonth.AddDays(-dayOfWeek + 1);

            // Tìm tuần chứa ngày hiện tại
            while (weekStart.AddDays(6) < date)
            {
                weekStart = weekStart.AddDays(7);
            }

            return weekStart;
        }

        // Helper method to get the start of the week (Monday) for any date
        private DateTime GetWeekStart(DateTime date)
        {
            // Calculate days to subtract to get to Monday (0 = Sunday, 1 = Monday, etc.)
            var daysToSubtract = ((int)date.DayOfWeek + 6) % 7; // Convert Sunday=0 to Monday=0
            return date.AddDays(-daysToSubtract).Date;
        }

        public async Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD")
        {
            return await GetDashboardSummaryAsync(currency, null);
        }

        public async Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency, List<string>? allowedAdvertiserIds)
        {
            var now = DateTime.Now;
            var from = new DateTime(now.Year, now.Month, 1); // Ngày đầu tháng
            var to = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month), 23, 59, 59); // Ngày cuối tháng

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            string sql = $@"
                SELECT 
                    -- Financial Metrics (using CostPerOrder * Orders as proxy for total cost)
                    SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) AS TotalCost,
                    SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) AS TotalNetCost,
                    SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalGrossRevenue,
                    
                    -- Performance Metrics
                    AVG(CASE WHEN fp.ROAS IS NOT NULL THEN fp.ROAS ELSE NULL END) AS AverageROAS,
                    AVG(CASE WHEN fp.TACOS IS NOT NULL THEN fp.TACOS ELSE NULL END) AS AverageTACOS,
                    
                    -- Volume Metrics
                    SUM(fp.Orders) AS TotalOrders,
                    
                    -- Product Metrics
                    COUNT(DISTINCT fp.ProductId) AS ProductCount,
                    COUNT(DISTINCT fp.StoreId) AS ActiveStores,
                    COUNT(DISTINCT fp.DimAdAccountId) AS ActiveAdvertisers,
                    
                    -- Data Count
                    COUNT(*) AS DataPointCount
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @From AND dd.FullDate <= @To
                {advertiserFilter}";

            var result = await QuerySingleOrDefaultAsync<DashboardSummaryDto>(sql, new { From = from.Date, To = to.Date });

            if (result == null)
            {
                // Return empty result if no data found
                return new DashboardSummaryDto
                {
                    TotalCost = 0m,
                    TotalNetCost = 0m,
                    TotalGrossRevenue = 0m,
                    AverageROAS = 0m,
                    AverageTACOS = 0m,
                    TotalOrders = 0,
                    ProductCount = 0,
                    ActiveStores = 0,
                    ActiveAdvertisers = 0,
                    Month = now.Month,
                    Year = now.Year,
                    MonthName = now.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN")),
                    FromDate = from,
                    ToDate = to,
                    DataPointCount = 0
                };
            }

            // Set additional properties
            result.Month = now.Month;
            result.Year = now.Year;
            result.MonthName = now.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN"));
            result.FromDate = from;
            result.ToDate = to;

            return result;
        }

        public async Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD")
        {
            return await GetDetailedAnalysisDataAsync(currency, null);
        }

        public async Task<object> GetDetailedAnalysisDataAsync(string? currency, List<string>? allowedAdvertiserIds)
        {
            var now = DateTime.Now;
            var from = now.AddDays(-6).Date; // 7 ngày gần nhất (bao gồm hôm nay)
            var to = now.Date.AddDays(1).AddSeconds(-1); // Đến cuối ngày hôm nay

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            string sql = $@"
                SELECT 
                    dd.FullDate,
                    DAY(dd.FullDate) AS DayNumber,
                    DATENAME(WEEKDAY, dd.FullDate) AS DayName,
                    -- Financial Metrics (using CostPerOrder * Orders as proxy for total cost)
                    SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) AS TotalCost,
                    SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) AS TotalNetCost,
                    SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalGrossRevenue,
                    SUM(ISNULL(fp.GrossRevenueVND, 0)) AS TotalGrossRevenueVND,
                    -- Orders
                    SUM(ISNULL(fp.Orders, 0)) AS TotalOrders,
                    -- Calculated Metrics
                    CASE 
                        WHEN SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) > 0 
                        THEN (SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) / SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders)) * 100
                        ELSE 0 
                    END AS ROI,
                    CASE 
                        WHEN SUM(ISNULL(fp.Orders, 0)) > 0 
                        THEN SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) / SUM(ISNULL(fp.Orders, 0))
                        ELSE 0 
                    END AS CPA
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY dd.FullDate, DAY(dd.FullDate), DATENAME(WEEKDAY, dd.FullDate)
                ORDER BY dd.FullDate ASC";

            var results = await QueryAsync<dynamic>(sql, new { FromDate = from, ToDate = to });

            return new
            {
                FromDate = from,
                ToDate = to,
                DailyData = results.Select(r => new
                {
                    Date = (DateTime)r.FullDate,
                    DayNumber = (int)r.DayNumber,
                    DayName = (string)r.DayName,
                    // Financial
                    Cost = (decimal)(r.TotalCost ?? 0),
                    NetCost = (decimal)(r.TotalNetCost ?? 0),
                    Revenue = (decimal)(r.TotalGrossRevenue ?? 0),
                    RevenueVND = (decimal)(r.TotalGrossRevenueVND ?? 0),
                    ROI = (decimal)(r.ROI ?? 0),
                    // Orders
                    Orders = (int)(r.TotalOrders ?? 0),
                    CPA = (decimal)(r.CPA ?? 0)
                }).ToList()
            };
        }

        // ✅ NEW: Section-specific methods for independent loading

        public async Task<SummaryCardsDto> GetSummaryCardsAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            // ✅ OPTIMIZED: Chỉ lấy dữ liệu tháng hiện tại
            var from = new DateTime(now.Year, now.Month, 1);
            var to = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month), 23, 59, 59);

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: Sử dụng currency field để tăng performance
            string sql = $@"
                SELECT 
                    SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) AS TotalCost,
                    SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) AS TotalNetCost,
                    SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalGrossRevenue,
                    SUM(ISNULL(fp.Orders, 0)) AS TotalOrders,
                    COUNT(DISTINCT fp.ProductId) AS ProductCount,
                    COUNT(DISTINCT fp.StoreId) AS ActiveStores,
                    COUNT(DISTINCT fp.AdvertiserId) AS ActiveAdvertisers,
                    CASE 
                        WHEN SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) > 0 
                        THEN SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) / SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders)
                        ELSE 0 
                    END AS AverageROAS,
                    CASE 
                        WHEN SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) > 0 
                        THEN SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) / SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0))
                        ELSE 0 
                    END AS AverageTACOS
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}";

            var result = await QueryAsync<dynamic>(sql, new { FromDate = from, ToDate = to });
            var firstResult = result.FirstOrDefault();

            return new SummaryCardsDto
            {
                TotalCost = (decimal)(firstResult?.TotalCost ?? 0),
                TotalNetCost = (decimal)(firstResult?.TotalNetCost ?? 0),
                TotalGrossRevenue = (decimal)(firstResult?.TotalGrossRevenue ?? 0),
                AverageROAS = (decimal)(firstResult?.AverageROAS ?? 0),
                AverageTACOS = (decimal)(firstResult?.AverageTACOS ?? 0),
                TotalOrders = (int)(firstResult?.TotalOrders ?? 0),
                CampaignCount = (int)(firstResult?.ProductCount ?? 0), // Using ProductCount as CampaignCount
                ActiveStores = (int)(firstResult?.ActiveStores ?? 0),
                ActiveAdvertisers = (int)(firstResult?.ActiveAdvertisers ?? 0),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            var currentMonth = new DateTime(now.Year, now.Month, 1);
            var lastMonth = currentMonth.AddMonths(-1);

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: SQL cho tháng hiện tại và tháng trước - với currency support
            string monthlySql = $@"
                SELECT 
                    CASE 
                        WHEN dd.FullDate >= @CurrentMonthStart AND dd.FullDate < @NextMonthStart THEN 'Current'
                        WHEN dd.FullDate >= @LastMonthStart AND dd.FullDate < @CurrentMonthStart THEN 'Last'
                        ELSE 'Other'
                    END AS MonthType,
                    SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) AS TotalCost,
                    SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0) * fp.Orders) AS TotalNetCost,
                    SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalGrossRevenue,
                    SUM(ISNULL(fp.Orders, 0)) AS TotalOrders
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE (dd.FullDate >= @CurrentMonthStart AND dd.FullDate < @NextMonthStart)
                   OR (dd.FullDate >= @LastMonthStart AND dd.FullDate < @CurrentMonthStart)
                {advertiserFilter}
                GROUP BY 
                    CASE 
                        WHEN dd.FullDate >= @CurrentMonthStart AND dd.FullDate < @NextMonthStart THEN 'Current'
                        WHEN dd.FullDate >= @LastMonthStart AND dd.FullDate < @CurrentMonthStart THEN 'Last'
                        ELSE 'Other'
                    END";

            // ✅ OPTIMIZED: SQL cho weekly data - chỉ lấy tháng hiện tại và tháng trước
            string weeklySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month],
                       -- Tính tuần trong tháng: 1-7 = tuần 1, 8-14 = tuần 2, 15-21 = tuần 3, 22-31 = tuần 4
                       CASE 
                           WHEN DAY(dd.FullDate) BETWEEN 1 AND 7 THEN 1
                           WHEN DAY(dd.FullDate) BETWEEN 8 AND 14 THEN 2
                           WHEN DAY(dd.FullDate) BETWEEN 15 AND 21 THEN 3
                           WHEN DAY(dd.FullDate) BETWEEN 22 AND 31 THEN 4
                           ELSE 4
                       END AS [Week],
                       SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0) * fp.Orders) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate),
                         CASE 
                             WHEN DAY(dd.FullDate) BETWEEN 1 AND 7 THEN 1
                             WHEN DAY(dd.FullDate) BETWEEN 8 AND 14 THEN 2
                             WHEN DAY(dd.FullDate) BETWEEN 15 AND 21 THEN 3
                             WHEN DAY(dd.FullDate) BETWEEN 22 AND 31 THEN 4
                             ELSE 4
                         END
                ORDER BY [Year], [Month], [Week];";

            var monthlyResults = await QueryAsync<dynamic>(monthlySql, new { 
                CurrentMonthStart = currentMonth,
                NextMonthStart = currentMonth.AddMonths(1),
                LastMonthStart = lastMonth
            });

            // ✅ OPTIMIZED: Chỉ lấy dữ liệu 2 tháng gần nhất cho weekly data
            var fromDate = lastMonth; // Tháng trước
            var toDate = currentMonth.AddMonths(1).AddDays(-1); // Cuối tháng hiện tại
            var weeklyResults = await QueryAsync<DashboardWeeklyRevenue>(weeklySql, new { FromDate = fromDate.Date, ToDate = toDate.Date });

            var currentMonthData = monthlyResults.FirstOrDefault(r => r.MonthType == "Current");
            var lastMonthData = monthlyResults.FirstOrDefault(r => r.MonthType == "Last");

            return new OverviewSectionDto
            {
                CurrentMonth = new MonthDataDto
                {
                    Year = now.Year,
                    Month = now.Month,
                    TotalCost = (decimal)(currentMonthData?.TotalCost ?? 0),
                    TotalRevenue = (decimal)(currentMonthData?.TotalGrossRevenue ?? 0),
                    MonthName = now.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN"))
                },
                LastMonth = new MonthDataDto
                {
                    Year = lastMonth.Year,
                    Month = lastMonth.Month,
                    TotalCost = (decimal)(lastMonthData?.TotalCost ?? 0),
                    TotalRevenue = (decimal)(lastMonthData?.TotalGrossRevenue ?? 0),
                    MonthName = lastMonth.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN"))
                },
                WeeklyData = weeklyResults.Select(w => new WeeklyDataDto
                {
                    Year = w.Year,
                    Month = w.Month,
                    Week = w.Week,
                    TotalCost = w.TotalCost,
                    TotalRevenue = w.TotalRevenue
                }).ToList(),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            // ✅ OPTIMIZED: Chỉ lấy 6 tháng gần nhất (tính cả tháng hiện tại)
            var from = now.AddMonths(-5); // 6 tháng gần nhất

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: SQL cho weekly data - chỉ lấy tháng hiện tại và tháng trước
            string weeklySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month],
                       -- Tính tuần trong tháng: 1-7 = tuần 1, 8-14 = tuần 2, 15-21 = tuần 3, 22-31 = tuần 4
                       CASE 
                           WHEN DAY(dd.FullDate) BETWEEN 1 AND 7 THEN 1
                           WHEN DAY(dd.FullDate) BETWEEN 8 AND 14 THEN 2
                           WHEN DAY(dd.FullDate) BETWEEN 15 AND 21 THEN 3
                           WHEN DAY(dd.FullDate) BETWEEN 22 AND 31 THEN 4
                           ELSE 4
                       END AS [Week],
                       SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0) * fp.Orders) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate),
                         CASE 
                             WHEN DAY(dd.FullDate) BETWEEN 1 AND 7 THEN 1
                             WHEN DAY(dd.FullDate) BETWEEN 8 AND 14 THEN 2
                             WHEN DAY(dd.FullDate) BETWEEN 15 AND 21 THEN 3
                             WHEN DAY(dd.FullDate) BETWEEN 22 AND 31 THEN 4
                             ELSE 4
                         END
                ORDER BY [Year], [Month], [Week];";

            // ✅ OPTIMIZED: SQL cho monthly data - chỉ lấy 6 tháng gần nhất
            string monthlySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month], 
                       SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0) * fp.Orders) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @FromDate
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate)
                ORDER BY [Year], [Month];";

            // ✅ OPTIMIZED: Chỉ lấy weekly data cho tháng hiện tại và tháng trước
            var currentMonth = new DateTime(now.Year, now.Month, 1);
            var lastMonth = currentMonth.AddMonths(-1);
            var weeklyFromDate = lastMonth;
            var weeklyToDate = currentMonth.AddMonths(1).AddDays(-1);

            var weeklyResults = await QueryAsync<DashboardWeeklyRevenue>(weeklySql, new { FromDate = weeklyFromDate.Date, ToDate = weeklyToDate.Date });
            var monthlyResults = await QueryAsync<DashboardMonthlyRevenue>(monthlySql, new { FromDate = from.Date });

            return new ChartsDataDto
            {
                WeeklyData = weeklyResults.Select(w => new WeeklyDataDto
                {
                    Year = w.Year,
                    Month = w.Month,
                    Week = w.Week,
                    TotalCost = w.TotalCost,
                    TotalRevenue = w.TotalRevenue
                }).ToList(),
                MonthlyData = monthlyResults.Select(m => new MonthlyDataDto
                {
                    Year = m.Year,
                    Month = m.Month,
                    TotalCost = m.TotalCost,
                    TotalRevenue = m.TotalRevenue,
                    MonthName = new DateTime(m.Year, m.Month, 1).ToString("MMMM", new System.Globalization.CultureInfo("vi-VN"))
                }).ToList(),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<DetailedChartsDto> GetDetailedChartsAsync(List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            // ✅ OPTIMIZED: Chỉ lấy 7 ngày gần nhất (bao gồm hôm nay)
            var from = now.AddDays(-6).Date; // 7 ngày gần nhất (bao gồm hôm nay)
            var to = now.Date.AddDays(1).AddSeconds(-1); // Đến cuối ngày hôm nay

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: SQL cho daily data - chỉ lấy các field cần thiết
            string dailySql = $@"
                SELECT 
                    dd.FullDate,
                    DAY(dd.FullDate) AS DayNumber,
                    DATENAME(WEEKDAY, dd.FullDate) AS DayName,
                    -- Financial Metrics (chỉ USD để tăng performance)
                    SUM(ISNULL(fp.CostPerOrder, 0) * fp.Orders) AS TotalCost,
                    SUM(ISNULL(fp.CostPerOrder, 0) * fp.Orders) AS TotalNetCost,
                    SUM(ISNULL(fp.GrossRevenue, 0)) AS TotalGrossRevenue,
                    -- Orders
                    SUM(ISNULL(fp.Orders, 0)) AS TotalOrders,
                    -- Calculated Metrics
                    CASE 
                        WHEN SUM(ISNULL(fp.CostPerOrder, 0) * fp.Orders) > 0 
                        THEN (SUM(ISNULL(fp.GrossRevenue, 0)) / SUM(ISNULL(fp.CostPerOrder, 0) * fp.Orders)) * 100
                        ELSE 0 
                    END AS ROI,
                    CASE 
                        WHEN SUM(ISNULL(fp.Orders, 0)) > 0 
                        THEN SUM(ISNULL(fp.CostPerOrder, 0) * fp.Orders) / SUM(ISNULL(fp.Orders, 0))
                        ELSE 0 
                    END AS CPA
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY dd.FullDate, DAY(dd.FullDate), DATENAME(WEEKDAY, dd.FullDate)
                ORDER BY dd.FullDate ASC";

            var dailyResults = await QueryAsync<dynamic>(dailySql, new { FromDate = from, ToDate = to });

            return new DetailedChartsDto
            {
                FinancialAnalysis = new FinancialAnalysisDto
                {
                    DailyData = dailyResults.Select(r => new DailyDataDto
                    {
                        Date = (DateTime)r.FullDate,
                        Period = (string)r.DayName,
                        Cost = (decimal)(r.TotalCost ?? 0),
                        Revenue = (decimal)(r.TotalGrossRevenue ?? 0),
                        ROI = (decimal)(r.ROI ?? 0)
                    }).ToList()
                },
                OrdersAnalysis = new OrdersAnalysisDto
                {
                    DailyData = dailyResults.Select(r => new DailyDataDto
                    {
                        Date = (DateTime)r.FullDate,
                        Period = (string)r.DayName,
                        Orders = (int)(r.TotalOrders ?? 0),
                        CPA = (decimal)(r.CPA ?? 0)
                    }).ToList()
                },
                LivePerformance = new LivePerformanceDto
                {
                    DailyData = dailyResults.Select(r => new DailyDataDto
                    {
                        Date = (DateTime)r.FullDate,
                        Period = (string)r.DayName,
                        LiveViews = 0, // Not applicable for products
                        TenSecondLiveViews = 0, // Not applicable for products
                        LiveEngagementRate = 0 // Not applicable for products
                    }).ToList()
                },
                FromDate = from,
                ToDate = to,
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            
            // ✅ Sử dụng logic tuần giống GetDashboardAsync (thứ 2 đến chủ nhật)
            var currentWeekStart = GetWeekStart(now); // Thứ 2 tuần này
            var currentWeekEnd = currentWeekStart.AddDays(6); // Chủ nhật tuần này
            
            // Tuần trước: từ thứ 2 tuần trước đến chủ nhật tuần trước
            var oneWeekAgoStart = currentWeekStart.AddDays(-7); // Thứ 2 tuần trước
            var oneWeekAgoEnd = oneWeekAgoStart.AddDays(6); // Chủ nhật tuần trước
            
            // 2 tuần trước: từ thứ 2 2 tuần trước đến chủ nhật 2 tuần trước
            var twoWeeksAgoStart = currentWeekStart.AddDays(-14); // Thứ 2 2 tuần trước
            var twoWeeksAgoEnd = twoWeeksAgoStart.AddDays(6); // Chủ nhật 2 tuần trước

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fp.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ SQL cho xếp hạng store theo tuần (doanh thu) - với currency support và store name
            string storeRankingSql = $@"
                SELECT TOP 10 fp.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fp.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(fp.Orders) AS TotalOrders,
                       AVG(CASE WHEN fp.ROAS IS NOT NULL THEN fp.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fp.DimStoreId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY fp.StoreId
                ORDER BY TotalRevenue DESC;";

            // ✅ SQL cho xếp hạng store theo tuần (chi tiêu) - với currency support và store name
            string storeCostRankingSql = $@"
                SELECT TOP 10 fp.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fp.CostPerOrder{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0) * fp.Orders) AS TotalCost,
                       SUM(fp.Orders) AS TotalOrders,
                       AVG(CASE WHEN fp.ROAS IS NOT NULL THEN fp.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxProducts] fp
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fp.DimDateId
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fp.DimStoreId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY fp.StoreId
                ORDER BY TotalCost DESC;";

            // Lấy xếp hạng store tuần hiện tại (doanh thu)
            var currentWeekRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // Lấy xếp hạng store 2 tuần trước (doanh thu)
            var twoWeeksAgoRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // Lấy xếp hạng store 1 tuần trước (doanh thu)
            var oneWeekAgoRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            // ✅ Lấy xếp hạng store tuần hiện tại (chi tiêu)
            var currentWeekCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // ✅ Lấy xếp hạng store 2 tuần trước (chi tiêu)
            var twoWeeksAgoCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // ✅ Lấy xếp hạng store 1 tuần trước (chi tiêu)
            var oneWeekAgoCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            return new RankingsDataDto
            {
                CurrentWeekStoreRanking = currentWeekRanking.Select((r, index) => new StoreRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalRevenue = r.TotalRevenue,
                    TotalOrders = r.TotalOrders,
                    AverageROAS = r.AverageROAS,
                    Rank = index + 1
                }).ToList(),
                OneWeekAgoStoreRanking = oneWeekAgoRanking.Select((r, index) => new StoreRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalRevenue = r.TotalRevenue,
                    TotalOrders = r.TotalOrders,
                    AverageROAS = r.AverageROAS,
                    Rank = index + 1
                }).ToList(),
                TwoWeeksAgoStoreRanking = twoWeeksAgoRanking.Select((r, index) => new StoreRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalRevenue = r.TotalRevenue,
                    TotalOrders = r.TotalOrders,
                    AverageROAS = r.AverageROAS,
                    Rank = index + 1
                }).ToList(),
                CurrentWeekStoreCostRanking = currentWeekCostRanking.Select((r, index) => new StoreCostRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalCost = r.TotalCost,
                    TotalOrders = r.TotalOrders,
                    AverageCPA = r.AverageROAS, // Sử dụng AverageROAS thay vì AverageCPA
                    Rank = index + 1
                }).ToList(),
                OneWeekAgoStoreCostRanking = oneWeekAgoCostRanking.Select((r, index) => new StoreCostRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalCost = r.TotalCost,
                    TotalOrders = r.TotalOrders,
                    AverageCPA = r.AverageROAS, // Sử dụng AverageROAS thay vì AverageCPA
                    Rank = index + 1
                }).ToList(),
                TwoWeeksAgoStoreCostRanking = twoWeeksAgoCostRanking.Select((r, index) => new StoreCostRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalCost = r.TotalCost,
                    TotalOrders = r.TotalOrders,
                    AverageCPA = r.AverageROAS, // Sử dụng AverageROAS thay vì AverageCPA
                    Rank = index + 1
                }).ToList(),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

    }
}


