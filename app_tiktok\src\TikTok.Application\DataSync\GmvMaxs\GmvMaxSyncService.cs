using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.BusinessCenters;
using TikTok.Repositories;
using TikTokBusinessApi;
using Volo.Abp;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ tối ưu dữ liệu GMV Max
    /// </summary>
    public class GmvMaxSyncService : IGmvMaxSyncService
    {
        private readonly IAssetRepository _assetRepository;
        private readonly IBusinessCenterCache _businessCenterCache;
        private readonly IGmvMaxCampaignSyncService _gmvMaxCampaignSyncService;
        private readonly IGmvMaxProductCampaignSyncService _gmvMaxProductCampaignSyncService;
        private readonly IGmvMaxProductDetailProductSyncService _gmvMaxProductDetailProductSyncService;
        private readonly IGmvMaxProductCreativeSyncService _gmvMaxProductCreativeSyncService;
        private readonly IGmvMaxLiveCampaignSyncService _gmvMaxLiveCampaignSyncService;
        private readonly IGmvMaxLiveLivestreamSyncService _gmvMaxLiveLivestreamSyncService;
        private readonly IRawGmvMaxCampaignsRepository _rwgmvMaxCampaignsRepository;
        private readonly ILogger<GmvMaxSyncService> _logger;

        public GmvMaxSyncService(
            IAssetRepository assetRepository,
            IBusinessCenterCache businessCenterCache,
            IGmvMaxCampaignSyncService gmvMaxCampaignSyncService,
            IGmvMaxProductCampaignSyncService gmvMaxProductCampaignSyncService,
            IGmvMaxProductDetailProductSyncService gmvMaxProductDetailProductSyncService,
            IGmvMaxProductCreativeSyncService gmvMaxProductCreativeSyncService,
            IGmvMaxLiveCampaignSyncService gmvMaxLiveCampaignSyncService,
            IGmvMaxLiveLivestreamSyncService gmvMaxLiveLivestreamSyncService,
            IRawGmvMaxCampaignsRepository rwgmvMaxCampaignsRepository,
            ILogger<GmvMaxSyncService> logger)
        {
            _assetRepository = assetRepository;
            _businessCenterCache = businessCenterCache;
            _gmvMaxCampaignSyncService = gmvMaxCampaignSyncService;
            _gmvMaxProductCampaignSyncService = gmvMaxProductCampaignSyncService;
            _gmvMaxProductDetailProductSyncService = gmvMaxProductDetailProductSyncService;
            _gmvMaxProductCreativeSyncService = gmvMaxProductCreativeSyncService;
            _gmvMaxLiveCampaignSyncService = gmvMaxLiveCampaignSyncService;
            _gmvMaxLiveLivestreamSyncService = gmvMaxLiveLivestreamSyncService;
            _rwgmvMaxCampaignsRepository = rwgmvMaxCampaignsRepository;
            _logger = logger;
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max cho một Business Center
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        [UnitOfWork]
        public virtual async Task<GmvMaxSyncResult> SyncGmvMaxAsync(string bcId,DateTime? startDate=null,DateTime?endDate = null)
        {
            var result = new GmvMaxSyncResult
            {
                StartTime = DateTime.UtcNow,
                BcCount = 1
            };

            try
            {
                _logger.LogInformation("Bắt đầu đồng bộ GMV Max cho Business Center: {BcId} từ ngày {startDate} đến ngày {endDate}", bcId,startDate,endDate);

                // 1. Lấy danh sách AdvertiserIds từ bcId
                var advertiserIds = (await _rwgmvMaxCampaignsRepository.GetQueryableAsync()).Where(x => x.BcId == bcId).Select(x => x.AdvertiserId).Distinct().ToList();
                if (!advertiserIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Advertiser nào cho Business Center: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Advertiser nào cho Business Center: {bcId}";
                    return result;
                }

                result.AdvertiserCount = advertiserIds.Count;
                _logger.LogInformation("Tìm thấy {AdvertiserCount} Advertiser cho Business Center: {BcId}", advertiserIds.Count, bcId);

                // 2. Đồng bộ chiến dịch (Campaign)
                _logger.LogInformation("Bắt đầu đồng bộ chiến dịch GMV Max cho Business Center: {BcId}", bcId);
                //result.CampaignSyncResult = await _gmvMaxCampaignSyncService.SyncManyGmvMaxCampaignsAsync(bcId, advertiserIds);
                _logger.LogInformation("Hoàn thành đồng bộ chiến dịch GMV Max cho Business Center: {BcId}. Kết quả: {Result}",
                    bcId, result.CampaignSyncResult?.IsSuccess ?? false);

                // 3. Đồng bộ báo cáo chiến dịch (Product Campaign Report)
                _logger.LogInformation("Bắt đầu đồng bộ báo cáo chiến dịch GMV Max cho Business Center: {BcId}", bcId);
                result.ProductCampaignSyncResult = await _gmvMaxProductCampaignSyncService.SyncGmvMaxProductCampaignAsync(bcId, startDate, endDate);
                _logger.LogInformation("Hoàn thành đồng bộ báo cáo chiến dịch GMV Max cho Business Center: {BcId}. Kết quả: {Result}", 
                    bcId, result.ProductCampaignSyncResult?.IsSuccess ?? false);

                // 4. Đồng bộ báo cáo sản phẩm (Product Detail Product Report)
                //_logger.LogDebug("Bắt đầu đồng bộ báo cáo sản phẩm GMV Max cho Business Center: {BcId}", bcId);
                //result.ProductDetailProductSyncResult = await _gmvMaxProductDetailProductSyncService.SyncGmvMaxProductDetailProductAsync(bcId);
                //_logger.LogDebug("Hoàn thành đồng bộ báo cáo sản phẩm GMV Max cho Business Center: {BcId}. Kết quả: {Result}", 
                //    bcId, result.ProductDetailProductSyncResult?.IsSuccess ?? false);

                // 5. Đồng bộ báo cáo sáng tạo (Product Creative Report)
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo sáng tạo GMV Max cho Business Center: {BcId}", bcId);
                result.ProductCreativeSyncResult = await _gmvMaxProductCreativeSyncService.SyncGmvMaxProductCreativeAsync(bcId, startDate, endDate);
                _logger.LogDebug("Hoàn thành đồng bộ báo cáo sáng tạo GMV Max cho Business Center: {BcId}. Kết quả: {Result}",
                    bcId, result.ProductCreativeSyncResult?.IsSuccess ?? false);
                // 6. Đồng bộ báo cáo live cho campaign
                _logger.LogInformation("Bắt đầu đồng bộ báo cáo live campaign GMV Max cho Business Center: {BcId}", bcId);
                result.LiveCampaignSyncResult = await _gmvMaxLiveCampaignSyncService.SyncGmvMaxLiveCampaignAsync(bcId, startDate, endDate);
                _logger.LogInformation("Hoàn thành đồng bộ báo cáo live campaign GMV Max cho Business Center: {BcId}. Kết quả: {Result}", 
                    bcId, result.LiveCampaignSyncResult?.IsSuccess ?? false);

                // 7. Đồng bộ báo cáo live cho livestream
                //_logger.LogDebug("Bắt đầu đồng bộ báo cáo live livestream GMV Max cho Business Center: {BcId}", bcId);
                //result.LiveLivestreamSyncResult = await _gmvMaxLiveLivestreamSyncService.SyncGmvMaxLiveLivestreamAsync(bcId);
                //_logger.LogDebug("Hoàn thành đồng bộ báo cáo live livestream GMV Max cho Business Center: {BcId}. Kết quả: {Result}", 
                //    bcId, result.LiveLivestreamSyncResult?.IsSuccess ?? false);
                //result.EndTime = DateTime.UtcNow;

                _logger.LogInformation("Hoàn thành đồng bộ GMV Max cho Business Center: {BcId}. Tổng thời gian: {Duration}, Tổng bản ghi: {TotalSynced}", 
                    bcId, result.Duration, result.TotalSynced);

                return result;
            }
            catch (BusinessException ex)
            {
                result.EndTime = DateTime.UtcNow;
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi Business khi đồng bộ GMV Max cho Business Center: {BcId}", bcId);
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.UtcNow;
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max: {ex.Message}";
                _logger.LogError(ex, "Lỗi exception khi đồng bộ GMV Max cho Business Center: {BcId}", bcId);
                return result;
            }
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max với parameters tùy chỉnh
        /// </summary>
        /// <param name="parameters">Parameters chứa BcId, TikTokClient và danh sách Advertisers</param>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        public async Task<GmvMaxSyncResult> SyncGmvMaxWithParametersAsync(GmvMaxSyncParameters parameters)
        {
            var result = new GmvMaxSyncResult
            {
                StartTime = DateTime.UtcNow,
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);

                if (parameters.Advertisers == null || !parameters.Advertisers.Any())
                {
                    _logger.LogWarning("Không có Advertiser nào trong parameters cho Business Center: {BcId}", parameters.BcId);
                    result.ErrorMessage = $"Không có Advertiser nào trong parameters cho Business Center: {parameters.BcId}";
                    return result;
                }

                result.AdvertiserCount = parameters.Advertisers.Count;
                _logger.LogDebug("Có {AdvertiserCount} Advertiser trong parameters cho Business Center: {BcId}", 
                    parameters.Advertisers.Count, parameters.BcId);

                // 1. Đồng bộ chiến dịch (Campaign) với parameters
                _logger.LogDebug("Bắt đầu đồng bộ chiến dịch GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);
                result.CampaignSyncResult = await SyncCampaignsWithParametersAsync(parameters);
                _logger.LogDebug("Hoàn thành đồng bộ chiến dịch GMV Max với parameters cho Business Center: {BcId}. Kết quả: {Result}", 
                    parameters.BcId, result.CampaignSyncResult?.IsSuccess ?? false);

                // 2. Đồng bộ báo cáo chiến dịch (Product Campaign Report) với parameters
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chiến dịch GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);
                result.ProductCampaignSyncResult = await SyncProductCampaignReportsWithParametersAsync(parameters);
                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chiến dịch GMV Max với parameters cho Business Center: {BcId}. Kết quả: {Result}", 
                    parameters.BcId, result.ProductCampaignSyncResult?.IsSuccess ?? false);

                // 3. Đồng bộ báo cáo sản phẩm (Product Detail Product Report) với parameters
                //_logger.LogDebug("Bắt đầu đồng bộ báo cáo sản phẩm GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);
                //result.ProductDetailProductSyncResult = await SyncProductDetailProductReportsWithParametersAsync(parameters);
                //_logger.LogDebug("Hoàn thành đồng bộ báo cáo sản phẩm GMV Max với parameters cho Business Center: {BcId}. Kết quả: {Result}", 
                //    parameters.BcId, result.ProductDetailProductSyncResult?.IsSuccess ?? false);

                // 4. Đồng bộ báo cáo sáng tạo (Product Creative Report) với parameters
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo sáng tạo GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);
                result.ProductCreativeSyncResult = await SyncProductCreativeReportsWithParametersAsync(parameters);
                _logger.LogDebug("Hoàn thành đồng bộ báo cáo sáng tạo GMV Max với parameters cho Business Center: {BcId}. Kết quả: {Result}", 
                    parameters.BcId, result.ProductCreativeSyncResult?.IsSuccess ?? false);

                // 5. Đồng bộ báo cáo live campaign với parameters
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo live campaign GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);
                result.LiveCampaignSyncResult = await SyncLiveCampaignReportsWithParametersAsync(parameters);
                _logger.LogDebug("Hoàn thành đồng bộ báo cáo live campaign GMV Max với parameters cho Business Center: {BcId}. Kết quả: {Result}", 
                    parameters.BcId, result.LiveCampaignSyncResult?.IsSuccess ?? false);

                // 6. Đồng bộ báo cáo live livestream với parameters
                //_logger.LogDebug("Bắt đầu đồng bộ báo cáo live livestream GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);
                //result.LiveLivestreamSyncResult = await SyncLiveLivestreamReportsWithParametersAsync(parameters);
                //_logger.LogDebug("Hoàn thành đồng bộ báo cáo live livestream GMV Max với parameters cho Business Center: {BcId}. Kết quả: {Result}", 
                //    parameters.BcId, result.LiveLivestreamSyncResult?.IsSuccess ?? false);

                //result.EndTime = DateTime.UtcNow;

                _logger.LogDebug("Hoàn thành đồng bộ GMV Max với parameters cho Business Center: {BcId}. Tổng thời gian: {Duration}, Tổng bản ghi: {TotalSynced}", 
                    parameters.BcId, result.Duration, result.TotalSynced);

                return result;
            }
            catch (BusinessException ex)
            {
                result.EndTime = DateTime.UtcNow;
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi Business khi đồng bộ GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.UtcNow;
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max với parameters: {ex.Message}";
                _logger.LogError(ex, "Lỗi exception khi đồng bộ GMV Max với parameters cho Business Center: {BcId}", parameters.BcId);
                return result;
            }
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        public async Task<GmvMaxSyncResult> SyncAllGmvMaxAsync()
        {
            var result = new GmvMaxSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max cho tất cả Business Centers");

                // Lấy tất cả BC IDs từ cache hoặc repository
                // Giả sử có method để lấy tất cả BC IDs
                var allBcIds = await GetAllBusinessCenterIdsAsync();
                
                if (!allBcIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Business Center nào");
                    result.ErrorMessage = "Không tìm thấy Business Center nào";
                    return result;
                }

                _logger.LogDebug("Tìm thấy {BcCount} Business Centers", allBcIds.Count);

                var totalResult = new GmvMaxSyncResult
                {
                    StartTime = DateTime.UtcNow,
                    BcCount = allBcIds.Count
                };

                foreach (var bcId in allBcIds)
                {
                    try
                    {
                        _logger.LogDebug("Đồng bộ GMV Max cho Business Center: {BcId}", bcId);
                        var bcResult = await SyncGmvMaxAsync(bcId);
                        
                        // Tổng hợp kết quả
                        totalResult.AdvertiserCount += bcResult.AdvertiserCount;
                        
                        //if (bcResult.CampaignSyncResult != null)
                        //{
                        //    if (totalResult.CampaignSyncResult == null)
                        //        totalResult.CampaignSyncResult = new GmvMaxCampaignSyncResult();
                        //    totalResult.CampaignSyncResult.NewRecords += bcResult.CampaignSyncResult.NewRecords;
                        //    totalResult.CampaignSyncResult.UpdatedRecords += bcResult.CampaignSyncResult.UpdatedRecords;
                        //    totalResult.CampaignSyncResult.ErrorRecords += bcResult.CampaignSyncResult.ErrorRecords;
                        //}

                        if (bcResult.ProductCampaignSyncResult != null)
                        {
                            if (totalResult.ProductCampaignSyncResult == null)
                                totalResult.ProductCampaignSyncResult = new GmvMaxProductCampaignSyncResult();
                            totalResult.ProductCampaignSyncResult.NewRecords += bcResult.ProductCampaignSyncResult.NewRecords;
                            totalResult.ProductCampaignSyncResult.UpdatedRecords += bcResult.ProductCampaignSyncResult.UpdatedRecords;
                            totalResult.ProductCampaignSyncResult.ErrorRecords += bcResult.ProductCampaignSyncResult.ErrorRecords;
                        }

                        //if (bcResult.ProductDetailProductSyncResult != null)
                        //{
                        //    if (totalResult.ProductDetailProductSyncResult == null)
                        //        totalResult.ProductDetailProductSyncResult = new GmvMaxProductDetailProductSyncResult();
                        //    totalResult.ProductDetailProductSyncResult.NewRecords += bcResult.ProductDetailProductSyncResult.NewRecords;
                        //    totalResult.ProductDetailProductSyncResult.UpdatedRecords += bcResult.ProductDetailProductSyncResult.UpdatedRecords;
                        //    totalResult.ProductDetailProductSyncResult.ErrorRecords += bcResult.ProductDetailProductSyncResult.ErrorRecords;
                        //}

                        if (bcResult.ProductCreativeSyncResult != null)
                        {
                            if (totalResult.ProductCreativeSyncResult == null)
                                totalResult.ProductCreativeSyncResult = new GmvMaxProductCreativeSyncResult();
                            totalResult.ProductCreativeSyncResult.NewRecords += bcResult.ProductCreativeSyncResult.NewRecords;
                            totalResult.ProductCreativeSyncResult.UpdatedRecords += bcResult.ProductCreativeSyncResult.UpdatedRecords;
                            totalResult.ProductCreativeSyncResult.ErrorRecords += bcResult.ProductCreativeSyncResult.ErrorRecords;
                        }

                        if (bcResult.LiveCampaignSyncResult != null)
                        {
                            if (totalResult.LiveCampaignSyncResult == null)
                                totalResult.LiveCampaignSyncResult = new GmvMaxLiveCampaignSyncResult();
                            totalResult.LiveCampaignSyncResult.NewRecords += bcResult.LiveCampaignSyncResult.NewRecords;
                            totalResult.LiveCampaignSyncResult.UpdatedRecords += bcResult.LiveCampaignSyncResult.UpdatedRecords;
                            totalResult.LiveCampaignSyncResult.ErrorRecords += bcResult.LiveCampaignSyncResult.ErrorRecords;
                        }

                        //if (bcResult.LiveLivestreamSyncResult != null)
                        //{
                        //    if (totalResult.LiveLivestreamSyncResult == null)
                        //        totalResult.LiveLivestreamSyncResult = new GmvMaxLiveLivestreamSyncResult();
                        //    totalResult.LiveLivestreamSyncResult.NewRecords += bcResult.LiveLivestreamSyncResult.NewRecords;
                        //    totalResult.LiveLivestreamSyncResult.UpdatedRecords += bcResult.LiveLivestreamSyncResult.UpdatedRecords;
                        //    totalResult.LiveLivestreamSyncResult.ErrorRecords += bcResult.LiveLivestreamSyncResult.ErrorRecords;
                        //}
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max cho Business Center: {BcId}", bcId);
                        // Tiếp tục với BC tiếp theo
                    }
                }

                totalResult.EndTime = DateTime.UtcNow;

                _logger.LogDebug("Hoàn thành đồng bộ GMV Max cho tất cả Business Centers. Tổng thời gian: {Duration}, Tổng bản ghi: {TotalSynced}", 
                    totalResult.Duration, totalResult.TotalSynced);

                return totalResult;
            }
            catch (BusinessException ex)
            {
                result.EndTime = DateTime.UtcNow;
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi Business khi đồng bộ GMV Max cho tất cả Business Centers");
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.UtcNow;
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max cho tất cả Business Centers: {ex.Message}";
                _logger.LogError(ex, "Lỗi exception khi đồng bộ GMV Max cho tất cả Business Centers");
                return result;
            }
        }

        /// <summary>
        /// Lấy tất cả Business Center IDs
        /// </summary>
        /// <returns>Danh sách BC IDs</returns>
        private async Task<List<string>> GetAllBusinessCenterIdsAsync()
        {
            try
            {
                var allBusinessCenters = await _businessCenterCache.GetAllAsync();
                return allBusinessCenters.Select(x => x.BcId).Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi lấy danh sách Business Center IDs");
                return new List<string>();
            }
        }

        /// <summary>
        /// Đồng bộ Campaign với parameters
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Kết quả đồng bộ Campaign</returns>
        private async Task<GmvMaxCampaignSyncResult> SyncCampaignsWithParametersAsync(GmvMaxSyncParameters parameters)
        {
            var result = new GmvMaxCampaignSyncResult();
            var advertiserIds = parameters.Advertisers.Select(x => x.AdvertiserId).ToList();

            foreach (var advertiser in parameters.Advertisers)
            {
                try
                {
                    _logger.LogDebug("Đồng bộ Campaign cho Advertiser: {AdvertiserId}", advertiser.AdvertiserId);
                    var singleResult = await _gmvMaxCampaignSyncService.SyncGmvMaxCampaignsAsync(advertiser.AdvertiserId, parameters.BcId);
                    
                    result.NewRecords += singleResult.NewRecords;
                    result.UpdatedRecords += singleResult.UpdatedRecords;
                    result.ErrorRecords += singleResult.ErrorRecords;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi đồng bộ Campaign cho Advertiser: {AdvertiserId}", advertiser.AdvertiserId);
                    result.ErrorRecords++;
                }
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Product Campaign Report với parameters
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Kết quả đồng bộ Product Campaign Report</returns>
        private async Task<GmvMaxProductCampaignSyncResult> SyncProductCampaignReportsWithParametersAsync(GmvMaxSyncParameters parameters)
        {
            var result = new GmvMaxProductCampaignSyncResult();

            try
            {
                // Sử dụng method hiện tại nhưng với TikTokClient từ parameters
                result = await _gmvMaxProductCampaignSyncService.SyncGmvMaxProductCampaignAsync(parameters.BcId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ Product Campaign Report với parameters cho BC: {BcId}", parameters.BcId);
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Product Detail Product Report với parameters
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Kết quả đồng bộ Product Detail Product Report</returns>
        private async Task<GmvMaxProductDetailProductSyncResult> SyncProductDetailProductReportsWithParametersAsync(GmvMaxSyncParameters parameters)
        {
            var result = new GmvMaxProductDetailProductSyncResult();

            try
            {
                // Sử dụng method hiện tại nhưng với TikTokClient từ parameters
                result = await _gmvMaxProductDetailProductSyncService.SyncGmvMaxProductDetailProductAsync(parameters.BcId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ Product Detail Product Report với parameters cho BC: {BcId}", parameters.BcId);
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Product Creative Report với parameters
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Kết quả đồng bộ Product Creative Report</returns>
        private async Task<GmvMaxProductCreativeSyncResult> SyncProductCreativeReportsWithParametersAsync(GmvMaxSyncParameters parameters)
        {
            var result = new GmvMaxProductCreativeSyncResult();

            try
            {
                // Sử dụng method hiện tại nhưng với TikTokClient từ parameters
                result = await _gmvMaxProductCreativeSyncService.SyncGmvMaxProductCreativeAsync(parameters.BcId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ Product Creative Report với parameters cho BC: {BcId}", parameters.BcId);
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Live Campaign Report với parameters
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Kết quả đồng bộ Live Campaign Report</returns>
        private async Task<GmvMaxLiveCampaignSyncResult> SyncLiveCampaignReportsWithParametersAsync(GmvMaxSyncParameters parameters)
        {
            var result = new GmvMaxLiveCampaignSyncResult();

            try
            {
                // Sử dụng method hiện tại nhưng với TikTokClient từ parameters
                result = await _gmvMaxLiveCampaignSyncService.SyncGmvMaxLiveCampaignAsync(parameters.BcId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ Live Campaign Report với parameters cho BC: {BcId}", parameters.BcId);
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Live Livestream Report với parameters
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Kết quả đồng bộ Live Livestream Report</returns>
        private async Task<GmvMaxLiveLivestreamSyncResult> SyncLiveLivestreamReportsWithParametersAsync(GmvMaxSyncParameters parameters)
        {
            var result = new GmvMaxLiveLivestreamSyncResult();

            try
            {
                // Sử dụng method hiện tại nhưng với TikTokClient từ parameters
                result = await _gmvMaxLiveLivestreamSyncService.SyncGmvMaxLiveLivestreamAsync(parameters.BcId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ Live Livestream Report với parameters cho BC: {BcId}", parameters.BcId);
                result.ErrorMessage = ex.Message;
            }

            return result;
        }
    }
} 