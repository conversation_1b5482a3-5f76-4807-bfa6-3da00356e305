@page
@model TikTok.Web.Pages.AdAccounts.IndexModel
@using TikTok.Enums
@using TikTok.Localization
@using TikTok.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IStringLocalizer<TikTokResource> L
@inject IAuthorizationService AuthorizationService
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["AdAccounts"].Value;
    PageLayout.Content.MenuItemName = "AdAccounts";
}

@section scripts {
    <script>
        const currentLang = localStorage.getItem('lpx:lang') === 'EN' ? 'en-US' : 'vi-VN';

        // Global status data for MultiSelect components
        window.statusesData = @Json.Serialize(Model.Statuses);
        window.users = @Json.Serialize(Model.Users);

        const syncfusionLocalization = {
            placeholder: currentLang === 'en-US' ? 'Select Status' : 'Chọn trạng thái',
            selectAllText: currentLang === 'en-US' ? 'Select All' : 'Chọn tất cả',
            unSelectAllText: currentLang === 'en-US' ? 'Unselect All' : 'Bỏ chọn tất cả',
            filterBarPlaceholder: currentLang === 'en-US' ? 'Search Status' : 'Tìm kiếm trạng thái',
        }
        // Initialize Basic Status Filter
        window.basicStatusMultiSelect = new ej.dropdowns.MultiSelect({
            dataSource: window.statusesData,
            fields: { text: 'label', value: 'value' },
            mode: 'CheckBox',
            placeholder: syncfusionLocalization.placeholder,
            showSelectAll: true,
            selectAllText: syncfusionLocalization.selectAllText,
            unSelectAllText: syncfusionLocalization.unSelectAllText,
            popupHeight: '300px',
            allowFiltering: true,
            filterBarPlaceholder: syncfusionLocalization.filterBarPlaceholder,
            width: '100%',
            locale: currentLang
        });
        window.basicStatusMultiSelect.appendTo('#basic-status-filter');

        // Initialize Advanced Status Filter
        window.advancedStatusMultiSelect = new ej.dropdowns.MultiSelect({
            dataSource: window.statusesData,
            fields: { text: 'label', value: 'value' },
            mode: 'CheckBox',
            placeholder: syncfusionLocalization.placeholder,
            showSelectAll: true,
            selectAllText: syncfusionLocalization.selectAllText,
            unSelectAllText: syncfusionLocalization.unSelectAllText,
            popupHeight: '300px',
            allowFiltering: true,
            filterBarPlaceholder: syncfusionLocalization.filterBarPlaceholder,
            width: '100%',
            locale: currentLang
        });
        window.advancedStatusMultiSelect.appendTo('#advanced-status-filter');

        ej.base.L10n.load({
            'vi-VN': {
                'multi-select': {
                    'noRecordsTemplate': "Không có dữ liệu",
                    'actionFailureTemplate': "Lỗi"
                }
            }
        });
    </script>
    <abp-script src="/Pages/AdAccounts/Index.js" />
    <abp-script src="/Pages/AdAccounts/ConfigAdAccountRules.js" />
}

@section styles {
    <link rel="stylesheet" href="/Pages/AdAccounts/adAccounts.css" />
}

<abp-card>
    <abp-card-header class="pt-2">
        <abp-row>
            <abp-column size-md="_6">
                <abp-card-title>@L["AdAccounts"]</abp-card-title>
            </abp-column>
            <abp-column size-md="_6" class="text-end">
                @if (await AuthorizationService.IsGrantedAsync(TikTokPermissions.AdAccounts.Create))
                {
                    <abp-button id="NewAdAccountButton" text="@L["NewAdAccount"].Value" icon="plus" button-type="Primary" />
                }
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body class="pt-2">
        <abp-row>
            <abp-column size-md="_12">
                <form id="SearchForm" autocomplete="off">
                    <!-- Basic Search -->
                    <div class="input-group mb-1">
                        <input class="form-control me-1" id="FilterText" name="FilterText"
                            placeholder="@L["SearchPlaceholder"]" />
                        <input class="form-control" id="CustomerFilterText" name="CustomerFilterText"
                            placeholder="@L["SearchByCustomer"]" />
                        <div class="filter-status-container">
                            <div id="basic-status-filter" style="max-width:200px; min-width:150px;"></div>
                        </div>
                        <abp-button button-type="Primary" type="submit" icon="search" />
                        <abp-button button-type="Secondary" type="button" id="AdvancedSearchToggle" icon="filter" />
                        <div class="dropdown">
                            <abp-button button-type="Info" class="rounded-0 rounded-end" type="button"
                                id="ColumnVisibilityToggle" icon="columns" data-bs-toggle="dropdown"
                                aria-expanded="false" />
                            <ul class="dropdown-menu dropdown-menu-end" id="ColumnVisibilityMenu">
                                <li>
                                    <h6 class="dropdown-header">@L["ColumnVisibility"]</h6>
                                </li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="0"><i
                                            class="fa fa-check"></i> @L["AdAccount:OwnerBcId"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="1"><i
                                            class="fa fa-check"></i> @L["AdvertiserId"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="2"><i
                                            class="fa fa-check"></i> @L["AdAccount:Name"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="3"><i
                                            class="fa fa-check"></i> @L["Company"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="4"><i
                                            class="fa fa-check"></i> @L["Status"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="5"><i
                                            class="fa fa-check"></i> @L["AdAccount:Role"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="6"><i
                                            class="fa fa-check"></i> @L["AdAccount:Country"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="7"><i
                                            class="fa fa-check"></i> @L["Currency"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="8"><i
                                            class="fa fa-check"></i> @L["Balance"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="9"><i
                                            class="fa fa-check"></i> @L["Customer:CustomerId"]</a></li>
                                <li><a class="dropdown-item column-toggle" href="#" data-column="10"><i
                                            class="fa fa-check"></i> @L["Customer:CustomerName"]</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="#" id="ShowAllColumns"><i class="fa fa-eye"></i>
                                        @L["ShowAllColumns"]</a></li>
                                <li><a class="dropdown-item" href="#" id="HideAllColumns"><i
                                            class="fa fa-eye-slash"></i> @L["HideAllColumns"]</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Advanced Search -->
                    <div id="AdvancedSearchPanel" class="card mb-1" style="display: none;">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="AdvertiserIdFilter" class="form-label">@L["AdvertiserId"]</label>
                                    <input type="text" class="form-control" id="AdvertiserIdFilter"
                                        name="AdvertiserIdFilter" placeholder="@L["SearchByAdvertiserId"]" />
                                </div>
                                <div class="col-md-3">
                                    <label for="OwnerBcIdFilter" class="form-label">@L["AdAccount:OwnerBcId"]</label>
                                    <input type="text" class="form-control" id="OwnerBcIdFilter" name="OwnerBcIdFilter"
                                        placeholder="@L["SearchByOwnerBcId"]" />
                                </div>
                                <div class="col-md-3">
                                    <label for="NameFilter" class="form-label">@L["AdAccount:Name"]</label>
                                    <input type="text" class="form-control" id="NameFilter" name="NameFilter"
                                        placeholder="@L["SearchByName"]" />
                                </div>
                                <div class="col-md-3">
                                    <label for="CompanyFilter" class="form-label">@L["Company"]</label>
                                    <input type="text" class="form-control" id="CompanyFilter" name="CompanyFilter"
                                        placeholder="@L["SearchByCompany"]" />
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-3">
                                    <label for="IndustryFilter" class="form-label">@L["AdAccount:Industry"]</label>
                                    <input type="text" class="form-control" id="IndustryFilter" name="IndustryFilter"
                                        placeholder="@L["SearchByIndustry"]" />
                                </div>
                                <div class="col-md-3">
                                    <label for="AddressFilter" class="form-label">@L["AdAccount:Address"]</label>
                                    <input type="text" class="form-control" id="AddressFilter" name="AddressFilter"
                                        placeholder="@L["SearchByAddress"]" />
                                </div>
                                <div class="col-md-3">
                                    <label for="CountryFilter" class="form-label">@L["AdAccount:Country"]</label>
                                    <input type="text" class="form-control" id="CountryFilter" name="CountryFilter"
                                        placeholder="@L["SearchByCountry"]" />
                                </div>
                                <div class="col-md-3">
                                    <label for="CurrencyFilter" class="form-label">@L["Currency"]</label>
                                    <input type="text" class="form-control" id="CurrencyFilter" name="CurrencyFilter"
                                        placeholder="@L["SearchByCurrency"]" />
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="StatusFilter" class="form-label">@L["Status"]</label>
                                    <div id="advanced-status-filter"></div>
                                </div>
                                <div class="col-md-6 d-flex align-items-end justify-content-end">
                                    <div class="d-flex gap-2">
                                        <abp-button button-type="Primary" type="button" id="AdvancedSearchBtn"
                                            icon="search" text="@L["Search"]" />
                                        <abp-button button-type="Secondary" type="button" id="ClearAdvancedSearchBtn"
                                            icon="times" text="@L["Clear"]" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </abp-column>
        </abp-row>

        <!-- Active Filter Tags -->
        <div id="ActiveFilterTags" class="mb-1" style="display: none;">
            <div class="d-flex align-items-center flex-wrap gap-2">
                <span class="text-muted small">@L["ActiveFilters"]:</span>
                <div id="FilterTagsContainer" class="d-flex flex-wrap gap-1">
                    <!-- Filter tags will be dynamically added here -->
                </div>
                <button type="button" id="ClearAllFiltersBtn" class="btn btn-sm btn-outline-secondary">
                    <i class="fa fa-times"></i> @L["ClearAllFilters"]
                </button>
            </div>
        </div>

        <div class="table-container">
            <abp-table striped-rows="true" id="AdAccountsTable" class="nowrap">
                <thead>
                    <tr>
                        <th data-column="0">@L["AdAccount:OwnerBcId"]</th>
                        <th data-column="1">@L["AdvertiserId"]</th>
                        <th data-column="2">@L["AdAccount:Name"]</th>
                        <th data-column="3">@L["Company"]</th>
                        <th data-column="4">@L["Status"]</th>
                        <th data-column="5">@L["AdAccount:Role"]</th>
                        <th data-column="6">@L["Asset:RelationType"]</th>
                        <th data-column="7">@L["AdAccount:Country"]</th>
                        <th data-column="8">@L["Currency"]</th>
                        <th data-column="9">@L["Balance"]</th>
                        <th data-column="10">@L["Customer:CustomerId"]</th>
                        <th data-column="11">@L["Customer:CustomerName"]</th>
                        <th data-column="12">@L["Actions"]</th>
                    </tr>
                </thead>
            </abp-table>
        </div>
    </abp-card-body>
</abp-card>

<!-- Supporter Configuration Modal -->
<abp-modal id="SupporterConfigModal" size="ExtraLarge">
    <abp-modal-header title="@L["SupporterConfiguration"].Value"></abp-modal-header>
    <abp-modal-body>
        <div class="row mt-3">
            @if (!(await AuthorizationService.IsGrantedAsync(TikTokPermissions.AdAccounts.AssignSupport)))
            {
                <div class="col">
                    <h6>@L["AssignedSupporters"]</h6>
                    <div class="mb-2">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" id="AssignedSupportersSearch" class="form-control"
                                placeholder="@L["SearchByNameEmailPhone"]" />
                        </div>
                    </div>
                    <div id="AssignedSupportersList" class="list-group">
                        <!-- Assigned supporters will be loaded here -->
                    </div>
                </div>
            }
            else
            {
                <div class="col-md-6 overflow-y-auto">
                    <h6>@L["AvailableSupporters"]</h6>
                    <div class="mb-2">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" id="AvailableSupportersSearch" class="form-control"
                                placeholder="@L["SearchByNameEmailPhone"]" />
                        </div>
                    </div>
                    <div id="AvailableSupportersList" class="list-group">
                        <!-- Available supporters will be loaded here -->
                    </div>
                </div>
                <div class="col-md-6 overflow-y-auto">
                    <h6>@L["AssignedSupporters"]</h6>
                    <div class="mb-2">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" id="AssignedSupportersSearch" class="form-control"
                                placeholder="@L["SearchByNameEmailPhone"]" />
                        </div>
                    </div>
                    <div id="AssignedSupportersList" class="list-group">
                        <!-- Assigned supporters will be loaded here -->
                    </div>
                </div>
            }
        </div>
    </abp-modal-body>
    <abp-modal-footer>
        <abp-button button-type="Secondary" data-bs-dismiss="modal">@L["Cancel"]</abp-button>
        <abp-button button-type="Primary" id="SaveSupporterConfig">@L["Save"]</abp-button>
    </abp-modal-footer>
</abp-modal>

<!-- Rule Notification Configuration Modal -->
<abp-modal id="RuleNotificationModal" size="ExtraLarge">
    <abp-modal-header title="@L["RuleNotificationConfiguration"].Value"></abp-modal-header>
    <abp-modal-body>
        <div class="mt-3">
            <div id="rule-notification">
                <!-- Rule notification configuration will be loaded here -->
            </div>
        </div>
    </abp-modal-body>
    <abp-modal-footer>
        <abp-button button-type="Secondary" data-bs-dismiss="modal">@L["Cancel"]</abp-button>
        <abp-button button-type="Primary" id="SaveRuleNotificationConfig">@L["Save"]</abp-button>
    </abp-modal-footer>
</abp-modal>

<!-- Column Settings Modal -->
<abp-modal id="ColumnSettingsModal">
    <abp-modal-header title="@L["ColumnSettings"].Value"></abp-modal-header>
    <abp-modal-body>
        <div class="row">
            <div class="col-md-6">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colBcId" checked disabled>
                    <label class="form-check-label" for="colBcId">@L["AdAccount:OwnerBcId"]</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colAdvertiserId" checked disabled>
                    <label class="form-check-label" for="colAdvertiserId">@L["AdvertiserId"]</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colName" checked>
                    <label class="form-check-label" for="colName">@L["AdAccount:Name"]</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colCompany" checked>
                    <label class="form-check-label" for="colCompany">@L["Company"]</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colStatus">
                    <label class="form-check-label" for="colStatus">@L["Status"]</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colRole">
                    <label class="form-check-label" for="colRole">@L["AdAccount:Role"]</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colCountry">
                    <label class="form-check-label" for="colCountry">@L["AdAccount:Country"]</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colCurrency">
                    <label class="form-check-label" for="colCurrency">@L["Currency"]</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="colBalance">
                    <label class="form-check-label" for="colBalance">@L["Balance"]</label>
                </div>
            </div>
        </div>
    </abp-modal-body>
    <abp-modal-footer>
        <abp-button button-type="Secondary" data-bs-dismiss="modal">@L["Cancel"]</abp-button>
        <abp-button button-type="Primary" id="SaveColumnSettings">@L["Save"]</abp-button>
    </abp-modal-footer>
</abp-modal>