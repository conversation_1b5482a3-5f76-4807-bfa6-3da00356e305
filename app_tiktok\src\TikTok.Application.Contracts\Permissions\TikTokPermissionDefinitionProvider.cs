using TikTok.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace TikTok.Permissions;

public class TikTokPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(TikTokPermissions.GroupName, L("Permission:TikTok"));

        var businessCenterPermission = myGroup.AddPermission(TikTokPermissions.BusinessCenters.Default, L("Permission:BusinessCenters"));
        businessCenterPermission.AddChild(TikTokPermissions.BusinessCenters.Create, L("Permission:BusinessCenters.Create"));
        businessCenterPermission.AddChild(TikTokPermissions.BusinessCenters.Edit, L("Permission:BusinessCenters.Edit"));
        businessCenterPermission.AddChild(TikTokPermissions.BusinessCenters.Delete, L("Permission:BusinessCenters.Delete"));

        var adAccountPermission = myGroup.AddPermission(TikTokPermissions.AdAccounts.Default, L("Permission:AdAccounts"));
        adAccountPermission.AddChild(TikTokPermissions.AdAccounts.Create, L("Permission:AdAccounts.Create"));
        adAccountPermission.AddChild(TikTokPermissions.AdAccounts.Edit, L("Permission:AdAccounts.Edit"));
        adAccountPermission.AddChild(TikTokPermissions.AdAccounts.Delete, L("Permission:AdAccounts.Delete"));
        adAccountPermission.AddChild(TikTokPermissions.AdAccounts.AssignSupport, L("Permission:AdAccounts.AssignSupport"));
        adAccountPermission.AddChild(TikTokPermissions.AdAccounts.Supervise, L("Permission:AdAccounts.Supervise"));

        var adAccountSupportPermission = myGroup.AddPermission(TikTokPermissions.AdAccountSupporters.Default, L("Permission:AdAccountSupporters"));
        adAccountSupportPermission.AddChild(TikTokPermissions.AdAccountSupporters.Create, L("Permission:AdAccountSupporters.Create"));
        adAccountSupportPermission.AddChild(TikTokPermissions.AdAccountSupporters.Edit, L("Permission:AdAccountSupporters.Edit"));
        adAccountSupportPermission.AddChild(TikTokPermissions.AdAccountSupporters.Delete, L("Permission:AdAccountSupporters.Delete"));

        var assetPermission = myGroup.AddPermission(TikTokPermissions.Assets.Default, L("Permission:Assets"));
        assetPermission.AddChild(TikTokPermissions.Assets.Create, L("Permission:Assets.Create"));
        assetPermission.AddChild(TikTokPermissions.Assets.Edit, L("Permission:Assets.Edit"));
        assetPermission.AddChild(TikTokPermissions.Assets.Delete, L("Permission:Assets.Delete"));

        var transactionPermission = myGroup.AddPermission(TikTokPermissions.Transactions.Default, L("Permission:Transactions"));
        transactionPermission.AddChild(TikTokPermissions.Transactions.Create, L("Permission:Transactions.Create"));
        transactionPermission.AddChild(TikTokPermissions.Transactions.Edit, L("Permission:Transactions.Edit"));
        transactionPermission.AddChild(TikTokPermissions.Transactions.Delete, L("Permission:Transactions.Delete"));
        transactionPermission.AddChild(TikTokPermissions.Transactions.Sync, L("Permission:Transactions.Sync"));

        var businessApplicationPermission = myGroup.AddPermission(TikTokPermissions.BusinessApplications.Default, L("Permission:BusinessApplications"));
        businessApplicationPermission.AddChild(TikTokPermissions.BusinessApplications.Create, L("Permission:BusinessApplications.Create"));
        businessApplicationPermission.AddChild(TikTokPermissions.BusinessApplications.Edit, L("Permission:BusinessApplications.Edit"));
        businessApplicationPermission.AddChild(TikTokPermissions.BusinessApplications.Delete, L("Permission:BusinessApplications.Delete"));

        var balanceBusinessCenterPermission = myGroup.AddPermission(TikTokPermissions.BalanceBusinessCenters.Default, L("Permission:BalanceBusinessCenters"));
        balanceBusinessCenterPermission.AddChild(TikTokPermissions.BalanceBusinessCenters.Create, L("Permission:BalanceBusinessCenters.Create"));
        balanceBusinessCenterPermission.AddChild(TikTokPermissions.BalanceBusinessCenters.Edit, L("Permission:BalanceBusinessCenters.Edit"));
        balanceBusinessCenterPermission.AddChild(TikTokPermissions.BalanceBusinessCenters.Delete, L("Permission:BalanceBusinessCenters.Delete"));

        var balanceAdAccountPermission = myGroup.AddPermission(TikTokPermissions.BalanceAdAccounts.Default, L("Permission:BalanceAdAccounts"));
        balanceAdAccountPermission.AddChild(TikTokPermissions.BalanceAdAccounts.Create, L("Permission:BalanceAdAccounts.Create"));
        balanceAdAccountPermission.AddChild(TikTokPermissions.BalanceAdAccounts.Edit, L("Permission:BalanceAdAccounts.Edit"));
        balanceAdAccountPermission.AddChild(TikTokPermissions.BalanceAdAccounts.Delete, L("Permission:BalanceAdAccounts.Delete"));

        var recordTransactionAdAccountPermission = myGroup.AddPermission(TikTokPermissions.RecordTransactionAdAccounts.Default, L("Permission:RecordTransactionAdAccounts"));
        recordTransactionAdAccountPermission.AddChild(TikTokPermissions.RecordTransactionAdAccounts.Create, L("Permission:RecordTransactionAdAccounts.Create"));
        recordTransactionAdAccountPermission.AddChild(TikTokPermissions.RecordTransactionAdAccounts.Edit, L("Permission:RecordTransactionAdAccounts.Edit"));
        recordTransactionAdAccountPermission.AddChild(TikTokPermissions.RecordTransactionAdAccounts.Delete, L("Permission:RecordTransactionAdAccounts.Delete"));

        var recordTransactionBcPermission = myGroup.AddPermission(TikTokPermissions.RecordTransactionBcs.Default, L("Permission:RecordTransactionBcs"));
        recordTransactionBcPermission.AddChild(TikTokPermissions.RecordTransactionBcs.Create, L("Permission:RecordTransactionBcs.Create"));
        recordTransactionBcPermission.AddChild(TikTokPermissions.RecordTransactionBcs.Edit, L("Permission:RecordTransactionBcs.Edit"));
        recordTransactionBcPermission.AddChild(TikTokPermissions.RecordTransactionBcs.Delete, L("Permission:RecordTransactionBcs.Delete"));

        var jobManagementPermission = myGroup.AddPermission(TikTokPermissions.JobManagement.Default, L("Permission:JobManagement"));
        jobManagementPermission.AddChild(TikTokPermissions.JobManagement.Create, L("Permission:JobManagement.Create"));
        jobManagementPermission.AddChild(TikTokPermissions.JobManagement.Edit, L("Permission:JobManagement.Edit"));
        jobManagementPermission.AddChild(TikTokPermissions.JobManagement.Delete, L("Permission:JobManagement.Delete"));

        var costProfilePermission = myGroup.AddPermission(TikTokPermissions.CostProfiles.Default, L("Permission:CostProfiles"));
        costProfilePermission.AddChild(TikTokPermissions.CostProfiles.Create, L("Permission:CostProfiles.Create"));
        costProfilePermission.AddChild(TikTokPermissions.CostProfiles.Edit, L("Permission:CostProfiles.Edit"));
        costProfilePermission.AddChild(TikTokPermissions.CostProfiles.Delete, L("Permission:CostProfiles.Delete"));
        costProfilePermission.AddChild(TikTokPermissions.CostProfiles.Sync, L("Permission:CostProfiles.Sync"));

        var reportIntegratedBcPermission = myGroup.AddPermission(TikTokPermissions.ReportIntegratedBcs.Default, L("Permission:ReportIntegratedBcs"));
        reportIntegratedBcPermission.AddChild(TikTokPermissions.ReportIntegratedBcs.Create, L("Permission:ReportIntegratedBcs.Create"));
        reportIntegratedBcPermission.AddChild(TikTokPermissions.ReportIntegratedBcs.Edit, L("Permission:ReportIntegratedBcs.Edit"));
        reportIntegratedBcPermission.AddChild(TikTokPermissions.ReportIntegratedBcs.Delete, L("Permission:ReportIntegratedBcs.Delete"));

        var campaignPermission = myGroup.AddPermission(TikTokPermissions.Campaigns.Default, L("Permission:Campaigns"));
        campaignPermission.AddChild(TikTokPermissions.Campaigns.Create, L("Permission:Campaigns.Create"));
        campaignPermission.AddChild(TikTokPermissions.Campaigns.Edit, L("Permission:Campaigns.Edit"));
        campaignPermission.AddChild(TikTokPermissions.Campaigns.Delete, L("Permission:Campaigns.Delete"));

        var reportIntegratedAdAccountPermission = myGroup.AddPermission(TikTokPermissions.ReportIntegratedAdAccounts.Default, L("Permission:ReportIntegratedAdAccounts"));
        reportIntegratedAdAccountPermission.AddChild(TikTokPermissions.ReportIntegratedAdAccounts.Create, L("Permission:ReportIntegratedAdAccounts.Create"));
        reportIntegratedAdAccountPermission.AddChild(TikTokPermissions.ReportIntegratedAdAccounts.Edit, L("Permission:ReportIntegratedAdAccounts.Edit"));
        reportIntegratedAdAccountPermission.AddChild(TikTokPermissions.ReportIntegratedAdAccounts.Delete, L("Permission:ReportIntegratedAdAccounts.Delete"));

        var reportIntegratedAdGroupPermission = myGroup.AddPermission(TikTokPermissions.ReportIntegratedAdGroups.Default, L("Permission:ReportIntegratedAdGroups"));
        reportIntegratedAdGroupPermission.AddChild(TikTokPermissions.ReportIntegratedAdGroups.Create, L("Permission:ReportIntegratedAdGroups.Create"));
        reportIntegratedAdGroupPermission.AddChild(TikTokPermissions.ReportIntegratedAdGroups.Edit, L("Permission:ReportIntegratedAdGroups.Edit"));
        reportIntegratedAdGroupPermission.AddChild(TikTokPermissions.ReportIntegratedAdGroups.Delete, L("Permission:ReportIntegratedAdGroups.Delete"));

        var factBalancePermission = myGroup.AddPermission(TikTokPermissions.FactBalances.Default, L("Permission:FactBalancePermission"));

        var customerPermission = myGroup.AddPermission(TikTokPermissions.Customers.Default, L("Permission:Customers"));
        customerPermission.AddChild(TikTokPermissions.Customers.Create, L("Permission:Customers.Create"));
        customerPermission.AddChild(TikTokPermissions.Customers.Edit, L("Permission:Customers.Edit"));
        customerPermission.AddChild(TikTokPermissions.Customers.Delete, L("Permission:Customers.Delete"));
        customerPermission.AddChild(TikTokPermissions.Customers.Import, L("Permission:Customers.Import"));

        var systemCachePermissionGroup = context.AddGroup(TikTokPermissions.SystemCache.Default, L("Permission:SystemCache"));
        var systemCachePermission = systemCachePermissionGroup.AddPermission(TikTokPermissions.SystemCache.Default, L("Permission:SystemCache"));
        systemCachePermission.AddChild(TikTokPermissions.SystemCache.Monitor, L("Permission:SystemCache.Monitor"));
        systemCachePermission.AddChild(TikTokPermissions.SystemCache.Clear, L("Permission:SystemCache.Clear"));
        systemCachePermission.AddChild(TikTokPermissions.SystemCache.ClearAll, L("Permission:SystemCache.ClearAll"));

        // Notification rules
        var groupRule = context.AddGroup(TikTokPermissions.NotificationRules.Default, L("Permission:NotificationRules"));

        var notificationRulePermission = groupRule.AddPermission(TikTokPermissions.NotificationRules.Default, L("Permission:NotificationRules"));
        notificationRulePermission.AddChild(TikTokPermissions.NotificationRules.Create, L("Permission:NotificationRules.Create"));
        notificationRulePermission.AddChild(TikTokPermissions.NotificationRules.Edit, L("Permission:NotificationRules.Edit"));
        notificationRulePermission.AddChild(TikTokPermissions.NotificationRules.Delete, L("Permission:NotificationRules.Delete"));
        notificationRulePermission.AddChild(TikTokPermissions.NotificationRules.ViewAll, L("Permission:NotificationRules.ViewAll"));
        notificationRulePermission.AddChild(TikTokPermissions.NotificationRules.UseCommonRule, L("Permission:NotificationRules.UseCommonRule"));
        notificationRulePermission.AddChild(TikTokPermissions.NotificationRules.ManageRule, L("Permission:NotificationRules.ManageRule"));
        // Receive Notification
        var receiveNotificationPermission = groupRule.AddPermission(TikTokPermissions.ReceiveNotification.Default, L("Permission:ReceiveNotification"));

        // Support Management
        var supportManagementPermission = myGroup.AddPermission(TikTokPermissions.SupportManagement.Default, L("Permission:SupportManagement"));
        supportManagementPermission.AddChild(TikTokPermissions.SupportManagement.Import, L("Permission:SupportManagement.Import"));

        // Ad Account Permission Management
        var adAccountPermissionManagementPermission = myGroup.AddPermission(TikTokPermissions.AdAccountPermissionManagement.Default, L("Permission:AdAccountPermissionManagement"));

        // GMVMax - Unified Permission Group
        var gmvMaxGroup = context.AddGroup(TikTokPermissions.FactGmvMax.Default, L("Permission:FactGmvMax"));
        var factGmvMaxPermission = gmvMaxGroup.AddPermission(TikTokPermissions.FactGmvMax.Default, L("Permission:FactGmvMax"));
        factGmvMaxPermission.AddChild(TikTokPermissions.FactGmvMax.ViewSpending, L("Permission:FactGmvMax.ViewSpending"));
        factGmvMaxPermission.AddChild(TikTokPermissions.FactGmvMax.ViewMetrics, L("Permission:FactGmvMax.ViewMetrics"));
        factGmvMaxPermission.AddChild(TikTokPermissions.FactGmvMax.ViewAll, L("Permission:FactGmvMax.ViewAll"));
        factGmvMaxPermission.AddChild(TikTokPermissions.FactGmvMax.ViewVideo, L("Permission:FactGmvMax.ViewVideo"));
        factGmvMaxPermission.AddChild(TikTokPermissions.FactGmvMax.ViewAllAdvertisers, L("Permission:FactGmvMax.ViewAllAdvertisers"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<TikTokResource>(name);
    }
}
