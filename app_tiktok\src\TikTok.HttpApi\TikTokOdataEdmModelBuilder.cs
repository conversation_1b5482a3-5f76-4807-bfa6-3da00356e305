using TikTok.Entities;

namespace Microsoft.OData.ModelBuilder
{
    public static class TikTokOdataEdmModelBuilder
    {
        public static ODataConventionModelBuilder AddTikTokEntities(this ODataConventionModelBuilder builder)
        {
            // Raw GMV Max Product Creative Reports
            var creativeSet = builder.EntitySet<RawGmvMaxProductCreativeReportEntity>("RawGmvMaxProductCreativeReports");
            creativeSet.EntityType.HasKey(x => x.Id);

            return builder;
        }
    }
}


