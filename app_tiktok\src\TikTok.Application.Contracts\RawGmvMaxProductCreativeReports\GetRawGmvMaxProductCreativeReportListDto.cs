using System;
using System.Collections.Generic;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.RawGmvMaxProductCreativeReports
{
    /// <summary>
    /// DTO cho việc lấy danh sách RawGmvMaxProductCreativeReport với phân trang và lọc
    /// </summary>
    public class GetRawGmvMaxProductCreativeReportListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Tìm kiếm theo text (Title, CampaignId, ItemGroupId, ItemId)
        /// </summary>
        public string? SearchText { get; set; }

        /// <summary>
        /// Lọc từ ngày
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// Lọc đến ngày
        /// </summary>
        public DateTime? ToDate { get; set; }

        /// <summary>
        /// Lọc theo Campaign ID (có thể truyền qua URL)
        /// </summary>
        public string? CampaignId { get; set; }

        /// <summary>
        /// Lọc theo nhiều Campaign IDs (hỗ trợ MultiSelect), truyền ?CampaignIds=a&CampaignIds=b
        /// hoặc ?campaignIds=a,b,c
        /// </summary>
        public List<string>? CampaignIds { get; set; }

        /// <summary>
        /// Lọc theo loại nội dung shop (VIDEO, PRODUCT_CARD, LIVE)
        /// </summary>
        public List<ShopContentType>? ShopContentTypes { get; set; }

        /// <summary>
        /// Lọc theo trạng thái giao hàng creative
        /// </summary>
        public List<CreativeDeliveryStatus>? CreativeDeliveryStatuses { get; set; }
    }
}
