$(function () {
    var l = abp.localization.getResource('TikTok');
    var adAccountService = tikTok.adAccounts.adAccount;
    var editModal = new abp.ModalManager(abp.appPath + 'AdAccounts/EditModal');
    window.editModal = editModal;

    // Global variables to store supporters data
    window.supportersData = {
        available: [],
        assigned: [],
    };

    // Helper to get query string param
    function getQueryParam(name) {
        const url = window.location.search;
        const params = new URLSearchParams(url);
        return params.get(name);
    }

    // Set filter from query string if present
    var statusFromQuery = getQueryParam('StatusFilter');
    var filterTextFromQuery = getQueryParam('FilterText');
    var customerFilterTextFromQuery = getQueryParam('CustomerFilterText');
    var advertiserIdFromQuery = getQueryParam('AdvertiserIdFilter');
    var ownerBcIdFromQuery = getQueryParam('OwnerBcIdFilter');
    var nameFromQuery = getQueryParam('NameFilter');
    var companyFromQuery = getQueryParam('CompanyFilter');
    var industryFromQuery = getQueryParam('IndustryFilter');
    var addressFromQuery = getQueryParam('AddressFilter');
    var countryFromQuery = getQueryParam('CountryFilter');
    var currencyFromQuery = getQueryParam('CurrencyFilter');

    if (statusFromQuery !== null) {
        // For multiselect, set value after component is initialized
        setTimeout(function () {
            var statusValues = statusFromQuery.split(',').map((s) => s.trim());
            if (window.basicStatusMultiSelect) {
                window.basicStatusMultiSelect.value = statusValues;
            }
            if (window.advancedStatusMultiSelect) {
                window.advancedStatusMultiSelect.value = statusValues;
            }
        }, 500);
        localStorage.setItem('adAccountStatus', statusFromQuery);
    }

    if (filterTextFromQuery !== null) {
        $('#FilterText').val(filterTextFromQuery);
        localStorage.setItem('adAccountFilterText', filterTextFromQuery);
    }

    if (customerFilterTextFromQuery !== null) {
        $('#CustomerFilterText').val(customerFilterTextFromQuery);
        localStorage.setItem('adAccountCustomerFilterText', customerFilterTextFromQuery);
    }

    if (advertiserIdFromQuery !== null) {
        $('#AdvertiserIdFilter').val(advertiserIdFromQuery);
        localStorage.setItem('adAccountAdvertiserId', advertiserIdFromQuery);
    }

    if (ownerBcIdFromQuery !== null) {
        $('#OwnerBcIdFilter').val(ownerBcIdFromQuery);
        localStorage.setItem('adAccountOwnerBcId', ownerBcIdFromQuery);
    }

    if (nameFromQuery !== null) {
        $('#NameFilter').val(nameFromQuery);
        localStorage.setItem('adAccountName', nameFromQuery);
    }

    if (companyFromQuery !== null) {
        $('#CompanyFilter').val(companyFromQuery);
        localStorage.setItem('adAccountCompany', companyFromQuery);
    }

    if (industryFromQuery !== null) {
        $('#IndustryFilter').val(industryFromQuery);
        localStorage.setItem('adAccountIndustry', industryFromQuery);
    }

    if (addressFromQuery !== null) {
        $('#AddressFilter').val(addressFromQuery);
        localStorage.setItem('adAccountAddress', addressFromQuery);
    }

    if (countryFromQuery !== null) {
        $('#CountryFilter').val(countryFromQuery);
        localStorage.setItem('adAccountCountry', countryFromQuery);
    }

    if (currencyFromQuery !== null) {
        $('#CurrencyFilter').val(currencyFromQuery);
        localStorage.setItem('adAccountCurrency', currencyFromQuery);
    }

    // Load saved search parameters from localStorage
    function loadSavedSearchParams() {
        var savedFilterText = localStorage.getItem('adAccountFilterText');
        var savedCustomerFilterText = localStorage.getItem('adAccountCustomerFilterText');
        var savedStatus = localStorage.getItem('adAccountStatus');
        var savedAdvertiserId = localStorage.getItem('adAccountAdvertiserId');
        var savedOwnerBcId = localStorage.getItem('adAccountOwnerBcId');
        var savedName = localStorage.getItem('adAccountName');
        var savedCompany = localStorage.getItem('adAccountCompany');
        var savedIndustry = localStorage.getItem('adAccountIndustry');
        var savedAddress = localStorage.getItem('adAccountAddress');
        var savedCountry = localStorage.getItem('adAccountCountry');
        var savedCurrency = localStorage.getItem('adAccountCurrency');

        if (savedFilterText && filterTextFromQuery === null) {
            $('#FilterText').val(savedFilterText);
        }
        if (savedCustomerFilterText && customerFilterTextFromQuery === null) {
            $('#CustomerFilterText').val(savedCustomerFilterText);
        }
        if (savedStatus && statusFromQuery === null) {
            setTimeout(function () {
                var statusValues = savedStatus.split(',').map((s) => s.trim());
                if (window.basicStatusMultiSelect) {
                    window.basicStatusMultiSelect.value = statusValues;
                }
                if (window.advancedStatusMultiSelect) {
                    window.advancedStatusMultiSelect.value = statusValues;
                }
            }, 500);
        }
        if (savedAdvertiserId && advertiserIdFromQuery === null) {
            $('#AdvertiserIdFilter').val(savedAdvertiserId);
        }
        if (savedOwnerBcId && ownerBcIdFromQuery === null) {
            $('#OwnerBcIdFilter').val(savedOwnerBcId);
        }
        if (savedName && nameFromQuery === null) {
            $('#NameFilter').val(savedName);
        }
        if (savedCompany && companyFromQuery === null) {
            $('#CompanyFilter').val(savedCompany);
        }
        if (savedIndustry && industryFromQuery === null) {
            $('#IndustryFilter').val(savedIndustry);
        }
        if (savedAddress && addressFromQuery === null) {
            $('#AddressFilter').val(savedAddress);
        }
        if (savedCountry && countryFromQuery === null) {
            $('#CountryFilter').val(savedCountry);
        }
        if (savedCurrency && currencyFromQuery === null) {
            $('#CurrencyFilter').val(savedCurrency);
        }

        // Update filter tags after loading saved parameters
        updateFilterTags();
    }

    // Get value from Status MultiSelectList
    function getStatusFilterValue() {
        return window.basicStatusMultiSelect?.value || [];
    }

    // Save search parameters to localStorage and update URL
    function saveSearchParams() {
        var filterText = $('#FilterText').val();
        var customerFilterText = $('#CustomerFilterText').val();
        customerFilterText = customerFilterText.trim();
        var status = getStatusFilterValue().join(',');
        var advertiserId = $('#AdvertiserIdFilter').val();
        var ownerBcId = $('#OwnerBcIdFilter').val();
        var name = $('#NameFilter').val();
        var company = $('#CompanyFilter').val();
        var industry = $('#IndustryFilter').val();
        var address = $('#AddressFilter').val();
        var country = $('#CountryFilter').val();
        var currency = $('#CurrencyFilter').val();

        localStorage.setItem('adAccountFilterText', filterText);
        localStorage.setItem('adAccountCustomerFilterText', customerFilterText);
        localStorage.setItem('adAccountStatus', status);
        localStorage.setItem('adAccountAdvertiserId', advertiserId);
        localStorage.setItem('adAccountOwnerBcId', ownerBcId);
        localStorage.setItem('adAccountName', name);
        localStorage.setItem('adAccountCompany', company);
        localStorage.setItem('adAccountIndustry', industry);
        localStorage.setItem('adAccountAddress', address);
        localStorage.setItem('adAccountCountry', country);
        localStorage.setItem('adAccountCurrency', currency);

        // Update URL with query parameters
        updateUrlWithSearchParams(
            filterText,
            customerFilterText,
            status,
            advertiserId,
            ownerBcId,
            name,
            company,
            industry,
            address,
            country,
            currency
        );

        // Update filter tags
        updateFilterTags();
    }

    // Update URL with search parameters
    function updateUrlWithSearchParams(
        filterText,
        customerFilterText,
        status,
        advertiserId,
        ownerBcId,
        name,
        company,
        industry,
        address,
        country,
        currency
    ) {
        var url = new URL(window.location);

        // Remove existing search parameters
        url.searchParams.delete('FilterText');
        url.searchParams.delete('CustomerFilterText');
        url.searchParams.delete('StatusFilter');
        url.searchParams.delete('AdvertiserIdFilter');
        url.searchParams.delete('OwnerBcIdFilter');
        url.searchParams.delete('NameFilter');
        url.searchParams.delete('CompanyFilter');
        url.searchParams.delete('IndustryFilter');
        url.searchParams.delete('AddressFilter');
        url.searchParams.delete('CountryFilter');
        url.searchParams.delete('CurrencyFilter');

        // Add new search parameters if they have values
        if (filterText && filterText.trim() !== '') {
            url.searchParams.set('FilterText', filterText);
        }
        if (customerFilterText && customerFilterText.trim() !== '') {
            url.searchParams.set('CustomerFilterText', customerFilterText);
        }
        if (status && status !== '') {
            url.searchParams.set('StatusFilter', status);
        }
        if (advertiserId && advertiserId.trim() !== '') {
            url.searchParams.set('AdvertiserIdFilter', advertiserId);
        }
        if (ownerBcId && ownerBcId.trim() !== '') {
            url.searchParams.set('OwnerBcIdFilter', ownerBcId);
        }
        if (name && name.trim() !== '') {
            url.searchParams.set('NameFilter', name);
        }
        if (company && company.trim() !== '') {
            url.searchParams.set('CompanyFilter', company);
        }
        if (industry && industry.trim() !== '') {
            url.searchParams.set('IndustryFilter', industry);
        }
        if (address && address.trim() !== '') {
            url.searchParams.set('AddressFilter', address);
        }
        if (country && country.trim() !== '') {
            url.searchParams.set('CountryFilter', country);
        }
        if (currency && currency.trim() !== '') {
            url.searchParams.set('CurrencyFilter', currency);
        }

        // Update URL without reloading the page
        window.history.replaceState({}, '', url.toString());
    }

    // Load saved parameters when page loads
    loadSavedSearchParams();

    var dataTable = $('#AdAccountsTable').DataTable(
        abp.libs.datatables.normalizeConfiguration({
            serverSide: true,
            paging: true,
            pageLength: 20,
            lengthMenu: [10, 20, 30, 50, 100],
            order: [[2, 'asc']],
            searching: false,
            scrollX: true,
            drawCallback: function(settings) {
                // Initialize tooltips after table draw
                $('[data-bs-toggle="tooltip"]').tooltip();
            },
            ajax: abp.libs.datatables.createAjax(
                function (input) {
                    return $.ajax({
                        url: abp.appPath + 'api/AdAccounts',
                        type: 'POST',
                        data: input,
                    });
                },
                function (requestData) {
                    return {
                        filterText: $('#FilterText').val(),
                        customerFilterText: $('#CustomerFilterText').val().trim(),
                        advertiserId: $('#AdvertiserIdFilter').val(),
                        ownerBcId: $('#OwnerBcIdFilter').val(),
                        name: $('#NameFilter').val(),
                        company: $('#CompanyFilter').val(),
                        industry: $('#IndustryFilter').val(),
                        address: $('#AddressFilter').val(),
                        country: $('#CountryFilter').val(),
                        currency: $('#CurrencyFilter').val(),
                        statuses: getStatusFilterValue(),
                    };
                }
            ),
            columnDefs: [
                {
                    title: l('AdAccount:OwnerBcId'),
                    data: 'ownerBcId',
                },
                {
                    title: l('AdvertiserId'),
                    data: 'advertiserId',
                },
                {
                    title: l('AdAccount:Name'),
                    data: 'name',
                    render: function (data) {
                        return (
                            '<div style="word-wrap: break-word; white-space: normal;">' +
                            data +
                            '</div>'
                        );
                    },
                },
                {
                    title: l('Company'),
                    data: 'company',
                },
                {
                    title: l('AdAccount:Status'),
                    data: 'status',
                    visible: false,
                    render: function (data) {
                        var badgeClass = getBadgeClassForStatus(data);
                        var statusName = getStatusName(data);
                        var statusText = l('AdAccount:Status:' + statusName);
                        return (
                            '<span class="badge ' +
                            badgeClass +
                            '">' +
                            statusText +
                            '</span>'
                        );
                    },
                },
                {
                    title: l('AdAccount:Role'),
                    data: 'role',
                    visible: false,
                    render: function (data) {
                        var roleText = '';
                        switch (data) {
                            case 1:
                                roleText = l('AdAccount:Role:ROLE_ADVERTISER');
                                break;
                            case 2:
                                roleText = l(
                                    'AdAccount:Role:ROLE_CHILD_ADVERTISER'
                                );
                                break;
                            case 3:
                                roleText = l('AdAccount:Role:ROLE_CHILD_AGENT');
                                break;
                            case 4:
                                roleText = l('AdAccount:Role:ROLE_AGENT');
                                break;
                        }
                        return roleText;
                    },
                },
                {
                    title: l('Asset:RelationType'),
                    data: 'relationType',
                    visible: true,
                    render: function (data) {
                        return data ? l('Asset:RelationType:' + data) : '';
                    },
                },
                {
                    title: l('AdAccount:Country'),
                    data: 'country',
                    visible: false,
                },
                {
                    title: l('Currency'),
                    data: 'currency',
                    visible: false,
                },
                {
                    title: l('AdAccount:Balance'),
                    data: 'balance',
                    visible: false,
                    render: function (data) {
                        return data ? data.toLocaleString() : '0';
                    },
                },
                {
                    title: l('Customer:CustomerId'),
                    data: 'customerId',
                    visible: false,
                    render: function (data) {
                        return data ? data : 'N/A';
                    },
                },
                {
                    title: l('Customer:CustomerName'),
                    data: 'customerName',
                    visible: false,
                    render: function (data) {
                        return data ? data : 'N/A';
                    },
                },
                {
                    title: l('Actions'),
                    data: null,
                    width: '200px',
                    render: function (data, type, row) {
                        var html =
                            '<div class="ad-account-action-buttons d-flex gap-1">';

                        // Rule Notification Configuration button (standalone)
                        html += `<button class="btn btn-sm btn-warning" onclick="openRuleNotificationModal('${
                            row.advertiserId
                        }', '${row.id}', '${row.ownerBcId || ''}')" title="${l(
                            'RuleNotificationConfiguration'
                        )}" data-bs-toggle="tooltip" data-bs-placement="top">
                        <i class="fa fa-bell"></i>
                        </button>`;
                        if (
                            abp.auth.isGranted('TikTok.AdAccounts.Edit') ||
                            row.permissions.includes(
                                'TikTok.AdAccounts.Edit'
                            ) ||
                            abp.auth.isGranted(
                                'TikTok.AdAccounts.AssignSupport'
                            ) ||
                            abp.auth.isGranted('TikTok.AdAccounts.Delete') ||
                            row.permissions.includes('TikTok.AdAccounts.Delete')
                        ) {
                            // Actions dropdown (Edit, Supporter Configuration, Delete)
                            html += `<div class="dropdown">
                        <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fa fa-ellipsis-h"></i> ${l('Actions')}
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">`;

                            // Edit option
                            if (
                                abp.auth.isGranted('TikTok.AdAccounts.Edit') ||
                                row.permissions.includes(
                                    'TikTok.AdAccounts.Edit'
                                )
                            ) {
                                html += `<a class="dropdown-item" href="javascript:void(0)" onclick="loadEditModal('${
                                    row.id
                                }')">
                            <i class="fa fa-edit"></i> ${l('Edit')}
                        </a>`;
                            }
                            if (
                                abp.auth.isGranted(
                                    'TikTok.AdAccounts.AssignSupport'
                                )
                            ) {
                                html += `<a class="dropdown-item" href="javascript:void(0)" onclick="openSupporterConfigModal('${
                                    row.advertiserId
                                }', '${row.id}', '${row.ownerBcId || ''}')">
                        <i class="fa fa-users"></i> ${l(
                            'SupporterConfiguration'
                        )}
                        </a>`;
                            }

                            // Delete option
                            if (
                                abp.auth.isGranted(
                                    'TikTok.AdAccounts.Delete'
                                ) ||
                                row.permissions.includes(
                                    'TikTok.AdAccounts.Delete'
                                )
                            ) {
                                html += `<div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="javascript:void(0)" onclick="deleteAdAccount('${
                                row.id
                            }', '${row.name}')">
                                <i class="fa fa-trash text-danger"></i> ${l(
                                    'Delete'
                                )}
                            </a>`;
                            }

                            html += `</div></div></div>`;
                        }

                        return html;
                    },
                },
            ],
        })
    );
    window.dataTable = dataTable;

    // Get selected statuses from multiselect (function kept for compatibility but not used)
    function getSelectedStatuses() {
        // This function is replaced by getStatusFilterValue()
        return getStatusFilterValue().map((s) => parseInt(s));
    }

    // Filter Tags Management
    function updateFilterTags() {
        var activeFilters = [];

        // Check basic search
        var filterText = $('#FilterText').val();
        if (filterText && filterText.trim() !== '') {
            activeFilters.push({
                type: 'search',
                label: l('Search'),
                value: filterText,
                field: 'FilterText',
            });
        }

        // Check status filter
        var statusValues = getStatusFilterValue();
        let displaynames = window.statusesData
            .filter((x) => statusValues.includes(x.value))
            .map((x) => x.label);
        if (statusValues && statusValues.length > 0) {
            var statusText = displaynames.join(', ');
            activeFilters.push({
                type: 'status',
                label: l('Status'),
                value: statusText,
                field: 'StatusFilter',
            });
        }

        // Check advanced search filters
        var advertiserId = $('#AdvertiserIdFilter').val();
        if (advertiserId && advertiserId.trim() !== '') {
            activeFilters.push({
                type: 'advertiserId',
                label: l('AdvertiserId'),
                value: advertiserId,
                field: 'AdvertiserIdFilter',
            });
        }

        var ownerBcId = $('#OwnerBcIdFilter').val();
        if (ownerBcId && ownerBcId.trim() !== '') {
            activeFilters.push({
                type: 'ownerBcId',
                label: l('AdAccount:OwnerBcId'),
                value: ownerBcId,
                field: 'OwnerBcIdFilter',
            });
        }

        var name = $('#NameFilter').val();
        if (name && name.trim() !== '') {
            activeFilters.push({
                type: 'name',
                label: l('AdAccount:Name'),
                value: name,
                field: 'NameFilter',
            });
        }

        var company = $('#CompanyFilter').val();
        if (company && company.trim() !== '') {
            activeFilters.push({
                type: 'company',
                label: l('Company'),
                value: company,
                field: 'CompanyFilter',
            });
        }

        var industry = $('#IndustryFilter').val();
        if (industry && industry.trim() !== '') {
            activeFilters.push({
                type: 'industry',
                label: l('AdAccount:Industry'),
                value: industry,
                field: 'IndustryFilter',
            });
        }

        var address = $('#AddressFilter').val();
        if (address && address.trim() !== '') {
            activeFilters.push({
                type: 'address',
                label: l('AdAccount:Address'),
                value: address,
                field: 'AddressFilter',
            });
        }

        var country = $('#CountryFilter').val();
        if (country && country.trim() !== '') {
            activeFilters.push({
                type: 'country',
                label: l('AdAccount:Country'),
                value: country,
                field: 'CountryFilter',
            });
        }

        var currency = $('#CurrencyFilter').val();
        if (currency && currency.trim() !== '') {
            activeFilters.push({
                type: 'currency',
                label: l('Currency'),
                value: currency,
                field: 'CurrencyFilter',
            });
        }
        var customerFilterText = $('#CustomerFilterText').val();
        if (customerFilterText && customerFilterText.trim() !== '') {
            activeFilters.push({
                type: 'customerFilterText',
                label: l('Customer:CustomerName'),
                value: customerFilterText,
                field: 'CustomerFilterText',
            });
        }

        // Update UI
        if (activeFilters.length > 0) {
            $('#ActiveFilterTags').show();
            renderFilterTags(activeFilters);
        } else {
            $('#ActiveFilterTags').hide();
        }
    }

    function renderFilterTags(filters) {
        var container = $('#FilterTagsContainer');
        container.empty();

        filters.forEach(function (filter) {
            var tagClass =
                filter.type === 'status'
                    ? 'filter-tag status-filter'
                    : 'filter-tag';
            var tag = $('<div>').addClass(tagClass).html(`
                <span class="tag-label">${filter.label}:</span>
                <span class="tag-value">${filter.value}</span>
                <button type="button" class="remove-tag" data-field="${
                    filter.field
                }" title="${l('RemoveFilter')}">
                    <i class="fa fa-times"></i>
                </button>
            `);

            container.append(tag);
        });
    }

    function removeFilterTag(field) {
        switch (field) {
            case 'FilterText':
                $('#FilterText').val('');
                localStorage.removeItem('adAccountFilterText');
                break;
            case 'StatusFilter':
                if (window.basicStatusMultiSelect) {
                    window.basicStatusMultiSelect.clear();
                }
                if (window.advancedStatusMultiSelect) {
                    window.advancedStatusMultiSelect.clear();
                }
                localStorage.removeItem('adAccountStatus');
                break;
            case 'AdvertiserIdFilter':
                $('#AdvertiserIdFilter').val('');
                localStorage.removeItem('adAccountAdvertiserId');
                break;
            case 'OwnerBcIdFilter':
                $('#OwnerBcIdFilter').val('');
                localStorage.removeItem('adAccountOwnerBcId');
                break;
            case 'NameFilter':
                $('#NameFilter').val('');
                localStorage.removeItem('adAccountName');
                break;
            case 'CompanyFilter':
                $('#CompanyFilter').val('');
                localStorage.removeItem('adAccountCompany');
                break;
            case 'IndustryFilter':
                $('#IndustryFilter').val('');
                localStorage.removeItem('adAccountIndustry');
                break;
            case 'AddressFilter':
                $('#AddressFilter').val('');
                localStorage.removeItem('adAccountAddress');
                break;
            case 'CountryFilter':
                $('#CountryFilter').val('');
                localStorage.removeItem('adAccountCountry');
                break;
            case 'CurrencyFilter':
                $('#CurrencyFilter').val('');
                localStorage.removeItem('adAccountCurrency');
                break;
            case 'CustomerFilterText':
                $('#CustomerFilterText').val('');
                localStorage.removeItem('adAccountCustomerFilterText');
                break;
        }

        // Update URL
        saveSearchParams();

        // Update filter tags
        updateFilterTags();

        // Reload data table
        dataTable.ajax.reload();
    }

    function clearSearchParams() {
        $('#FilterText').val('');
        try {
            if (window.basicStatusMultiSelect) {
                window.basicStatusMultiSelect.clear();
            }
            if (window.advancedStatusMultiSelect) {
                window.advancedStatusMultiSelect.clear();
            }
        } catch (e) {
            console.error('Error clearing status filters:', e);
        }

        $('#AdvertiserIdFilter').val('');
        $('#OwnerBcIdFilter').val('');
        $('#NameFilter').val('');
        $('#CompanyFilter').val('');
        $('#IndustryFilter').val('');
        $('#AddressFilter').val('');
        $('#CountryFilter').val('');
        $('#CurrencyFilter').val('');
        $('#CustomerFilterText').val('');
        // Status filter is now multiselect, cleared above

        // Clear localStorage
        localStorage.removeItem('adAccountFilterText');
        localStorage.removeItem('adAccountStatus');
        localStorage.removeItem('adAccountAdvertiserId');
        localStorage.removeItem('adAccountOwnerBcId');
        localStorage.removeItem('adAccountName');
        localStorage.removeItem('adAccountCompany');
        localStorage.removeItem('adAccountIndustry');
        localStorage.removeItem('adAccountAddress');
        localStorage.removeItem('adAccountCountry');
        localStorage.removeItem('adAccountCurrency');
        localStorage.removeItem('adAccountCustomerFilterText');

        // Clear URL parameters
        var url = new URL(window.location);
        url.searchParams.delete('FilterText');
        url.searchParams.delete('StatusFilter');
        url.searchParams.delete('AdvertiserIdFilter');
        url.searchParams.delete('OwnerBcIdFilter');
        url.searchParams.delete('NameFilter');
        url.searchParams.delete('CompanyFilter');
        url.searchParams.delete('IndustryFilter');
        url.searchParams.delete('AddressFilter');
        url.searchParams.delete('CountryFilter');
        url.searchParams.delete('CurrencyFilter');
        url.searchParams.delete('CustomerFilterText');
        window.history.replaceState({}, '', url.toString());

        // Hide advanced search panel
        $('#AdvancedSearchPanel').hide();
        $('#AdvancedSearchToggle').removeClass('active');

        // Clear filter tags
        updateFilterTags();

        // Reload data table
        dataTable.ajax.reload();
    }

    // Event handlers
    $('#SearchForm').submit(function (e) {
        e.preventDefault();
        saveSearchParams();
        dataTable.ajax.reload();
    });

    // Auto search when status changes - handled by MultiSelect change event
    // Initialize event handlers after MultiSelect components are ready
    setTimeout(function () {
        if (window.basicStatusMultiSelect) {
            window.basicStatusMultiSelect.change = function (args) {
                // Sync with advanced status filter
                if (window.advancedStatusMultiSelect) {
                    window.advancedStatusMultiSelect.value = this.value;
                }
                saveSearchParams();
                dataTable.ajax.reload();
            };
        }

        if (window.advancedStatusMultiSelect) {
            window.advancedStatusMultiSelect.change = function (args) {
                // Sync with basic status filter
                if (window.basicStatusMultiSelect) {
                    window.basicStatusMultiSelect.value = this.value;
                }
                saveSearchParams();
                dataTable.ajax.reload();
            };
        }
    }, 1000);

    // Advanced search toggle
    $('#AdvancedSearchToggle').on('click', function () {
        const panel = $('#AdvancedSearchPanel');
        const isVisible = panel.is(':visible');

        if (isVisible) {
            panel.hide();
            $(this).removeClass('active');
        } else {
            panel.show();
            $(this).addClass('active');
        }
    });

    // Advanced search button
    $('#AdvancedSearchBtn').on('click', function () {
        saveSearchParams();
        dataTable.ajax.reload();
    });

    // Clear advanced search button
    $('#ClearAdvancedSearchBtn').on('click', function () {
        $('#AdvertiserIdFilter').val('');
        $('#OwnerBcIdFilter').val('');
        $('#NameFilter').val('');
        $('#CompanyFilter').val('');
        $('#IndustryFilter').val('');
        $('#AddressFilter').val('');
        $('#CountryFilter').val('');
        $('#CurrencyFilter').val('');
        $('#CustomerFilterText').val('');
        // Status filter is now multiselect, cleared above
        saveSearchParams();
        dataTable.ajax.reload();
    });

    // Enter key on advanced search fields
    $(
        '#AdvertiserIdFilter, #OwnerBcIdFilter, #NameFilter, #CompanyFilter, #IndustryFilter, #AddressFilter, #CountryFilter, #CurrencyFilter'
    ).on('keypress', function (e) {
        if (e.which === 13) {
            e.preventDefault();
            saveSearchParams();
            dataTable.ajax.reload();
        }
    });

    // Filter Tags Event Handlers
    $(document).on('click', '.remove-tag', function (e) {
        e.preventDefault();
        var field = $(this).data('field');
        removeFilterTag(field);
    });

    // Clear all filters button
    $('#ClearAllFiltersBtn').on('click', function (e) {
        e.preventDefault();
        clearSearchParams();
    });

    // Update filter tags when basic search text changes (with debounce)
    var searchTimeout;
    $('#FilterText').on('input', function () {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function () {
            saveSearchParams();
        }, 500);
    });

    // Column visibility functionality
    function initializeColumnVisibility() {
        // Load saved column visibility state
        loadColumnVisibilityState();

        // Individual column toggle - use event delegation
        $(document).on('click', '.column-toggle', function (e) {
            e.preventDefault();
            console.log('Column toggle clicked:', $(this).data('column'));

            const columnIndex = parseInt($(this).data('column'));
            const isVisible = dataTable.column(columnIndex).visible();

            // Toggle column visibility
            dataTable.column(columnIndex).visible(!isVisible);

            // Update UI
            updateColumnToggleUI(columnIndex, !isVisible);

            // Update table header visibility
            updateTableHeaderVisibility(columnIndex, !isVisible);

            // Save state
            saveColumnVisibilityState();
        });

        // Show all columns
        $(document).on('click', '#ShowAllColumns', function (e) {
            e.preventDefault();
            showAllColumns();
        });

        // Hide all columns (except advertiserId and actions)
        $(document).on('click', '#HideAllColumns', function (e) {
            e.preventDefault();
            hideAllColumns();
        });
    }

    function loadColumnVisibilityState() {
        const savedState = localStorage.getItem('adAccountColumnVisibility');
        if (savedState) {
            try {
                const columnStates = JSON.parse(savedState);
                columnStates.forEach((isVisible, index) => {
                    if (index >= 0 && index <= 10) {
                        dataTable.column(index).visible(isVisible);
                        updateColumnToggleUI(index, isVisible);
                        updateTableHeaderVisibility(index, isVisible);
                    }
                });
            } catch (e) {
                console.error('Error loading column visibility state:', e);
            }
        }
    }

    function saveColumnVisibilityState() {
        const columnStates = [];
        for (let i = 0; i <= 10; i++) {
            columnStates.push(dataTable.column(i).visible());
        }
        localStorage.setItem(
            'adAccountColumnVisibility',
            JSON.stringify(columnStates)
        );
    }

    function updateColumnToggleUI(columnIndex, isVisible) {
        const toggleItem = $(`.column-toggle[data-column="${columnIndex}"]`);
        const icon = toggleItem.find('i');

        if (isVisible) {
            icon.removeClass('fa-eye-slash').addClass('fa-check');
        } else {
            icon.removeClass('fa-check').addClass('fa-eye-slash');
        }
    }

    function updateAllColumnToggleIcons() {
        for (let i = 0; i <= 10; i++) {
            const isVisible = dataTable.column(i).visible();
            updateColumnToggleUI(i, isVisible);
            updateTableHeaderVisibility(i, isVisible);
        }
    }

    function updateTableHeaderVisibility(columnIndex, isVisible) {
        const headerCell = $(
            `#AdAccountsTable th[data-column="${columnIndex}"]`
        );
        if (isVisible) {
            headerCell.show();
        } else {
            headerCell.hide();
        }
    }

    function initializeTableHeaderVisibility() {
        // Set initial visibility based on DataTable column visibility
        for (let i = 0; i <= 10; i++) {
            const isVisible = dataTable.column(i).visible();
            updateTableHeaderVisibility(i, isVisible);
        }
    }

    function showAllColumns() {
        for (let i = 0; i <= 10; i++) {
            dataTable.column(i).visible(true);
            updateColumnToggleUI(i, true);
            updateTableHeaderVisibility(i, true);
        }
        saveColumnVisibilityState();
    }

    function hideAllColumns() {
        for (let i = 1; i <= 10; i++) {
            dataTable.column(i).visible(false);
            updateColumnToggleUI(i, false);
            updateTableHeaderVisibility(i, false);
        }
        saveColumnVisibilityState();
    }

    // Initialize column visibility
    initializeColumnVisibility();

    // Initialize dropdown icons and table header after a short delay to ensure DOM is ready
    setTimeout(function () {
        updateAllColumnToggleIcons();
        initializeTableHeaderVisibility();
    }, 100);

    // Ensure dropdown functionality works
    $('#ColumnVisibilityToggle').on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Column visibility toggle clicked');

        // Simple toggle approach
        const $dropdown = $(this).siblings('.dropdown-menu');
        console.log('Dropdown element:', $dropdown);
        $dropdown.toggle();
    });

    // Close dropdown when clicking outside
    $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length) {
            $('.dropdown-menu').hide();
        }
    });

    // Close dropdown when clicking on dropdown items
    $(document).on('click', '.dropdown-item', function (e) {
        if (!$(this).hasClass('column-toggle') && !$(this).attr('id')) {
            $(this).closest('.dropdown-menu').hide();
        }
    });

    // Helper function to get status name from numeric value
    function getStatusName(status) {
        switch (status) {
            case 1:
                return 'STATUS_DISABLE';
            case 2:
                return 'STATUS_PENDING_CONFIRM';
            case 3:
                return 'STATUS_PENDING_VERIFIED';
            case 4:
                return 'STATUS_CONFIRM_FAIL';
            case 5:
                return 'STATUS_ENABLE';
            case 6:
                return 'STATUS_CONFIRM_FAIL_END';
            case 7:
                return 'STATUS_PENDING_CONFIRM_MODIFY';
            case 8:
                return 'STATUS_CONFIRM_MODIFY_FAIL';
            case 9:
                return 'STATUS_LIMIT';
            case 10:
                return 'STATUS_WAIT_FOR_BPM_AUDIT';
            case 11:
                return 'STATUS_WAIT_FOR_PUBLIC_AUTH';
            case 12:
                return 'STATUS_SELF_SERVICE_UNAUDITED';
            case 13:
                return 'STATUS_CONTRACT_PENDING';
            default:
                return 'STATUS_DISABLE';
        }
    }

    // Helper function to get badge class based on status
    function getBadgeClassForStatus(status) {
        switch (status) {
            case 1: // STATUS_DISABLE
                return 'bg-secondary';
            case 2: // STATUS_PENDING_CONFIRM
                return 'bg-warning';
            case 3: // STATUS_PENDING_VERIFIED
                return 'bg-info';
            case 4: // STATUS_CONFIRM_FAIL
                return 'bg-danger';
            case 5: // STATUS_ENABLE
                return 'bg-success';
            case 6: // STATUS_CONFIRM_FAIL_END
                return 'bg-danger';
            case 7: // STATUS_PENDING_CONFIRM_MODIFY
                return 'bg-warning';
            case 8: // STATUS_CONFIRM_MODIFY_FAIL
                return 'bg-danger';
            case 9: // STATUS_LIMIT
                return 'bg-warning';
            case 10: // STATUS_WAIT_FOR_BPM_AUDIT
                return 'bg-info';
            case 11: // STATUS_WAIT_FOR_PUBLIC_AUTH
                return 'bg-info';
            case 12: // STATUS_SELF_SERVICE_UNAUDITED
                return 'bg-secondary';
            case 13: // STATUS_CONTRACT_PENDING
                return 'bg-warning';
            default:
                return 'bg-secondary';
        }
    }

    // Load edit modal
    window.loadEditModal = function (id) {
        editModal.onResult(function (e) {
            console.log(e);
            dataTable.ajax.reload();
        });
        editModal.open({
            id: id,
        });
    };

    // Delete ad account
    window.deleteAdAccount = function (id, name) {
        abp.message.confirm(
            l('AdAccountDeletionConfirmationMessage', name),
            l('AreYouSure'),
            function (isConfirmed) {
                if (isConfirmed) {
                    adAccountService.delete(id).then(function () {
                        abp.notify.success(l('SuccessfullyDeleted'));
                        dataTable.ajax.reload();
                    });
                }
            }
        );
    };

    // Supporter Configuration Modal
    let currentAdvertiserId = null;

    window.openSupporterConfigModal = function (
        advertiserId,
        adAccountId,
        bcId
    ) {
        currentAdvertiserId = advertiserId;
        window.currentBcIdForSupporter = bcId || null;
        loadSupportConfiguration(advertiserId);
        $('#SupporterConfigModal').modal('show');

        // Clear search inputs when opening modal
        $('#AvailableSupportersSearch').val('');
        $('#AssignedSupportersSearch').val('');
    };

    // Rule Notification Configuration Modal
    window.openRuleNotificationModal = function (
        advertiserId,
        adAccountId,
        bcId
    ) {
        // Initialize rule configuration
        if (window.initializeRuleConfiguration) {
            window.initializeRuleConfiguration(adAccountId);
        }
        $('#RuleNotificationModal').modal('show');

        // Store both IDs for rule configuration
        window.currentAdAccountIdForRules = adAccountId;
        window.currentAdvertiserIdForRules = advertiserId;
        window.currentBcIdForRules = bcId || null;
    };

    function loadSupportConfiguration(advertiserId) {
        // Show loading
        $('#AvailableSupportersList').html(
            '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> ' +
                l('Loading') +
                '</div>'
        );
        $('#AssignedSupportersList').html(
            '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> ' +
                l('Loading') +
                '</div>'
        );

        // Call the API to get supporter configuration
        $.ajax({
            url: `/api/AdAccounts/supporter-configuration/${advertiserId}`,
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                RequestVerificationToken: $(
                    'input[name="__RequestVerificationToken"]'
                ).val(),
            },
            success: function (response) {
                console.log('loadSupportConfiguration', response);

                // Store data in global variables
                window.supportersData.available =
                    response.availableSupporters || [];
                window.supportersData.assigned =
                    response.assignedSupporters || [];

                // Render initial lists
                renderAvailableSupporters(window.supportersData.available);
                renderAssignedSupporters(window.supportersData.assigned);
            },
            error: function (xhr, status, error) {
                abp.notify.error(l('FailedToLoadSupportConfig') + ': ' + error);
                console.error(
                    'Error loading supporter configuration:',
                    xhr.responseText
                );
            },
        });
    }

    function renderAvailableSupporters(supporters) {
        const container = $('#AvailableSupportersList');
        container.empty();

        if (supporters.length === 0) {
            container.html(
                '<div class="text-muted">' +
                    l('NoAvailableSupporters') +
                    '</div>'
            );
            return;
        }
        supporters.forEach(function (supporter) {
            const item = $(`
                <div class="list-group-item d-flex justify-content-between align-items-center" data-supporter-id="${
                    supporter.id
                }">
                    <div>
                        <strong>${supporter.name || supporter.userName}</strong>
                        <br>
                        <small class="text-muted">${supporter.email}</small>
                        <br>
                        <small class="text-muted">📞 ${
                            supporter.phoneNumber || l('PhoneNumber') + ': N/A'
                        }</small>
                    </div>
                    <button class="btn btn-sm btn-primary assign-supporter" data-supporter-id="${
                        supporter.id
                    }" ${
                abp.auth.isGranted('TikTok.AdAccounts.AssignSupport') === true
                    ? ''
                    : 'disabled'
            }>
                        <i class="fas fa-plus"></i> ${l('AssignSupporter')}
                    </button>
                </div>
            `);
            container.append(item);
        });
    }

    // Helper function to create permission dropdown with checkboxes
    function createPermissionCheckboxes(selectedPermissions = []) {
        const permissions = [
            { value: 'TikTok.AdAccounts', label: 'Xem' },
            { value: 'TikTok.AdAccounts.Edit', label: 'Sửa' },
            { value: 'TikTok.AdAccounts.Delete', label: 'Xóa' },
        ];

        // Generate unique ID for dropdown
        const dropdownId =
            'permission_dropdown_' + Math.random().toString(36).substr(2, 9);

        // Create display text from selected permissions
        let displayText = 'Chọn quyền';
        if (selectedPermissions.length > 0) {
            const selectedLabels = permissions
                .filter((perm) => selectedPermissions.includes(perm.value))
                .map((perm) => perm.label);
            const joinedLabels = selectedLabels.join(', ');

            // Truncate if too long
            if (joinedLabels.length > 20) {
                displayText = joinedLabels.substring(0, 17) + '...';
            } else {
                displayText = joinedLabels;
            }
        }

        let dropdownHtml = `
        <div class="dropdown" data-dropdown-id="${dropdownId}">
            <button class="btn btn-outline-secondary dropdown-toggle btn-sm" 
                    type="button" 
                    id="${dropdownId}" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false">
                ${displayText}
            </button>
            <div class="dropdown-menu p-2" aria-labelledby="${dropdownId}">
                <div class="permission-checkboxes" onclick="event.stopPropagation();">`;

        permissions.forEach(function (permission) {
            const isChecked = selectedPermissions.includes(permission.value);
            const uniqueId =
                'perm_' +
                permission.value +
                '_' +
                Math.random().toString(36).substr(2, 9);

            // Check if this is the "View" permission (TikTok.AdAccounts)
            const isViewPermission = permission.value === 'TikTok.AdAccounts';
            const shouldBeChecked = isViewPermission || isChecked;
            const shouldBeDisabled = isViewPermission ? 'disabled' : '';

            dropdownHtml += `
                    <div class="form-check mb-1">
                        <input class="form-check-input permission-checkbox" 
                               type="checkbox" 
                               value="${permission.value}" 
                               id="${uniqueId}"
                               ${shouldBeChecked ? 'checked' : ''}
                               ${shouldBeDisabled}>
                        <label class="form-check-label small" for="${uniqueId}">
                            ${permission.label}
                        </label>
                    </div>`;
        });

        dropdownHtml += `
                </div>
            </div>
        </div>`;

        return { html: dropdownHtml, dropdownId: dropdownId };
    }

    // Function to initialize dropdown functionality after HTML is added to DOM
    function initializePermissionDropdown(dropdownId) {
        setTimeout(function () {
            const dropdown = document.querySelector(
                `[data-dropdown-id="${dropdownId}"]`
            );
            if (!dropdown) {
                console.warn('Dropdown not found:', dropdownId);
                return;
            }

            const button = dropdown.querySelector('.dropdown-toggle');
            const checkboxes = dropdown.querySelectorAll(
                '.permission-checkbox'
            );

            // Initialize Bootstrap dropdown
            try {
                // For Bootstrap 5
                if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                    new bootstrap.Dropdown(button);
                }
                // Fallback for Bootstrap 4 with jQuery
                else if (typeof $ !== 'undefined' && $.fn.dropdown) {
                    $(button).dropdown();
                }
                // Manual fallback if Bootstrap is not detected
                else {
                    button.addEventListener('click', function (e) {
                        e.preventDefault();
                        const dropdownMenu =
                            dropdown.querySelector('.dropdown-menu');
                        if (dropdownMenu) {
                            const isOpen =
                                dropdownMenu.classList.contains('show');
                            if (isOpen) {
                                dropdownMenu.classList.remove('show');
                                button.setAttribute('aria-expanded', 'false');
                            } else {
                                dropdownMenu.classList.add('show');
                                button.setAttribute('aria-expanded', 'true');
                            }
                        }
                    });

                    // Close dropdown when clicking outside
                    document.addEventListener('click', function (e) {
                        if (!dropdown.contains(e.target)) {
                            const dropdownMenu =
                                dropdown.querySelector('.dropdown-menu');
                            if (
                                dropdownMenu &&
                                dropdownMenu.classList.contains('show')
                            ) {
                                dropdownMenu.classList.remove('show');
                                button.setAttribute('aria-expanded', 'false');
                            }
                        }
                    });
                }
            } catch (e) {
                console.warn('Could not initialize Bootstrap dropdown:', e);
            }

            function updateButtonText() {
                const permissions = [
                    { value: 'TikTok.AdAccounts', label: 'Xem' },
                    { value: 'TikTok.AdAccounts.Edit', label: 'Sửa' },
                    { value: 'TikTok.AdAccounts.Delete', label: 'Xóa' },
                ];

                const selectedValues = Array.from(checkboxes)
                    .filter((cb) => cb.checked)
                    .map((cb) => cb.value);

                // Ensure "View" permission is always included in display
                if (!selectedValues.includes('TikTok.AdAccounts')) {
                    selectedValues.push('TikTok.AdAccounts');
                }

                if (selectedValues.length === 0) {
                    button.textContent = 'Chọn quyền';
                    return;
                }

                const selectedLabels = permissions
                    .filter((perm) => selectedValues.includes(perm.value))
                    .map((perm) => perm.label);
                const joinedLabels = selectedLabels.join(', ');

                if (joinedLabels.length > 20) {
                    button.textContent = joinedLabels.substring(0, 17) + '...';
                } else {
                    button.textContent = joinedLabels;
                }
            }

            checkboxes.forEach(function (checkbox) {
                checkbox.addEventListener('change', updateButtonText);
            });

            // Prevent dropdown from closing when clicking inside
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                dropdownMenu.addEventListener('click', function (e) {
                    e.stopPropagation();
                });
            }
        }, 100);
    }

    function renderAssignedSupporters(supporters) {
        const container = $('#AssignedSupportersList');
        container.empty();

        if (supporters.length === 0) {
            container.html(
                '<div class="text-muted">' +
                    l('NoAssignedSupporters') +
                    '</div>'
            );
            return;
        }

        supporters.forEach(function (supporter) {
            const permissionDropdown = createPermissionCheckboxes(
                supporter.permissions || []
            );

            const item = $(`
                <div class="list-group-item" data-supporter-id="${
                    supporter.supporterId || supporter.id
                }">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="mb-2">
                                <strong>${
                                    supporter.name || supporter.userName
                                }</strong>
                                <br>
                                <small class="text-muted">${
                                    supporter.email
                                }</small>
                                <br>
                                <small class="text-muted">📞 ${
                                    supporter.phoneNumber ||
                                    l('PhoneNumber') + ': N/A'
                                }</small>
                            </div>
                            <div class="permissions-section"${
                                abp.auth.isGranted(
                                    'TikTok.AdAccounts.AssignSupport'
                                ) === true
                                    ? ''
                                    : ' style="pointer-events: none; opacity: 0.6;"'
                            }>
                                ${permissionDropdown.html}
                            </div>
                        </div>
                        <div class="ms-3">
                            <button class="btn btn-sm btn-danger remove-supporter ${
                                abp.auth.isGranted(
                                    'TikTok.AdAccounts.AssignSupport'
                                ) === true
                                    ? ''
                                    : 'd-none'
                            }" data-supporter-id="${
                supporter.supporterId || supporter.id
            }"${
                abp.auth.isGranted('TikTok.AdAccounts.AssignSupport') === true
                    ? ''
                    : 'disabled'
            }>
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `);
            container.append(item);

            // Initialize dropdown functionality after adding to DOM
            initializePermissionDropdown(permissionDropdown.dropdownId);
        });
    }

    // Helper function to move supporter between lists
    function moveSupporterBetweenLists(
        supporterId,
        fromList,
        toList,
        permissions = ['TikTok.AdAccounts']
    ) {
        const fromArray = window.supportersData[fromList];
        const toArray = window.supportersData[toList];

        // Find supporter in source array
        const supporterIndex = fromArray.findIndex(
            (s) => s.id === supporterId || s.supporterId === supporterId
        );

        if (supporterIndex === -1) {
            console.error('Supporter not found in source list');
            return;
        }

        // Get supporter data
        const supporter = fromArray[supporterIndex];

        // Add permissions if moving to assigned list
        if (toList === 'assigned') {
            supporter.permissions = permissions;
            supporter.supporterId = supporter.id; // Ensure supporterId field exists
        }

        // Remove from source array
        fromArray.splice(supporterIndex, 1);

        // Add to destination array
        toArray.push(supporter);

        // Re-render both lists with current filters
        const availableSearchTerm = $('#AvailableSupportersSearch').val();
        const assignedSearchTerm = $('#AssignedSupportersSearch').val();

        filterSupportersList(availableSearchTerm, 'available');
        filterSupportersList(assignedSearchTerm, 'assigned');
    }

    // Modified filter function to work with global data
    function filterSupportersList(searchTerm, listType) {
        const searchTermLower = searchTerm.toLowerCase().trim();

        let dataToFilter, renderFunction;

        if (listType === 'available') {
            dataToFilter = window.supportersData.available;
            renderFunction = renderAvailableSupporters;
        } else if (listType === 'assigned') {
            dataToFilter = window.supportersData.assigned;
            renderFunction = renderAssignedSupporters;
        } else {
            console.error('Invalid list type:', listType);
            return;
        }

        // Filter data based on search term
        let filteredData = dataToFilter;

        if (searchTermLower !== '') {
            filteredData = dataToFilter.filter(function (supporter) {
                const name = (
                    supporter.name ||
                    supporter.userName ||
                    ''
                ).toLowerCase();
                const email = (supporter.email || '').toLowerCase();
                const phoneRaw = supporter.phoneNumber || '';

                // Clean phone number by removing emoji and extra spaces
                const phone = phoneRaw
                    .replace(/📞|Phone Number:|N\/A/gi, '')
                    .trim()
                    .toLowerCase();

                // Check if search term matches name, email, or phone
                return (
                    name.includes(searchTermLower) ||
                    email.includes(searchTermLower) ||
                    phone.includes(searchTermLower)
                );
            });
        }

        // Re-render the filtered list
        renderFunction(filteredData);
    }

    // Event handlers for supporter configuration
    $(document).on('click', '.assign-supporter', function () {
        const supporterId = $(this).data('supporter-id');
        const defaultPermissions = ['TikTok.AdAccounts'];

        moveSupporterBetweenLists(
            supporterId,
            'available',
            'assigned',
            defaultPermissions
        );
    });

    $(document).on('click', '.remove-supporter', function () {
        const supporterId = $(this).data('supporter-id');

        moveSupporterBetweenLists(supporterId, 'assigned', 'available');
    });

    // Save supporter configuration only
    $('#SaveSupporterConfig').click(function () {
        const assignments = [];

        $('#AssignedSupportersList .list-group-item').each(function () {
            const supporterId = $(this).data('supporter-id');
            const permissions = [];

            // Get selected permissions from checkboxes
            $(this)
                .find('.permission-checkbox:checked')
                .each(function () {
                    permissions.push($(this).val());
                });

            // Ensure "View" permission is always included
            if (!permissions.includes('TikTok.AdAccounts')) {
                permissions.push('TikTok.AdAccounts');
            }

            assignments.push({
                supporterId: supporterId,
                permissions: permissions,
            });
        });

        const request = {
            bcId: null,
            advertiserId: currentAdvertiserId,
            supporters: assignments,
            rules: [], // No rules in supporter config modal
        };

        // Show loading
        const saveButton = $(this);
        const originalText = saveButton.text();
        saveButton
            .prop('disabled', true)
            .html(
                '<i class="fas fa-spinner fa-spin"></i> ' + l('Saving') + '...'
            );

        // First, get the AdAccount entity to get the AdAccount ID and BC ID
        $.ajax({
            url: `/api/AdAccounts/by-advertiser-id/${currentAdvertiserId}`,
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                RequestVerificationToken: $(
                    'input[name="__RequestVerificationToken"]'
                ).val(),
            },
            success: function (adAccountResponse) {
                if (!adAccountResponse) {
                    abp.notify.error(l('AdAccountNotFound'));
                    saveButton.prop('disabled', false).text(originalText);
                    return;
                }

                // Call the API to update supporter configuration
                $.ajax({
                    url: `/api/AdAccounts/supporter-configuration/${currentAdvertiserId}`,
                    method: 'PUT',
                    contentType: 'application/json',
                    headers: {
                        RequestVerificationToken: $(
                            'input[name="__RequestVerificationToken"]'
                        ).val(),
                    },
                    data: JSON.stringify(request),
                    success: function (response) {
                        abp.notify.success(
                            l('SupportConfigurationSaved') ||
                                'Support configuration saved successfully'
                        );
                        $('#SupporterConfigModal').modal('hide');

                        // Reload supporter configuration to reflect changes
                        loadSupportConfiguration(currentAdvertiserId);
                    },
                    error: function (xhr, status, error) {
                        abp.notify.error(
                            l('FailedToSaveSupportConfig') + ': ' + error
                        );
                        console.error(
                            'Error saving supporter configuration and rules:',
                            xhr.responseText
                        );
                    },
                    complete: function () {
                        saveButton.prop('disabled', false).text(originalText);
                    },
                });
            },
            error: function (xhr, status, error) {
                abp.notify.error(l('FailedToGetAdAccountInfo') + ': ' + error);
                console.error(
                    'Error getting AdAccount information:',
                    xhr.responseText
                );
                saveButton.prop('disabled', false).text(originalText);
            },
            complete: function () {
                saveButton.prop('disabled', false).text(originalText);
            },
        });
    });

    // Save rule notification configuration
    $('#SaveRuleNotificationConfig').click(function () {
        // Get selected rules from the rule configuration
        const selectedRuleIds = window.selectedRules
            ? window.selectedRules.map((rule) => rule.id)
            : [];

        // Show loading
        const saveButton = $(this);
        const originalText = saveButton.text();
        saveButton
            .prop('disabled', true)
            .html(
                '<i class="fas fa-spinner fa-spin"></i> ' + l('Saving') + '...'
            );

        // Build query string with bcId and advertiserId
        const queryParams = new URLSearchParams({
            advertiserId: window.currentAdvertiserIdForRules,
        });

        // Add bcId if available
        // if (window.currentBcIdForRules) {
        //     queryParams.append('bcId', window.currentBcIdForRules);
        // }

        // Call the dedicated API for rule configuration
        $.ajax({
            url: `/api/app/rule-ad-account/rule-advertiser?${queryParams.toString()}`,
            method: 'PUT',
            contentType: 'application/json',
            headers: {
                RequestVerificationToken: $(
                    'input[name="__RequestVerificationToken"]'
                ).val(),
            },
            data: JSON.stringify(selectedRuleIds),
            success: function (response) {
                abp.notify.success(
                    l('RuleNotificationConfigSaved') ||
                        'Rule notification configuration saved successfully'
                );
                $('#RuleNotificationModal').modal('hide');
            },
            error: function (xhr, status, error) {
                abp.notify.error(l('FailedToSaveRuleConfig') + ': ' + error);
                console.error(
                    'Error saving rule notification configuration:',
                    xhr.responseText
                );
            },
            complete: function () {
                saveButton.prop('disabled', false).text(originalText);
            },
        });
    });

    // Navigate to create page when New button is clicked
    $('#NewAdAccountButton').on('click', function (e) {
        e.preventDefault();
        window.location.href = '/AdAccounts/Create';
    });

    // Updated search event handlers
    $(document).on('change keyup', '#AvailableSupportersSearch', function () {
        const searchTerm = $(this).val();
        filterSupportersList(searchTerm, 'available');
    });

    $(document).on('change keyup', '#AssignedSupportersSearch', function () {
        const searchTerm = $(this).val();
        filterSupportersList(searchTerm, 'assigned');
    });

    // Existing modal logic
    const createModal = new abp.ModalManager(
        abp.appPath + 'AdAccounts/CreateModal'
    );

    createModal.onResult(function () {
        dataTable.ajax.reload();
    });
    editModal.onResult(function () {
        dataTable.ajax.reload();
    });
});
