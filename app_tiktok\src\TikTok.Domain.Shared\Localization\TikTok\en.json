{"Culture": "en", "Texts": {"AppName": "TikTok", "Menu:ContactUs": "Contact Us", "Menu:ArticleSample": "Article Sample", "Home": "Home", "Date": "Date", "Permission:Dashboard": "Dashboard", "Menu:Dashboard": "Dashboard", "Menu:HomePage": "Home page", "Menu:NotificationRules": "Notification Rules", "Dashboard": "Dashboard", "ExternalProvider:Google": "Google", "ExternalProvider:Google:ClientId": "Client ID", "ExternalProvider:Google:ClientSecret": "Client Secret", "ExternalProvider:Microsoft": "Microsoft", "ExternalProvider:Microsoft:ClientId": "Client ID", "ExternalProvider:Microsoft:ClientSecret": "Client Secret", "ExternalProvider:Twitter": "Twitter", "ExternalProvider:Twitter:ConsumerKey": "Consumer Key", "ExternalProvider:Twitter:ConsumerSecret": "Consumer Secret", "NewsletterHeader": "Subscribe to the newsletter!", "NewsletterInfo": "Get information about the latest happenings.", "NewsletterPreference_Default": "De<PERSON>ult Newsletter", "NewsletterPrivacyAcceptMessage": "I accept the <a href='/privacy-policy'>Privacy Policy</a>.", "Language": "Language", "Search": "Search", "SearchAndFilter": "Search and Filter", "SearchPlaceholder": "Search by name, company, ID...", "SearchByAdvertiserId": "Search by advertiser ID", "SearchByBcId": "Search by BC ID", "SearchByBcName": "Search by BC Name", "SearchByOwnerBcId": "Search by owner BC ID", "SearchByName": "Search by name", "SearchByAdvertiserName": "Search by advertiser name", "SearchByCompany": "Search by company", "SearchByIndustry": "Search by industry", "SearchByAddress": "Search by address", "SearchByCountry": "Search by country", "SearchByCurrency": "Search by currency", "SearchByTimezone": "Search by timezone", "AllStatuses": "All Statuses", "All": "All", "BasicInformation": "Basic Information", "LocationAndDetails": "Location and Details", "StatusFilter": "Status Filter", "ActiveFilters": "Active filters", "ClearAllFilters": "Clear all filters", "RemoveFilter": "Remove filter", "ColumnVisibility": "Column Visibility", "ShowAllColumns": "Show all columns", "HideAllColumns": "Hide all columns", "LoadMore": "Load More", "Settings": "Settings", "Theme": "Theme", "DeviceTheme": "Device theme", "Dark": "Dark", "Light": "Light", "Unspecified": "System", "SeeAllUsers": "See All Users", "TakePhoto": "Take Photo", "ChoosePhoto": "<PERSON>ose Photo", "Menu:Home": "Home", "LongWelcomeMessage": "Welcome to the application. This is a startup project based on the ABP framework. For more information visit", "Welcome": "Welcome", "Menu:BusinessCenters": "Business Centers", "NewBusinessCenter": "New Business Center", "BcId": "BC ID", "BcName": "BC Name", "BusinessCenter:Type": "Type", "BusinessCenter:RegisteredArea": "Registered Area", "BusinessCenter:Timezone": "Timezone", "BusinessCenter:UserRole": "User Role", "BusinessCenter:ExtUserFinanceRole": "Extended User Finance Role", "BusinessCenter:Status:Reviewing": "Reviewing", "BusinessCenter:Status:Deny": "<PERSON><PERSON>", "BusinessCenter:Status:Enable": "Enable", "BusinessCenter:Status:Punish": "<PERSON><PERSON><PERSON>", "BusinessCenter:Type:Normal": "Normal", "BusinessCenter:Type:Direct": "Direct", "BusinessCenter:Type:Agency": "Agency", "BusinessCenter:Type:SelfService": "Self Service", "BusinessCenter:Type:SelfServiceAgency": "Self Service Agency", "BusinessCenter:UserRole:Admin": "Admin", "BusinessCenter:UserRole:Standard": "Standard", "BusinessCenter:ExtUserFinanceRole:Manager": "Finance Manager", "BusinessCenter:ExtUserFinanceRole:Analyst": "Finance Analyst", "Permission:BusinessCenters": "Business Centers", "Permission:BusinessCenters.Create": "Create Business Centers", "Permission:BusinessCenters.Edit": "Edit Business Centers", "Permission:BusinessCenters.Delete": "Delete Business Centers", "BusinessCenterDeletionConfirmationMessage": "Are you sure you want to delete the business center '{0}'?", "Menu:AdAccounts": "Ad Accounts", "NewAdAccount": "New Ad Account", "AdAccount:OwnerBcId": "Owner BC ID", "AdAccount:Role": "Role", "AdAccount:RejectionReason": "Rejection Reason", "AdAccount:Name": "Name", "AdAccount:Timezone": "Timezone", "AdAccount:DisplayTimezone": "Display Timezone", "AdAccount:CompanyNameEditable": "Company Name Editable", "AdAccount:Industry": "Industry", "AdAccount:Address": "Address", "AdAccount:Country": "Country", "AdAccount:AdvertiserAccountType": "Account Type", "AdAccount:Contacter": "<PERSON>er", "AdAccount:Email": "Email", "AdAccount:CellphoneNumber": "Cellphone Number", "AdAccount:TelephoneNumber": "Telephone Number", "AdAccount:Language": "Language", "AdAccount:LicenseNo": "License No", "AdAccount:LicenseUrl": "License URL", "AdAccount:Description": "Description", "AdAccount:CreateTime": "Create Time", "AdAccount:Status:STATUS_DISABLE": "Disabled", "AdAccount:Status:STATUS_PENDING_CONFIRM": "Pending Confirmation", "AdAccount:Status:STATUS_PENDING_VERIFIED": "Pending Verification", "SelectAdAccounts": "Select Ad Accounts", "OwnerBcId": "Owner BC ID", "Company": "Company", "Status": "Status", "Role": "Role", "Country": "Country", "AdAccountType": "Ad Account Type", "Balance": "Balance", "Clear": "Clear", "Select": "Select", "SearchResults": "Search Results", "Menu:ReportIntegratedBcs": "Report Integrated BCs", "NewReportIntegratedBc": "New Report Integrated BC", "ReportIntegratedBc:Date": "Date", "ReportIntegratedBc:Spend": "Spend", "ReportIntegratedBc:BilledCost": "Billed Cost", "ReportIntegratedBc:CashSpend": "Cash Spend", "ReportIntegratedBc:VoucherSpend": "Voucher Spend", "ReportIntegratedBc:CashbackCouponSpend": "Cashback Coupon Spend", "ReportIntegratedBc:TaxSpend": "Tax Spend", "ReportIntegratedBc:Cpc": "CPC", "ReportIntegratedBc:Cpm": "CPM", "ReportIntegratedBc:Impressions": "Impressions", "ReportIntegratedBc:Clicks": "<PERSON>licks", "ReportIntegratedBc:Ctr": "CTR", "ReportIntegratedBc:Conversion": "Conversion", "ReportIntegratedBc:CostPerConversion": "Cost Per Conversion", "ReportIntegratedBc:ConversionRate": "Conversion Rate", "ReportIntegratedBc:Reach": "Reach", "ReportIntegratedBc:RealTimeConversion": "Real Time Conversion", "ReportIntegratedBc:RealTimeCostPerConversion": "Real Time Cost Per Conversion", "ReportIntegratedBc:RealTimeConversionRate": "Real Time Conversion Rate", "ReportIntegratedBc:SkanConversion": "SKAN Conversion", "ReportIntegratedBc:SkanCostPerConversion": "SKAN Cost Per Conversion", "ReportIntegratedBc:SkanConversionRate": "SKAN Conversion Rate", "ReportIntegratedBc:VideoWatched2s": "Video Watched 2s", "ReportIntegratedBc:VideoWatched6s": "Video Watched 6s", "ReportIntegratedBc:VideoViewsP100": "Video Views 100%", "ReportIntegratedBc:VideoViewsP75": "Video Views 75%", "ReportIntegratedBc:VideoViewsP50": "Video Views 50%", "ReportIntegratedBc:VideoViewsP25": "Video Views 25%", "ReportIntegratedBc:Type:Summary": "Summary", "ReportIntegratedBc:Type:Detailed": "Detailed", "ReportIntegratedBc:Type:Realtime": "Real Time", "Permission:ReportIntegratedBcs": "Report Integrated BCs", "Permission:ReportIntegratedBcs.Create": "Create Report Integrated BCs", "Permission:ReportIntegratedBcs.Edit": "Edit Report Integrated BCs", "Permission:ReportIntegratedBcs.Delete": "Delete Report Integrated BCs", "ReportIntegratedBcDeletionConfirmationMessage": "Are you sure you want to delete the report integrated BC '{0}'?", "AdvancedFilter": "Advanced Filter", "HideAdvancedFilter": "Hide Advanced Filter", "ClearFilters": "Clear Filters", "ApplyFilters": "Apply Filters", "ColumnSettings": "<PERSON>umn <PERSON>", "ColumnSettingsSaved": "Column settings saved successfully", "SupportConfiguration": "Support Configuration", "SupportConfigurationSaved": "Support configuration saved successfully", "SupporterConfiguration": "Supporter Configuration", "RuleNotificationConfiguration": "Rule Notification Configuration", "AvailableSupporters": "Available Supporters", "AssignedSupporters": "Assigned Supporters", "NoAvailableSupporters": "No available supporters", "NoAssignedSupporters": "No assigned supporters", "AssignSupporter": "Assign", "Viewer": "Viewer", "Secondary": "Secondary", "Primary": "Primary", "Loading": "Loading...", "FailedToLoadSupportConfig": "Failed to load supporter configuration", "FailedToSaveSupportConfig": "Failed to save supporter configuration and rules", "FailedToGetAdAccountInfo": "Failed to get AdAccount information", "AdAccountNotFound": "AdAccount not found", "Saving": "Saving", "AvailableRules": "Available Rules", "SelectedRules": "Selected Rules", "RuleInstructions": "Instructions: Drag rules from the left panel to the right panel to assign them to this AdAccount. You can also click on rules to select/deselect them. Use the search box to filter available rules.", "SearchRules": "Search rules...", "NoRulesFound": "No rules found matching", "NoRulesAvailable": "No rules available", "NoRulesSelected": "No rules selected", "UnnamedRule": "Unnamed Rule", "Created": "Created", "Owner": "Owner", "System": "System", "PhoneNumber": "Phone Number", "AdvancedSearch": "Advanced Search", "From": "From", "To": "To", "SuccessfullyDeleted": "Successfully deleted", "AdAccount:Status:STATUS_CONFIRM_FAIL": "Confirmation Failed", "AdAccount:Status:STATUS_ENABLE": "Enabled", "AdAccount:Status:STATUS_CONFIRM_FAIL_END": "CRM Confirmation Failed", "AdAccount:Status:STATUS_PENDING_CONFIRM_MODIFY": "Pending Modification Confirmation", "AdAccount:Status:STATUS_CONFIRM_MODIFY_FAIL": "Modification Confirmation Failed", "AdAccount:Status:STATUS_LIMIT": "Limited", "AdAccount:Status:STATUS_WAIT_FOR_BPM_AUDIT": "Waiting for BPM Audit", "AdAccount:Status:STATUS_WAIT_FOR_PUBLIC_AUTH": "Waiting for Public Auth", "AdAccount:Status:STATUS_SELF_SERVICE_UNAUDITED": "Self Service Unaudited", "AdAccount:Status:STATUS_CONTRACT_PENDING": "Contract Pending", "AdAccount:Role:ROLE_ADVERTISER": "Advertiser", "AdAccount:Role:ROLE_CHILD_ADVERTISER": "Child Advertiser", "AdAccount:Role:ROLE_CHILD_AGENT": "Child Agent", "AdAccount:Role:ROLE_AGENT": "Agent", "AdAccount:AdvertiserAccountType:RESERVATION": "Reservation", "AdAccount:AdvertiserAccountType:AUCTION": "Auction", "Permission:AdAccounts": "Ad Accounts", "Permission:AdAccounts.Create": "Create Ad Accounts", "Permission:AdAccounts.Edit": "Edit Ad Accounts", "Permission:AdAccounts.Delete": "Delete Ad Accounts", "AdAccountDeletionConfirmationMessage": "Are you sure you want to delete the ad account '{0}'?", "Menu:Assets": "Assets", "NewAsset": "New Asset", "Asset:AssetId": "Asset ID", "Asset:AssetName": "Asset Name", "Asset:AssetType": "Asset Type", "Asset:AdvertiserAccountType": "Advertiser Account Type", "Asset:AdvertiserRole": "Advertiser Role", "Asset:CatalogRole": "Catalog Role", "Asset:AdCreationEligible": "Ad Creation Eligible", "Asset:StoreRole": "Store Role", "Asset:TtAccountRoles": "TT Account Roles", "Asset:OwnerBcName": "Owner BC Name", "Asset:RelationType": "Relation Type", "Asset:RelationType:1": "Owner BC", "Asset:RelationType:2": "Owner Partner", "Asset:RelationType:3": "Owner Individual", "Asset:AssetType:1": "Advertiser", "Asset:AssetType:2": "Catalog", "Asset:AssetType:3": "TikTok Shop", "Asset:AssetType:4": "Pixel", "Asset:AssetType:5": "Lead", "Asset:AssetType:6": "TT Account", "Asset:AdvertiserRole:ADMIN": "Admin", "Asset:AdvertiserRole:OPERATOR": "Operator", "Asset:AdvertiserRole:ANALYST": "Analyst", "Asset:CatalogRole:ADMIN": "Admin", "Asset:CatalogRole:AD_PROMOTE": "Ad Promote", "Asset:AdCreationEligible:NOT_AVAILABLE": "Not Available", "Asset:AdCreationEligible:AVAILABLE": "Available", "Asset:StoreRole:AD_PROMOTION": "Ad Promotion", "Asset:TtAccountRole:POST": "Post", "Asset:TtAccountRole:LIVE": "Live", "Asset:TtAccountRole:DIRECT_MESSAGE": "Direct Message", "Permission:Assets": "Assets", "Permission:Assets.Create": "Create Assets", "Permission:Assets.Edit": "Edit Assets", "Permission:Assets.Delete": "Delete Assets", "AssetDeletionConfirmationMessage": "Are you sure you want to delete the asset '{0}'?", "Menu:Transactions": "Transactions", "NewTransaction": "New Transaction", "Transaction:TransactionId": "Transaction ID", "Transaction:PaymentPortfolioId": "Payment Portfolio ID", "Transaction:PaymentPortfolioName": "Payment Portfolio Name", "Transaction:AccountId": "Account ID", "Transaction:AccountName": "Account Name", "Transaction:Amount": "Amount", "Transaction:Subtotal": "Subtotal", "Transaction:TaxAmount": "Tax Amount", "Transaction:AmountType": "Amount Type", "Transaction:TransactionType": "Transaction Type", "Transaction:BillingType": "Billing Type", "Transaction:Timezone": "Timezone", "Transaction:CreateTime": "Create Time", "Transaction:InvoiceId": "Invoice ID", "Transaction:SerialNumber": "Serial Number", "Transaction:TransactionLevel": "Transaction Level", "Transaction:AmountType:POSITIVE": "Positive", "Transaction:AmountType:NEGATIVE": "Negative", "Transaction:AmountType:OTHER": "Other", "Transaction:TransactionType:BILL_PAYMENT": "Bill Payment", "Transaction:TransactionType:ADD_BALANCE": "Add Balance", "Transaction:TransactionType:CANCELLATION": "Cancellation", "Transaction:TransactionType:PROMOTION_ISSUED": "Promotion Issued", "Transaction:TransactionType:PROMOTION_EXPIRED": "Promotion Expired", "Transaction:TransactionType:INCREASE_BALANCE": "Increase Balance", "Transaction:TransactionType:DECREASE_BALANCE": "Decrease Balance", "Transaction:TransactionType:CARD_VERIFICATION": "Card Verification", "Transaction:TransactionType:CARD_VERIFICATION_REFUND": "Card Verification Refund", "Transaction:TransactionType:ORDER_CREATION_PAYMENT": "Order Creation Payment", "Transaction:TransactionType:ORDER_EDIT_PAYMENT": "Order Edit Payment", "Transaction:TransactionType:RETURN_OF_REMAINING_BUDGET": "Return of Remaining Budget", "Transaction:TransactionType:REFUND": "Refund", "Transaction:BillingType:CASH": "Cash", "Transaction:BillingType:CREDIT": "Credit", "Transaction:TransactionLevel:BC": "Business Center", "Transaction:TransactionLevel:ADVERTISER": "Advertiser", "Transaction:TransactionLevel:PAYMENT_PORTFOLIO": "Payment Portfolio", "Permission:Transactions": "Transactions", "Permission:Transactions.Create": "Create Transactions", "Menu:JobManagement": "Job Management", "JobManagement": "Job Management", "SystemStatus": "System Status", "Configuration": "Configuration", "QuickActions": "Quick Actions", "Workers": "Workers", "JobStatistics": "Job Statistics", "StartSystem": "Start System", "StopSystem": "Stop System", "EditConfiguration": "Edit Configuration", "Name": "Name", "Description": "Description", "IsActive": "Is Active", "ManagerJobCron": "Manager <PERSON>", "RegisterJobCron": "Register <PERSON>", "MaxWorkers": "Max Workers", "WorkerTimeoutMinutes": "Worker Timeout (Minutes)", "Refresh": "Refresh", "AreYouSureToStartSystem": "Are you sure you want to start the job system?", "AreYouSureToStopSystem": "Are you sure you want to stop the job system?", "AreYouSureToCancelWorkerJob": "Are you sure you want to cancel this worker job?", "SystemStarted": "Job system started successfully", "SystemStopped": "Job system stopped successfully", "WorkerJobCancelled": "Worker job cancelled successfully", "Permission:JobManagement": "Job Management", "Permission:JobManagement.Default": "View Job Management", "Permission:JobManagement.Create": "Create Job Management", "Permission:JobManagement.Edit": "Edit Job Management", "Permission:JobManagement.Delete": "Delete Job Management", "NewJobTypeConfiguration": "New Job Type Configuration", "JobTypeConfiguration": "Job Type Configuration", "JobTypeConfigurationDeletionConfirmationMessage": "Are you sure you want to delete the configuration '{0}'?", "CommandType": "Command Type", "DisplayName": "Display Name", "IntervalSeconds": "Interval (Seconds)", "Priority": "Priority", "TimeoutMinutes": "Timeout (Minutes)", "Actions": "Actions", "Edit": "Edit", "Delete": "Delete", "MaxRetryCount": "<PERSON>try Count", "IntervalMustBeGreaterThan0": "Interval must be greater than 0", "PriorityMustBeGreaterThan0": "Priority must be greater than 0", "MaxRetryCountMustBeBetween0And10": "Max retry count must be between 0 and 10", "Permission:Transactions.Sync": "Sync Transactions", "Menu:BusinessApplications": "Business Applications", "NewBusinessApplication": "New Business Application", "BusinessApplication:ApplicationId": "Application ID", "BusinessApplication:Secret": "Secret", "BusinessApplication:AccessToken": "Access Token", "BusinessApplication:AccessTokenCreatedAt": "Access Token Created At", "BusinessApplication:Comment": "Comment", "BusinessApplication:IsActive": "Is Active", "BusinessApplication:Payload": "Payload", "BusinessApplication:Status:DEFAULT": "<PERSON><PERSON><PERSON>", "BusinessApplication:Status:PENDING": "Pending", "BusinessApplication:Status:PROCESSING": "Processing", "BusinessApplication:Status:PROCESSED": "Processed", "BusinessApplication:Status:ERROR": "Error", "Permission:BusinessApplications": "Business Applications", "Permission:BusinessApplications.Create": "Create Business Applications", "Permission:BusinessApplications.Edit": "Edit Business Applications", "Permission:BusinessApplications.Delete": "Delete Business Applications", "BusinessApplicationDeletionConfirmationMessage": "Are you sure you want to delete the business application '{0}'?", "Yes": "Yes", "No": "No", "Permission:Transactions.Edit": "Edit Transactions", "Permission:Transactions.Delete": "Delete Transactions", "TransactionDeletionConfirmationMessage": "Are you sure you want to delete the transaction '{0}'?", "Sync": "Sync", "SyncInProgress": "Syncing...", "SyncConfirmationMessage": "Are you sure you want to sync transactions for BC '{0}'?", "SyncSuccessMessage": "Sync completed successfully. Total: {0}, New: {1}, Updated: {2}", "SyncErrorMessage": "An error occurred during sync. Please try again.", "PermissionDenied": "You don't have permission to perform this action.", "NoNewDataToSync": "No new data to sync.", "InvalidBcId": "Invalid BC ID.", "SyncTimeoutMessage": "Sync operation timed out. Please try again.", "NetworkErrorMessage": "Network error occurred. Please check your connection and try again.", "BcIdNotFound": "BC ID not found.", "ServerErrorMessage": "Server error occurred. Please try again later.", "SyncAllConfirmationMessage": "Are you sure you want to sync transactions for all business applications?", "SyncAllSuccessMessage": "Sync completed successfully for all business applications. Total: {0}, New: {1}, Updated: {2}", "FromDate": "From Date", "ToDate": "To Date", "View": "View", "TransactionDetails": "Transaction Details", "AdditionalInfo": "Additional Information", "ID": "ID", "AutoSearch": "Auto Search", "AutoSearchTooltip": "Automatically search after 1 second when changing search criteria", "SearchActive": "Active Search:", "Keyword": "Keyword", "Min": "Min", "Max": "Max", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "LastBalanceUpdateTime": "Last Balance Update Time", "RechargeFrequency": "Recharge <PERSON>", "BalanceType": "Balance Type", "Level": "Level", "Type": "Type", "AmountType": "Amount Type", "DateRange": "Date Range", "BalanceType:ACCOUNT_BALANCE": "Account <PERSON><PERSON>", "BalanceType:VALID_ACCOUNT_BALANCE": "Valid Account <PERSON>", "BalanceType:FROZEN_BALANCE": "Frozen Balance", "BalanceType:TAX": "Tax", "BalanceType:CASH_BALANCE": "Cash Balance", "BalanceType:VALID_CASH_BALANCE": "Valid Cash Balance", "BalanceType:GRANT_BALANCE": "<PERSON>", "BalanceType:VALID_GRANT_BALANCE": "<PERSON><PERSON>", "Menu:BalanceBusinessCenters": "BC Budget", "NewBalanceBusinessCenter": "New BC Budget", "BalanceBusinessCenter:AccountBalance": "Account <PERSON><PERSON>", "BalanceBusinessCenter:ValidAccountBalance": "Valid Account <PERSON>", "BalanceBusinessCenter:FrozenBalance": "Frozen Balance", "BalanceBusinessCenter:Tax": "Tax", "BalanceBusinessCenter:CashBalance": "Cash Balance", "BalanceBusinessCenter:ValidCashBalance": "Valid Cash Balance", "BalanceBusinessCenter:GrantBalance": "<PERSON>", "BalanceBusinessCenter:ValidGrantBalance": "<PERSON><PERSON>", "BalanceBusinessCenter:MinAccountBalance": "Min Account Balance", "BalanceBusinessCenter:MaxAccountBalance": "Max Account <PERSON>", "BalanceBusinessCenter:MinCashBalance": "Min Cash Balance", "BalanceBusinessCenter:MaxCashBalance": "Max Cash Balance", "BalanceBusinessCenter:MinGrantBalance": "<PERSON>", "BalanceBusinessCenter:MaxGrantBalance": "<PERSON>", "BalanceBusinessCenter:Date": "Date", "BalanceBusinessCenter:Timezone": "Timezone", "BalanceType:CASH": "Cash", "BalanceType:GRANT": "<PERSON>", "BalanceType:BONUS": "Bonus", "BalanceType:CREDIT": "Credit", "BalanceType:PREPAID": "Prepaid", "BalanceSyncStatus:IDLE": "Idle", "BalanceSyncStatus:SYNCING": "Syncing", "BalanceSyncStatus:SUCCESS": "Success", "BalanceSyncStatus:ERROR": "Error", "Low": "Low", "Medium": "Medium", "High": "High", "Today": "Today", "Yesterday": "Yesterday", "ThisWeek": "This Week", "ThisMonth": "This Month", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly", "BalanceDetails": "Balance Details", "Permission:BalanceBusinessCenters": "BC Budget", "Permission:BalanceBusinessCenters.Create": "Create BC Budget", "Permission:BalanceBusinessCenters.Edit": "Edit BC Budget", "Permission:BalanceBusinessCenters.Delete": "Delete BC Budget", "BalanceBusinessCenterDeletionConfirmationMessage": "Are you sure you want to delete the BC budget '{0}'?", "Menu:BalanceAdAccounts": "Balance Ad Accounts", "AdAccounts": "Ad Accounts", "AddAdAccount": "Add Ad Account", "EditAdAccount": "Edit Ad Account", "DeleteAdAccount": "Delete Ad Account", "AdAccountName": "Ad Account Name", "AdvertiserId": "Advertiser ID", "NoAdAccounts": "No ad accounts yet", "RemoveAdAccount": "Remove Ad Account", "UpdateAdAccount": "Update Ad Account", "NewBalanceAdAccount": "New Balance Ad Account", "BalanceAdAccount:AdvertiserName": "Advertiser Name", "BalanceAdAccount:AccountBalance": "Account <PERSON><PERSON>", "BalanceAdAccount:AdvertiserType": "Advertiser Type", "BalanceAdAccount:DailyBudget": "Daily Budget", "BalanceAdAccount:LifetimeBudget": "Lifetime Budget", "BalanceAdAccount:BudgetMode": "Budget Mode", "BalanceAdAccount:AdvertiserStatus": "Advertiser Status", "BalanceAdAccount:AdvertiserStatus:SHOW_ACCOUNT_STATUS_NOT_APPROVED": "Not Approved", "BalanceAdAccount:AdvertiserStatus:SHOW_ACCOUNT_STATUS_APPROVED": "Approved", "BalanceAdAccount:AdvertiserStatus:SHOW_ACCOUNT_STATUS_IN_REVIEW": "In Review", "BalanceAdAccount:AdvertiserStatus:SHOW_ACCOUNT_STATUS_PUNISHED": "Punished", "BalanceAdAccount:AdvertiserType:RESERVATION": "Reservation", "BalanceAdAccount:AdvertiserType:AUCTION": "Auction", "BalanceAdAccount:BudgetMode:UNLIMITED": "Unlimited", "BalanceAdAccount:BudgetMode:MONTHLY_BUDGET": "Monthly Budget", "BalanceAdAccount:BudgetMode:DAILY_BUDGET": "Daily Budget", "BalanceAdAccount:BudgetMode:CUSTOM_BUDGET": "Custom Budget", "BalanceAdAccount:Budget": "Budget", "BalanceAdAccount:CreateTime": "Create Time", "BalanceAdAccount:BasicInfo": "Basic Information", "BalanceAdAccount:ContactInfo": "Contact Information", "BalanceAdAccount:Settings": "Settings", "BalanceAdAccount:BalanceInfo": "Balance Information", "BalanceAdAccount:BudgetInfo": "Budget Information", "BalanceAdAccount:BudgetRestrictions": "Budget Restrictions", "BalanceAdAccount:CreatedAt": "Created At", "BalanceAdAccount:UpdatedAt": "Updated At", "BalanceAdAccount:DeletedAt": "Deleted At", "BalanceAdAccount:Notes": "Notes", "BalanceAdAccount:IsRemoved": "Is Removed", "BalanceAdAccount:RemovedAt": "Removed At", "BalanceAdAccount:ContactName": "Contact Name", "BalanceAdAccount:ContactEmail": "Contact Email", "BalanceAdAccount:Timezone": "Timezone", "BalanceAdAccount:AccountOpenDays": "Account Open Days", "BalanceAdAccount:BalanceReminder": "Balance Reminder", "BalanceAdAccount:ValidAccountBalance": "Valid Account <PERSON>", "BalanceAdAccount:FrozenBalance": "Frozen Balance", "BalanceAdAccount:Tax": "Tax", "BalanceAdAccount:CashBalance": "Cash Balance", "BalanceAdAccount:ValidCashBalance": "Valid Cash Balance", "BalanceAdAccount:GrantBalance": "<PERSON>", "BalanceAdAccount:ValidGrantBalance": "<PERSON><PERSON>", "BalanceAdAccount:TransferableAmount": "Transferable Amount", "BalanceAdAccount:BudgetCost": "Budget Cost", "BalanceAdAccount:BudgetRemaining": "Budget Remaining", "BalanceAdAccount:Date": "Last Sync Time", "Permission:BalanceAdAccounts": "Balance Ad Accounts", "Permission:BalanceAdAccounts.Create": "Create Balance Ad Accounts", "Permission:BalanceAdAccounts.Edit": "Edit Balance Ad Accounts", "Permission:BalanceAdAccounts.Delete": "Delete Balance Ad Accounts", "BalanceAdAccountDeletionConfirmationMessage": "Are you sure you want to delete the balance ad account '{0}'?", "Modal:DetailTitle": "Detail", "Modal:Close": "Close", "Modal:BasicInformation": "Basic Information", "Modal:ContactInformation": "Contact Information", "Modal:BalanceInformation": "Balance Information", "Modal:CashBalance": "Cash Balance", "Modal:GrantBalance": "<PERSON>", "Modal:BudgetInformation": "Budget Information", "Modal:TimeInformation": "Time Information", "Modal:AccountName": "Account Name", "Modal:LastUpdate": "Last Update", "Modal:AccountOpenDays": "Account Open Days", "Modal:TransferableAmount": "Transferable Amount", "Modal:BudgetCost": "Budget Cost", "Modal:BudgetRemaining": "Budget Remaining", "Modal:BalanceReminder": "Balance Reminder", "Modal:CreateTime": "Create Time", "Menu:RecordTransactionAdAccounts": "Record Transaction AdAccounts", "NewRecordTransactionAdAccount": "New Record Transaction AdAccount", "AdAccountTransaction:AdvertiserName": "Advertiser Name", "AdAccountTransaction:Amount": "Amount", "AdAccountTransaction:Date": "Date", "AdAccountTransaction:FundsType": "Funds Type", "AdAccountTransaction:TransferType": "Transfer Type", "AdAccountTransaction:Timezone": "Timezone", "AdAccountTransaction:FundsType:FUNDS_TYPE_CASH": "Cash", "AdAccountTransaction:FundsType:FUNDS_TYPE_GRANT": "Grant/Voucher", "AdAccountTransaction:TransferType:TRANS_TYPE_TRANSFER": "Transfer", "AdAccountTransaction:TransferType:TRANS_TYPE_TAX": "Consumption", "AdAccountTransaction:TransferType:TRANS_TYPE_COST": "Tax", "Permission:RecordTransactionAdAccounts": "Record Transaction AdAccounts", "Permission:RecordTransactionAdAccounts.Create": "Create Record Transaction AdAccounts", "Permission:RecordTransactionAdAccounts.Edit": "Edit Record Transaction AdAccounts", "Permission:RecordTransactionAdAccounts.Delete": "Delete Record Transaction AdAccounts", "RecordTransactionAdAccountDeletionConfirmationMessage": "Are you sure you want to delete the record transaction ad account '{0}'?", "Menu:RecordTransactionBcs": "Record Transaction BCs", "NewRecordTransactionBc": "New Record Transaction BC", "BcTransactionRecord:Date": "Transaction Date", "BcTransactionRecord:Amount": "Transaction Amount", "BcTransactionRecord:Timezone": "Timezone", "BcTransactionRecord:FundsType": "Funds Type", "BcTransactionRecord:InvoiceId": "Invoice ID", "BcTransactionRecord:InvoiceSerialNumber": "Invoice Serial Number", "BcTransactionRecord:FundsType:FUNDS_TYPE_CASH": "Cash", "BcTransactionRecord:FundsType:FUNDS_TYPE_GRANT": "Grant/Voucher", "Permission:RecordTransactionBcs": "Record Transaction BCs", "Permission:RecordTransactionBcs.Create": "Create Record Transaction BCs", "Permission:RecordTransactionBcs.Edit": "Edit Record Transaction BCs", "Permission:RecordTransactionBcs.Delete": "Delete Record Transaction BCs", "RecordTransactionBcDeletionConfirmationMessage": "Are you sure you want to delete the record transaction BC '{0}'?", "Menu:Campaigns": "Campaigns", "NewCampaign": "New Campaign", "Campaign:CampaignId": "Campaign ID", "Campaign:CampaignSystemOrigin": "Campaign System Origin", "Campaign:CreateTime": "Create Time", "Campaign:ModifyTime": "Modify Time", "Campaign:ObjectiveType": "Objective Type", "Campaign:AppPromotionType": "App Promotion Type", "Campaign:VirtualObjectiveType": "Virtual Objective Type", "Campaign:SalesDestination": "Sales Destination", "Campaign:IsSearchCampaign": "Is Search Campaign", "Campaign:IsSmartPerformanceCampaign": "Is Smart Performance Campaign", "Campaign:CampaignType": "Campaign Type", "Campaign:AppId": "App ID", "Campaign:IsAdvancedDedicatedCampaign": "Is Advanced Dedicated Campaign", "Campaign:DisableSkanCampaign": "Disable SKAN Campaign", "Campaign:BidAlignType": "Bid Align Type", "Campaign:CampaignAppProfilePageState": "Campaign App Profile <PERSON>", "Campaign:RfCampaignType": "RF Campaign Type", "Campaign:CampaignProductSource": "Campaign Product Source", "Campaign:CatalogEnabled": "Catalog Enabled", "Campaign:CampaignName": "Campaign Name", "Campaign:SpecialIndustries": "Special Industries", "Campaign:BudgetOptimizeOn": "Budget Optimize On", "Campaign:BidType": "Bid Type", "Campaign:DeepBidType": "Deep Bid Type", "Campaign:RoasBid": "ROAS Bid", "Campaign:OptimizationGoal": "Optimization Goal", "Campaign:BudgetMode": "Budget Mode", "Campaign:Budget": "Budget", "Campaign:RtaId": "RTA ID", "Campaign:RtaBidEnabled": "RTA Bid Enabled", "Campaign:RtaProductSelectionEnabled": "RTA Product Selection Enabled", "Campaign:OperationStatus": "Operation Status", "Campaign:SecondaryStatus": "Secondary Status", "Campaign:PostbackWindowMode": "Postback Window Mode", "Campaign:IsNewStructure": "Is New Structure", "Campaign:Objective": "Objective", "Campaign:CampaignSystemOrigin:PROMOTE": "Promote", "Campaign:AppPromotionType:APP_INSTALL": "App Install", "Campaign:AppPromotionType:APP_RETARGETING": "App Retargeting", "Campaign:AppPromotionType:APP_PREREGISTRATION": "App Preregistration", "Campaign:AppPromotionType:APP_POSTS_PROMOTION": "App Posts Promotion", "Campaign:VirtualObjectiveType:SALES": "Sales", "Campaign:SalesDestination:TIKTOK_SHOP": "TikTok Shop", "Campaign:SalesDestination:WEBSITE": "Website", "Campaign:SalesDestination:APP": "App", "Campaign:CampaignType:REGULAR_CAMPAIGN": "Regular Campaign", "Campaign:CampaignType:IOS14_CAMPAIGN": "iOS 14 Campaign", "Campaign:BidAlignType:SAN": "SAN", "Campaign:BidAlignType:SKAN": "SKAN", "Campaign:CampaignAppProfilePageState:INVALID": "Invalid", "Campaign:CampaignAppProfilePageState:UNSET": "Unset", "Campaign:CampaignAppProfilePageState:ON": "On", "Campaign:CampaignAppProfilePageState:OFF": "Off", "Campaign:RfCampaignType:STANDARD": "Standard", "Campaign:RfCampaignType:PULSE": "Pulse", "Campaign:RfCampaignType:TOPVIEW": "Topview", "Campaign:CampaignProductSource:CATALOG": "Catalog", "Campaign:CampaignProductSource:STORE": "Store", "Campaign:SpecialIndustries:HOUSING": "Housing", "Campaign:SpecialIndustries:EMPLOYMENT": "Employment", "Campaign:SpecialIndustries:CREDIT": "Credit", "Campaign:BidType:CPC": "CPC", "Campaign:BidType:CPM": "CPM", "Campaign:BidType:CPA": "CPA", "Campaign:BidType:ROAS": "ROAS", "Campaign:DeepBidType:NONE": "None", "Campaign:DeepBidType:APP_INSTALL": "App Install", "Campaign:DeepBidType:PURCHASE": "Purchase", "Campaign:DeepBidType:REGISTER": "Register", "Campaign:DeepBidType:ADD_TO_CART": "Add to Cart", "Campaign:DeepBidType:VIEW_CONTENT": "View Content", "Campaign:OptimizationGoal:TRAFFIC": "Traffic", "Campaign:OptimizationGoal:APP_INSTALL": "App Install", "Campaign:OptimizationGoal:PURCHASE": "Purchase", "Campaign:OptimizationGoal:REGISTER": "Register", "Campaign:OptimizationGoal:ADD_TO_CART": "Add to Cart", "Campaign:OptimizationGoal:VIEW_CONTENT": "View Content", "Campaign:OptimizationGoal:ENGAGEMENT": "Engagement", "Campaign:OptimizationGoal:BRAND_AWARENESS": "Brand Awareness", "Campaign:OptimizationGoal:REACH": "Reach", "Campaign:PostbackWindowMode:POSTBACK_WINDOW_MODE1": "Postback Window Mode 1", "Campaign:PostbackWindowMode:POSTBACK_WINDOW_MODE2": "Postback Window Mode 2", "Campaign:PostbackWindowMode:POSTBACK_WINDOW_MODE3": "Postback Window Mode 3", "Campaign:Objective:APP": "App", "Campaign:Objective:LANDING_PAGE": "<PERSON>", "Permission:Campaigns": "Campaigns", "Permission:Campaigns.Create": "Create Campaigns", "Permission:Campaigns.Edit": "Edit Campaigns", "Permission:Campaigns.Delete": "Delete Campaigns", "CampaignDeletionConfirmationMessage": "Are you sure you want to delete the campaign '{0}'?", "Menu:CostProfiles": "Cost Profiles", "NewCostProfile": "New Cost Profile", "CostProfile:AdvertiserName": "Advertiser Name", "CostProfile:Amount": "Total Amount", "CostProfile:CashAmount": "Cash Amount", "CostProfile:GrantAmount": "<PERSON>", "CostProfile:TaxAmount": "Tax Amount", "CostProfile:Date": "Sync Date", "CostProfile:CostProfileType": "Cost Profile Type", "CostProfile:CostProfileType:Cash": "Cash", "CostProfile:CostProfileType:Grant": "<PERSON>", "CostProfile:CostProfileType:Tax": "Tax", "CostProfile:CostProfileType:Total": "Total", "Permission:CostProfiles": "Cost Profiles", "Permission:CostProfiles.Create": "Create Cost Profiles", "Permission:CostProfiles.Edit": "Edit Cost Profiles", "Permission:CostProfiles.Delete": "Delete Cost Profiles", "CostProfileDeletionConfirmationMessage": "Are you sure you want to delete the cost profile '{0}'?", "Menu:ReportIntegratedCampaigns": "Report Integrated Campaigns", "NewReportIntegratedCampaign": "New Report Integrated Campaign", "ReportIntegratedCampaign:CampaignId": "Campaign ID", "ReportIntegratedCampaign:CampaignName": "Campaign Name", "ReportIntegratedCampaign:Date": "Report Date", "ReportIntegratedCampaign:Spend": "Spend", "ReportIntegratedCampaign:CashSpend": "Cash Spend", "ReportIntegratedCampaign:VoucherSpend": "Voucher Spend", "ReportIntegratedCampaign:Impressions": "Impressions", "ReportIntegratedCampaign:Clicks": "<PERSON>licks", "ReportIntegratedCampaign:Conversion": "Conversion", "ReportIntegratedCampaign:CostPerConversion": "Cost Per Conversion", "ReportIntegratedCampaign:ConversionRate": "Conversion Rate", "ReportIntegratedCampaign:Result": "Result", "ReportIntegratedCampaign:CostPerResult": "Cost <PERSON>", "ReportIntegratedCampaign:ObjectiveType": "Objective Type", "ReportIntegratedCampaign:PromotionType": "Promotion Type", "ReportIntegratedCampaign:CampaignDedicateType": "Campaign Dedicate Type", "ReportIntegratedCampaign:OnsiteShoppingRoas": "Onsite Shopping ROAS", "ReportIntegratedCampaign:TotalOnsiteShoppingValue": "Total Onsite Shopping Value", "ReportIntegratedCampaign:OnsiteShopping": "Onsite Shopping", "ReportIntegratedCampaign:CostPerOnsiteShopping": "Cost Per Onsite Shopping", "ReportIntegratedCampaign:ValuePerOnsiteShopping": "Value Per Onsite Shopping", "Permission:ReportIntegratedCampaigns": "Report Integrated Campaigns", "Permission:ReportIntegratedCampaigns.Create": "Create Report Integrated Campaigns", "Permission:ReportIntegratedCampaigns.Edit": "Edit Report Integrated Campaigns", "Permission:ReportIntegratedCampaigns.Delete": "Delete Report Integrated Campaigns", "ReportIntegratedCampaignDeletionConfirmationMessage": "Are you sure you want to delete the report integrated campaign '{0}'?", "Menu:ReportIntegratedAdGroups": "Report Integrated AdGroups", "NewReportIntegratedAdGroup": "New Report Integrated AdGroup", "ReportIntegratedAdGroup:AdGroupId": "AdGroup ID", "ReportIntegratedAdGroup:AdGroupName": "AdGroup Name", "ReportIntegratedAdGroup:CampaignId": "Campaign ID", "ReportIntegratedAdGroup:Date": "Report Date", "ReportIntegratedAdGroup:Spend": "Spend", "ReportIntegratedAdGroup:Impressions": "Impressions", "ReportIntegratedAdGroup:Clicks": "Clicks (Destination)", "ReportIntegratedAdGroup:Ctr": "CTR (Destination)", "ReportIntegratedAdGroup:Cpm": "CPM", "ReportIntegratedAdGroup:Cpc": "CPC (Destination)", "ReportIntegratedAdGroup:Conversion": "Conversion", "ReportIntegratedAdGroup:CostPerConversion": "Cost Per Conversion", "ReportIntegratedAdGroup:ConversionRateV2": "Conversion Rate (CVR)", "ReportIntegratedAdGroup:Reach": "Reach", "ReportIntegratedAdGroup:Frequency": "Frequency", "ReportIntegratedAdGroup:OnsiteShoppingRoas": "Onsite Shopping ROAS", "ReportIntegratedAdGroup:TotalOnsiteShoppingValue": "Total Onsite Shopping Value", "ReportIntegratedAdGroup:OnsiteShopping": "Onsite Shopping", "ReportIntegratedAdGroup:CostPerOnsiteShopping": "Cost Per Onsite Shopping", "ReportIntegratedAdGroup:ValuePerOnsiteShopping": "Value Per Onsite Shopping", "ReportIntegratedAdGroup:OnsiteOnWebDetail": "Onsite On Web Detail", "ReportIntegratedAdGroup:OnsiteOnWebCart": "Onsite On Web Cart", "ReportIntegratedAdGroup:OnsiteInitiateCheckoutCount": "Onsite Initiate Checkout Count", "ReportIntegratedAdGroup:PlacementType": "Placement Type", "ReportIntegratedAdGroup:Budget": "AdGroup Budget", "ReportIntegratedAdGroup:SmartTarget": "Smart Target", "ReportIntegratedAdGroup:BillingEvent": "Billing Event", "ReportIntegratedAdGroup:BidStrategy": "Bid Strategy", "ReportIntegratedAdGroup:Bid": "Bid", "Permission:ReportIntegratedAdGroups": "Report Integrated AdGroups", "Permission:ReportIntegratedAdGroups.Create": "Create Report Integrated AdGroups", "Permission:ReportIntegratedAdGroups.Edit": "Edit Report Integrated AdGroups", "Permission:ReportIntegratedAdGroups.Delete": "Delete Report Integrated AdGroups", "ReportIntegratedAdGroupDeletionConfirmationMessage": "Are you sure you want to delete the report integrated adgroup '{0}'?", "Menu:ReportIntegratedAds": "Report Integrated Ads", "NewReportIntegratedAd": "New Report Integrated Ad", "ReportIntegratedAd:CampaignId": "Campaign ID", "ReportIntegratedAd:AdGroupId": "AdGroup ID", "ReportIntegratedAd:AdId": "Ad ID", "ReportIntegratedAd:AdName": "Ad Name", "ReportIntegratedAd:AdText": "Ad Text", "ReportIntegratedAd:CallToAction": "Call To Action", "ReportIntegratedAd:ImageMode": "Image Mode", "ReportIntegratedAd:IsAco": "Is ACO", "ReportIntegratedAd:IsSmartCreative": "Is Smart Creative", "ReportIntegratedAd:Date": "Report Date", "ReportIntegratedAd:Spend": "Spend", "ReportIntegratedAd:Impressions": "Impressions", "ReportIntegratedAd:Clicks": "Clicks (Destination)", "ReportIntegratedAd:Ctr": "CTR (Destination)", "ReportIntegratedAd:Cpm": "CPM", "ReportIntegratedAd:Cpc": "CPC (Destination)", "ReportIntegratedAd:Conversion": "Conversion", "ReportIntegratedAd:CostPerConversion": "Cost Per Conversion", "ReportIntegratedAd:Reach": "Reach", "ReportIntegratedAd:Frequency": "Frequency", "ReportIntegratedAd:VideoPlayActions": "Video Play Actions", "ReportIntegratedAd:VideoWatched2s": "Video Watched 2s", "ReportIntegratedAd:VideoWatched6s": "Video Watched 6s", "ReportIntegratedAd:VideoViewsP25": "Video Views 25%", "ReportIntegratedAd:VideoViewsP50": "Video Views 50%", "ReportIntegratedAd:VideoViewsP75": "Video Views 75%", "ReportIntegratedAd:VideoViewsP100": "Video Views 100%", "ReportIntegratedAd:AverageVideoPlay": "Average Video Play", "ReportIntegratedAd:EngagedView": "Engaged View", "ReportIntegratedAd:OnsiteShoppingRoas": "Onsite Shopping ROAS", "ReportIntegratedAd:TotalOnsiteShoppingValue": "Total Onsite Shopping Value", "ReportIntegratedAd:OnsiteShopping": "Onsite Shopping", "ReportIntegratedAd:CostPerOnsiteShopping": "Cost Per Onsite Shopping", "ReportIntegratedAd:ValuePerOnsiteShopping": "Value Per Onsite Shopping", "ReportIntegratedAd:OnsiteOnWebDetail": "Onsite On Web Detail", "ReportIntegratedAd:OnsiteOnWebCart": "Onsite On Web Cart", "ReportIntegratedAd:OnsiteInitiateCheckoutCount": "Onsite Initiate Checkout Count", "ReportIntegratedAd:LiveViews": "Live Views", "ReportIntegratedAd:LiveUniqueViews": "Live Unique Views", "ReportIntegratedAd:LiveEffectiveViews": "Live Effective Views", "ReportIntegratedAd:LiveProductClicks": "Live Product Clicks", "ReportIntegratedAd:Type:Summary": "Summary", "ReportIntegratedAd:Type:Detailed": "Detailed", "ReportIntegratedAd:Type:Realtime": "Real Time", "Permission:ReportIntegratedAds": "Report Integrated Ads", "Permission:ReportIntegratedAds.Create": "Create Report Integrated Ads", "Permission:ReportIntegratedAds.Edit": "Edit Report Integrated Ads", "Permission:ReportIntegratedAds.Delete": "Delete Report Integrated Ads", "ReportIntegratedAdDeletionConfirmationMessage": "Are you sure you want to delete the report integrated ad '{0}'?", "OptimizationGoal:VALUE": "Value", "DeepBidType:VO_MIN_ROAS": "Value Optimization with Min ROAS", "GmvMaxShoppingAdsType:PRODUCT": "Product", "GmvMaxShoppingAdsType:LIVE": "Live", "GmvMaxProductSpecificType:ALL": "All Products", "GmvMaxProductSpecificType:CUSTOMIZED_PRODUCTS": "Customized Products", "GmvMaxProductSpecificType:UNSET": "Unset", "GmvMaxScheduleType:SCHEDULE_FROM_NOW": "Schedule From Now", "GmvMaxScheduleType:SCHEDULE_START_END": "Schedule Start End", "GmvMaxVideoSpecificType:AUTO_SELECTION": "Auto Selection", "GmvMaxVideoSpecificType:CUSTOM_SELECTION": "Custom Selection", "GmvMaxVideoSpecificType:UNSET": "Unset", "GmvMaxRoiProtectionStatus:IN_EFFECT": "In Effect", "GmvMaxRoiProtectionStatus:NOT_ELIGIBLE": "Not Eligible", "Authorize": "Authorize", "BetaAuthorization": "Beta Authorization", "Step1:AuthorizeWithTikTok": "Step 1: Authorize with TikTok", "Step2:EnterCallbackUrl": "Step 2: Enter Callback URL", "BetaAuthStep1Description": "Click the button below to open TikTok authorization in a new tab. After authorization, TikTok will redirect you to a URL with authorization parameters.", "BetaAuthStep2Description": "Copy the callback URL from TikTok and paste it below, then click 'Get Token' to process the authorization.", "BetaAuthorize": "Beta Authorize", "CallbackUrl": "Callback URL", "CallbackUrlPlaceholder": "https://comme.asia?AuthCode=xxx&Code=xxx&State=xxx", "GetToken": "Get Token", "AppIdAndBusinessIdRequired": "App ID and Business ID are required", "AuthorizationTabOpened": "Authorization tab opened. Please complete the authorization process.", "CallbackUrlRequired": "Please enter the callback URL", "InvalidCallbackUrl": "Invalid callback URL. Missing required parameters (AuthCode, Code, State)", "InvalidUrlFormat": "Invalid URL format", "AuthorizeConfirmationMessage": "Are you sure you want to authorize this application with TikTok?", "RedirectUriNotConfigured": "Redirect URI is not configured", "Menu:Customers": "Customer Management", "NewCustomer": "New Customer", "Customer:CustomerId": "Customer ID", "Customer:CustomerName": "Customer Name", "Customer:AccountName": "Account Name", "Customer:ShopId": "Shop ID", "Customer:ShopName": "Shop Name", "Customer:PhoneNumber": "Phone Number", "Customer:CustomerType": "Customer Type", "Customer:Website": "Website", "Customer:AdAccounts": "Ad Accounts", "Customer:CustomerType:Agency": "Agency", "Customer:CustomerType:Retail": "Retail", "Permission:Customers": "Customer Management", "Permission:Customers.Create": "Create Customer", "Permission:Customers.Edit": "Edit Customer", "Permission:Customers.Delete": "Delete Customer", "Permission:Customers.Import": "Import Customers", "Permission:SystemCache": "System Cache", "Permission:SystemCache.Monitor": "Monitor <PERSON>", "Permission:SystemCache.Clear": "<PERSON>ache", "Permission:SystemCache.ClearAll": "Clear All Cache", "Menu:SystemCache": "System Cache", "CustomerDeletionConfirmationMessage": "Are you sure you want to delete the customer '{0}'?", "CustomerIdAlreadyExists": "Customer ID '{0}' already exists", "CustomerManagement": "Customer Management", "CustomerAccounts": "Customer Accounts", "AddAccount": "Add Account", "AddAccountForCustomer": "Add Account for Customer", "Customer": "Customer", "AdvertiserInformation": "Advertiser Information", "ShopInformation": "Shop Information", "EditAccount": "Edit Account", "CustomerInformation": "Customer Information", "CustomerCode": "Customer Code", "CustomerAccountList": "Customer Account List", "CustomerAccountsOf": "Customer Accounts of", "AddNewAccount": "Add New Account", "EditAccountInfo": "Edit Account", "AdvertiserName": "Advertiser Name", "ShopId": "Shop ID", "ShopName": "Shop Name", "CreationDate": "Creation Date", "Accounts": "accounts", "ZeroAccounts": "0 accounts", "AccountList": "Account List", "NoAccessPermission": "No Access Permission", "NoCustomerAccessMessage": "You don't have permission to access the customer management page.", "ContactAdminForPermission": "Please contact administrator to get permission.", "PleaseSelectCustomer": "Please select a customer", "PleaseEnterAdvertiserOrShop": "Please enter at least Advertiser or Shop information", "AccountAddedSuccessfully": "Account added successfully", "AccountUpdatedSuccessfully": "Account updated successfully", "ErrorAddingAccount": "Error adding account", "ErrorUpdatingAccount": "Error updating account", "NoAccountsFound": "No accounts found", "AccountNotFound": "Account information not found", "ConfirmDeleteAccount": "Are you sure you want to delete this account from the customer?", "AccountDeletedSuccessfully": "Account deleted successfully", "ErrorDeletingAccount": "Error deleting account", "ErrorLoadingAccounts": "Error loading account list", "Import": "Import", "Save": "Save", "Cancel": "Cancel", "Update": "Update", "ImportProcessedMessage": "Processed {0} rows. Created: {1}, Skipped: {2}", "ImportFailed": "Import failed", "NotificationRules:Title": "Notification Rules", "NotificationRules:HeroText": "Notification Rules Management", "Menu:SupportManagement": "Support Management", "SupportManagement": "Support Management", "Permission:SupportManagement": "Support Management", "Permission:SupportManagement.Import": "Import Support Management", "SupportManagementDescription:Title": "Support List Management", "Menu:Facts": "BC Data", "Menu:FactBalance": "Balance Information", "Menu:FactCampaign": "Campaign Analysis", "Menu:FactGmvMaxCampaign": "GMV Max Campaign Analysis", "Menu:FactGmvMaxProduct": "GMV Max Product Analysis", "Menu:GmvMax": "GMV Max Analysis", "Permission:FactGmvMax": "GMV Max", "Permission:FactGmvMax.ViewSpending": "View GMV Max Spending", "Permission:FactGmvMax.ViewMetrics": "View GMV Max Metrics", "Permission:FactGmvMax.ViewAll": "View All GMV Max Information", "Permission:FactGmvMax.ViewVideo": "View GMV Max Video Creative", "Permission:FactGmvMax.ViewAllAdvertisers": "View All GMV Max Advertisers", "Permission:FactGmvMaxCampaigns": "GMV Max Campaign Analysis", "Permission:FactGmvMaxCampaigns.Default": "Access GMV Max Campaign Analysis", "Permission:FactGmvMaxCampaigns.View": "View GMV Max Campaign Reports", "Permission:FactGmvMaxCampaigns.Export": "Export GMV Max Campaign Reports", "Menu:UserAccessManagement": "Ad Account Permission Management", "UserAccessManagement": "Ad Account Permission Management", "UserAccessManagement:Title": "Ad Account Permission Management", "Permission:AdAccountPermissionManagement": "Ad Account Permission Management", "Permission:ReceiveNotification": "Receive Notification", "SearchUser": "Search User", "SearchByNameEmailUsername": "Search by name, email, or username", "SearchByNameIdBc": "Search by name, ID, or BC", "AllRoles": "All Roles", "Active": "Active", "Inactive": "Inactive", "AdAccountAccess": "Ad Account Access", "HasAccess": "Has Access", "NoAccess": "No Access", "Statistics": "Statistics", "TotalUsers": "Total Users", "ActiveUsers": "Active Users", "UsersWithAccess": "Users with Access", "AssignedAdAccounts": "Assigned Ad Accounts", "UserName": "User Name", "Email": "Email", "AdAccountsCount": "Ad Accounts Count", "ManageAccess": "Manage Access", "ViewAccess": "View Access", "ManageAdAccountAccess": "Manage Ad Account Access", "AvailableAdAccounts": "Available Ad Accounts", "Close": "Close", "ErrorLoadingStatistics": "Error loading statistics", "ErrorLoadingAdAccounts": "Error loading ad accounts", "AdAccountAccessUpdated": "Ad account access updated successfully", "ErrorUpdatingAdAccountAccess": "Error updating ad account access", "Permission:FactGmvMaxCampaigns.ViewSpending": "View Ad Account Spending", "Permission:FactGmvMaxCampaigns.ViewMetrics": "View Ad Metrics", "Permission:FactGmvMaxCampaigns.ViewAll": "View Complete Ad Information", "Permission:FactGmvMaxCampaigns.ViewAllAdvertisers": "View All Metrics of All Advertisers (Admin Level)", "Permission:FactGmvMaxProducts": "GMV Max Product Analysis", "Permission:FactGmvMaxProducts.ViewSpending": "View Ad Account Spending", "Permission:FactGmvMaxProducts.ViewMetrics": "View Ad Metrics", "Permission:FactGmvMaxProducts.ViewAll": "View Complete Ad Information", "Permission:FactGmvMaxProducts.ViewAllAdvertisers": "View All Metrics of All Advertisers (Admin Level)", "Notifications": "Notifications", "MarkAllAsRead": "Mark all as read", "NoNewNotifications": "No new notifications", "ViewAllNotifications": "View all notifications", "JustNow": "Just now", "MinutesAgo": "minutes ago", "HoursAgo": "hours ago", "DaysAgo": "days ago"}}