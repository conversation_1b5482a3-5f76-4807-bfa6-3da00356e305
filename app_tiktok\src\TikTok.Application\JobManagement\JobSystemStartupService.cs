using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Repositories;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Service để khởi động hệ thống job khi ứng dụng khởi động
    /// </summary>
    public class JobSystemStartupService : BackgroundWorkerBase
    {
        private readonly ILogger<JobSystemStartupService> _logger;
        private readonly IServiceProvider _serviceProvider;

        public JobSystemStartupService(
            ILogger<JobSystemStartupService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogDebug("Starting job system startup service...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var jobConfigurationStore = scope.ServiceProvider.GetRequiredService<JobConfigurationStore>();
                // Lấy ra worker cập nhật trạng thái
                var workerRepository = scope.ServiceProvider.GetRequiredService<IRepository<WorkerInfoEntity, Guid>>();
                var processWorkers = await workerRepository.GetListAsync(x => x.Status == Enums.WorkerStatus.Working);
                if (processWorkers != null && processWorkers.Any())
                {
                    foreach (var item in processWorkers)
                    {
                        item.Status = Enums.WorkerStatus.Idle;
                        item.BusinessApplicationId = null;
                        item.Result = null;
                    }
                    await workerRepository.UpdateManyAsync(processWorkers);
                }

                // Lấy ra Job đồng bộ để cập nhật trạng thái
                var syncJobs = scope.ServiceProvider.GetRequiredService<IJobRepository>();
                var syncJob = await syncJobs.GetListAsync(x => x.Status == Enums.JobStatus.InProcess);
                if (syncJob != null && syncJob.Any())
                {
                    foreach (var item in syncJob)
                    {
                        item.Status = Enums.JobStatus.Cancelled;
                    }
                    await syncJobs.UpdateManyAsync(syncJob);
                }

                // Khởi động hệ thống job
                await jobConfigurationStore.StartJobSystemAsync();

                _logger.LogDebug("Job system startup service started successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start job system startup service");
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogDebug("Stopping job system startup service...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var jobConfigurationStore = scope.ServiceProvider.GetRequiredService<JobConfigurationStore>();

                // Dừng hệ thống job
                await jobConfigurationStore.StopJobSystemAsync();

                _logger.LogDebug("Job system startup service stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to stop job system startup service");
            }
        }
    }
}