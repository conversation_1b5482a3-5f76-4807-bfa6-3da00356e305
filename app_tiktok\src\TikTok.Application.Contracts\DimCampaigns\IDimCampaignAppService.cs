using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DimCampaigns
{
    /// <summary>
    /// Service interface for DimCampaign operations
    /// </summary>
    public interface IDimCampaignAppService : IApplicationService
    {
        /// <summary>
        /// Get all active campaigns for dropdown/multiselect
        /// </summary>
        Task<List<DimCampaignDto>> GetActiveCampaignsAsync();

        /// <summary>
        /// Get campaigns by type
        /// </summary>
        Task<List<DimCampaignDto>> GetCampaignsByTypeAsync(string campaignType);

        /// <summary>
        /// Get campaigns by advertiser
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>List of campaigns for specified advertiser</returns>
        Task<List<DimCampaignDto>> GetCampaignsByAdvertiserAsync(string advertiserId);
    }
}
