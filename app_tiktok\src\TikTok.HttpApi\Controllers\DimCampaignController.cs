using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using TikTok.DimCampaigns;
using TikTok.Permissions;

namespace TikTok.HttpApi.Controllers
{
    /// <summary>
    /// Controller for DimCampaign operations - Reusable across multiple features
    /// </summary>
    [Route("api/dim-campaigns")]
    [Authorize]
    public class DimCampaignController : AbpControllerBase
    {
        private readonly IDimCampaignAppService _dimCampaignAppService;

        public DimCampaignController(IDimCampaignAppService dimCampaignAppService)
        {
            _dimCampaignAppService = dimCampaignAppService;
        }

        /// <summary>
        /// Get all campaigns for dropdowns/multiselects
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<object>> GetCampaignsAsync([FromQuery] string format = "full", [FromQuery] string? campaignType = null)
        {
            try
            {
                List<DimCampaignDto> campaigns;

                if (!string.IsNullOrWhiteSpace(campaignType))
                {
                    campaigns = await _dimCampaignAppService.GetCampaignsByTypeAsync(campaignType.ToUpperInvariant());
                }
                else
                {
                    campaigns = await _dimCampaignAppService.GetActiveCampaignsAsync();
                }

                if (format.Equals("simple", StringComparison.OrdinalIgnoreCase))
                {
                    // Return simplified format for UI components like Syncfusion MultiSelect
                    var simplifiedCampaigns = campaigns.Select(c => new
                    {
                        text = c.CampaignName ?? c.CampaignId,
                        value = c.CampaignId,
                        campaignType = c.CampaignType,
                        objectiveType = c.ObjectiveType
                    }).ToList();
                    return Ok(simplifiedCampaigns);
                }
                
                return Ok(campaigns);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get campaigns by type
        /// </summary>
        [HttpGet("by-type/{campaignType}")]
        public async Task<ActionResult<List<DimCampaignDto>>> GetCampaignsByTypeAsync(string campaignType)
        {
            try
            {
                var campaigns = await _dimCampaignAppService.GetCampaignsByTypeAsync(campaignType.ToUpperInvariant());
                return Ok(campaigns);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get campaigns by advertiser
        /// </summary>
        [HttpGet("by-advertiser/{advertiserId}")]
        public async Task<ActionResult<List<DimCampaignDto>>> GetCampaignsByAdvertiserAsync(string advertiserId)
        {
            try
            {
                var campaigns = await _dimCampaignAppService.GetCampaignsByAdvertiserAsync(advertiserId);
                return Ok(campaigns);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }


        /// <summary>
        /// Get campaign types for dropdown
        /// </summary>
        [HttpGet("types")]
        public async Task<ActionResult<List<object>>> GetCampaignTypesAsync()
        {
            try
            {
                var campaigns = await _dimCampaignAppService.GetActiveCampaignsAsync();
                var campaignTypes = campaigns
                    .Where(c => !string.IsNullOrWhiteSpace(c.CampaignType))
                    .GroupBy(c => c.CampaignType)
                    .Select(g => new
                    {
                        text = g.Key,
                        value = g.Key,
                        count = g.Count()
                    })
                    .OrderBy(t => t.text)
                    .ToList();

                return Ok(campaignTypes);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
