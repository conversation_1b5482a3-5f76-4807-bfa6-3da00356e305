using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Repositories
{
    /// <summary>
    /// Repository interface cho Asset
    /// </summary>
    public interface IAssetRepository : IRepository<RawAssetEntity, Guid>
    {
        /// <summary>
        /// Lấy danh sách tài sản với tìm kiếm và phân trang
        /// </summary>
        /// <param name="sorting">Sắp xếp</param>
        /// <param name="maxResultCount">Số lượng tối đa</param>
        /// <param name="skipCount">Số lượng bỏ qua</param>
        /// <param name="filter">Từ khóa tìm kiếm</param>
        /// <param name="assetId">Asset ID</param>
        /// <param name="assetName">Tên tài sản</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="advertiserAccountType">Loại tài khoản quảng cáo</param>
        /// <param name="advertiserRole">Vai trò quảng cáo</param>
        /// <param name="catalogRole">Vai trò danh mục</param>
        /// <param name="adCreationEligible">Có thể tạo quảng cáo</param>
        /// <param name="storeRole">Vai trò cửa hàng</param>
        /// <param name="ownerBcName">Tên BC sở hữu</param>
        /// <param name="isRemoved">Trạng thái xóa</param>
        /// <param name="removedAt">Thời gian xóa</param>
        /// <param name="relationType">Loại quan hệ</param>
        /// <param name="relationStatus">Trạng thái quan hệ</param>
        /// <param name="advertiserStatus">Trạng thái tài khoản quảng cáo</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Danh sách tài sản</returns>
        Task<List<RawAssetEntity>> GetListAsync(
            string? sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            string? filter = null,
            string? assetId = null,
            string? assetName = null,
            AssetType? assetType = null,
            string? bcId = null,
            AdAccountType? advertiserAccountType = null,
            AdvertiserRole? advertiserRole = null,
            CatalogRole? catalogRole = null,
            AdCreationEligible? adCreationEligible = null,
            StoreRole? storeRole = null,
            string? ownerBcName = null,
            bool? isRemoved = null,
            DateTime? removedAt = null,
            RelationType? relationType = null,
            RelationStatus? relationStatus = null,
            AdAccountStatus? advertiserStatus = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy tổng số tài sản với điều kiện tìm kiếm
        /// </summary>
        /// <param name="filter">Từ khóa tìm kiếm</param>
        /// <param name="assetId">Asset ID</param>
        /// <param name="assetName">Tên tài sản</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="advertiserAccountType">Loại tài khoản quảng cáo</param>
        /// <param name="advertiserRole">Vai trò quảng cáo</param>
        /// <param name="catalogRole">Vai trò danh mục</param>
        /// <param name="adCreationEligible">Có thể tạo quảng cáo</param>
        /// <param name="storeRole">Vai trò cửa hàng</param>
        /// <param name="ownerBcName">Tên BC sở hữu</param>
        /// <param name="isRemoved">Trạng thái xóa</param>
        /// <param name="removedAt">Thời gian xóa</param>
        /// <param name="relationType">Loại quan hệ</param>
        /// <param name="relationStatus">Trạng thái quan hệ</param>
        /// <param name="advertiserStatus">Trạng thái tài khoản quảng cáo</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Tổng số tài sản</returns>
        Task<long> GetCountAsync(
            string? filter = null,
            string? assetId = null,
            string? assetName = null,
            AssetType? assetType = null,
            string? bcId = null,
            AdAccountType? advertiserAccountType = null,
            AdvertiserRole? advertiserRole = null,
            CatalogRole? catalogRole = null,
            AdCreationEligible? adCreationEligible = null,
            StoreRole? storeRole = null,
            string? ownerBcName = null,
            bool? isRemoved = null,
            DateTime? removedAt = null,
            RelationType? relationType = null,
            RelationStatus? relationStatus = null,
            AdAccountStatus? advertiserStatus = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách tài sản theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="includeRemoved">Bao gồm cả tài sản đã bị xóa</param>
        /// <returns>Danh sách tài sản</returns>
        Task<List<RawAssetEntity>> GetByBcIdAsync(string bcId, bool includeRemoved = false, AssetType? assetType = null, List<RelationType>? relationTypes = null );

        /// <summary>
        /// Lấy tài sản theo Asset ID
        /// </summary>
        /// <param name="assetId">ID của tài sản</param>
        /// <param name="includeRemoved">Bao gồm cả tài sản đã bị xóa</param>
        /// <returns>Tài sản</returns>
        Task<RawAssetEntity> GetByAssetIdAsync(string assetId, bool includeRemoved = false);

        /// <summary>
        /// Lấy danh sách tài sản theo Asset IDs
        /// </summary>
        /// <param name="assetIds">Danh sách Asset IDs</param>
        /// <param name="includeRemoved">Bao gồm cả tài sản đã bị xóa</param>
        /// <returns>Danh sách tài sản</returns>
        Task<List<RawAssetEntity>> GetManyByAssetIdsAsync(List<string> assetIds, bool includeRemoved = false);

        /// <summary>
        /// Kiểm tra Asset ID đã tồn tại chưa
        /// </summary>
        /// <param name="assetId">Asset ID cần kiểm tra</param>
        /// <param name="excludeId">ID cần loại trừ (cho update)</param>
        /// <param name="includeRemoved">Bao gồm cả tài sản đã bị xóa</param>
        /// <returns>True nếu đã tồn tại</returns>
        Task<bool> IsAssetIdExistsAsync(string assetId, Guid? excludeId = null, bool includeRemoved = false);

        /// <summary>
        /// Đánh dấu tài sản đã bị xóa khỏi Business Center
        /// </summary>
        /// <param name="assetId">ID của tài sản</param>
        /// <param name="removedAt">Thời gian xóa</param>
        /// <returns>True nếu thành công</returns>
        Task<bool> MarkAsRemovedAsync(string assetId, DateTime? removedAt = null);

        /// <summary>
        /// Khôi phục tài sản đã bị xóa
        /// </summary>
        /// <param name="assetId">ID của tài sản</param>
        /// <returns>True nếu thành công</returns>
        Task<bool> RestoreAsync(string assetId);
    }
}