using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using TikTok.FactGmvMaxProducts;
using TikTok.FactGmvMaxProducts.Dtos;
using TikTok.Permissions;
using Volo.Abp.Authorization.Permissions;

namespace TikTok.Web.Pages.FactGmvMaxProduct
{
    public class IndexModel : PageModel
    {
        private readonly IFactGmvMaxProductService _factGmvMaxProductService;
        private readonly ILogger<IndexModel> _logger;
        private readonly IPermissionChecker _permissionChecker;
        
        public GetFactGmvMaxProductDataResponse? Data { get; set; }
        public ProductAlertThresholds ProductAlertThresholds { get; set; }
        
        // ✅ Permission properties for UI visibility
        public bool HasViewSpending { get; set; }
        public bool HasViewMetrics { get; set; }
        public bool HasViewAll { get; set; }
        public bool HasViewAllAdvertisers { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? FromDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? ToDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? CreativeType { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? ProductId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? Type { get; set; }

        // Alternative parameter names for URL query
        [BindProperty(SupportsGet = true, Name = "from")]
        public DateTime? From { get; set; }

        [BindProperty(SupportsGet = true, Name = "to")]
        public DateTime? To { get; set; }

        public IndexModel(IFactGmvMaxProductService factGmvMaxProductService, ILogger<IndexModel> logger, IPermissionChecker permissionChecker)
        {
            _factGmvMaxProductService = factGmvMaxProductService;
            _logger = logger;
            _permissionChecker = permissionChecker;
            ProductAlertThresholds = new ProductAlertThresholds();
        }

        public async Task OnGet()
        {
            try
            {
                // ✅ Check permissions for UI visibility
                HasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                HasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                HasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                HasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // Get date parameters from URL query - support both naming conventions
                var startDateToDay = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                var endDateToDay = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);

                var from = FromDate ?? From ?? DateTime.Now.AddDays(-7); // Default to 7 days to reduce data load
                var to = ToDate ?? To ?? endDateToDay;
                this.From = from;
                this.To = to;

                // Set default type if not specified
                if (string.IsNullOrEmpty(Type))
                {
                    Type = "product"; // Default type for product analysis
                }

                // ✅ No server-side data loading - data will be fetched via API
                _logger.LogInformation("GMV Max Product page loaded with date range {From} to {To} and type {Type}", from, to, Type);
                
                // Initialize empty data - will be populated by JavaScript via API
                Data = new GetFactGmvMaxProductDataResponse();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing GMV Max Product page from {From} to {To}", From, To);
                ModelState.AddModelError("", $"Error initializing GMV Max Product page: {ex.Message}");
                
                // Fallback to empty response  
                Data = new GetFactGmvMaxProductDataResponse();
            }
        }
    }

    public class ProductAlertThresholds
    {
        public string Currency { get; set; } = "USD";
        // Note: Only USD currency is supported
        public decimal RoasCritical { get; set; } = 1.5m; // ROAS < 1.5
        public decimal RoasLow { get; set; } = 2.0m; // ROAS < 2.0
        public decimal RoasGood { get; set; } = 3.0m; // ROAS > 3.0
        public decimal TacosHigh { get; set; } = 30m; // TACOS > 30%
        public decimal TacosMedium { get; set; } = 20m; // TACOS > 20%
        public decimal LowSalesThreshold { get; set; } = 5m; // Less than 5 units sold
        public decimal HighCostPerOrderThreshold { get; set; } = 100m; // Cost per order > $100
    }
}
