﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.AdAccounts;
using TikTok.Enums;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Identity;
using static Volo.Abp.Identity.Settings.IdentitySettingNames;

namespace TikTok.Entities.AdAccounts
{
    [Obsolete]
    public class AdAccountSupporterEntity: AuditedEntity<Guid>
    {
        public string AdvertiserId { get; set; } // From TikTok API
        public Guid SupporterId { get; set; } // ABP User ID
        public DateTime AssignedDate { get; set; }
        public bool IsActive { get; set; }
        public Guid? AssignedByUserId { get; set; } // Who assigned this
        public SupporterRole Role { get; set; } // "Primary", "Secondary", "Viewer"

    }
}
