using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using TikTok.BusinessApplications;
using TikTok.DataSync;
using Volo.Abp;

namespace TikTok.Controllers
{
    /// <summary>
    /// Controller cho Business Applications
    /// </summary>
    [RemoteService(Name = "TikTok")]
    [Area("app")]
    [Route("api/app/business-applications")]
    public class BusinessApplicationsController : TikTokController
    {
        private readonly IBusinessApplicationAppService _businessApplicationAppService;

        public BusinessApplicationsController(IBusinessApplicationAppService businessApplicationAppService)
        {
            _businessApplicationAppService = businessApplicationAppService;
        }

        /// <summary>
        /// Lấy redirect URI cho TikTok authorization
        /// </summary>
        /// <returns>Redirect URI response</returns>
        [HttpGet("get-redirect-uri")]
        public async Task<RedirectUriResponseDto> GetRedirectUriAsync()
        {
            return await _businessApplicationAppService.GetRedirectUriAsync();
        }

        /// <summary>
        /// X<PERSON> lý callback từ TikTok sau khi user authorize
        /// </summary>
        /// <param name="request">Thông tin callback</param>
        /// <returns>Kết quả xử lý callback</returns>
        [HttpPost("process-tiktok-callback")]
        public async Task<TikTokCallbackResponseDto> ProcessTikTokCallbackAsync([FromBody] TikTokCallbackRequestDto request)
        {
            return await _businessApplicationAppService.ProcessTikTokCallbackAsync(request);
        }

        /// <summary>
        /// Tạo job đồng bộ lùi cho Business Center với khoảng thời gian và các loại lệnh cụ thể
        /// </summary>
        /// <param name="request">Thông tin cài đặt đồng bộ lùi</param>
        /// <returns>Kết quả tạo job đồng bộ lùi</returns>
        [HttpPost("create-sync-back-jobs")]
        public async Task<SyncBackResponseDto> CreateSyncBackJobsAsync([FromBody] SyncBackRequestDto request)
        {
            return await _businessApplicationAppService.CreateSyncBackJobsAsync(request);
        }

        /// <summary>
        /// Cập nhật danh sách tài khoản quảng cáo từ tài sản cho Business Application
        /// </summary>
        /// <param name="businessApplicationId">ID của Business Application</param>
        /// <returns>Kết quả cập nhật</returns>
        [HttpPost("update-ad-accounts-from-assets/{businessApplicationId}")]
        public async Task<AdAccountSyncResult> UpdateAdAccountsFromAssetsAsync(Guid businessApplicationId)
        {
            return await _businessApplicationAppService.UpdateAdAccountsFromAssetsAsync(businessApplicationId);
        }
    }
}