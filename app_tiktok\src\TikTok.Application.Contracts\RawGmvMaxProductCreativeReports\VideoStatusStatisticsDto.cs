using System.Collections.Generic;
using TikTok.Enums;

namespace TikTok.RawGmvMaxProductCreativeReports
{
    /// <summary>
    /// DTO cho thống kê video theo trạng thái
    /// </summary>
    public class VideoStatusStatisticsDto
    {
        /// <summary>
        /// Danh sách thống kê theo từng trạng thái
        /// </summary>
        public List<VideoStatusCardDto> StatusCards { get; set; } = new();

        /// <summary>
        /// Thống kê tổng hợp
        /// </summary>
        public VideoStatisticsSummaryDto Summary { get; set; } = new();
    }

    /// <summary>
    /// DTO cho card thống kê video theo trạng thái
    /// </summary>
    public class VideoStatusCardDto
    {
        /// <summary>
        /// Trạng thái (numeric value)
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// Tên trạng thái
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Icon HTML
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// Màu sắc (Bootstrap color class)
        /// </summary>
        public string Color { get; set; } = string.Empty;

        /// <summary>
        /// Số lượng video
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// Phần trăm so với tổng
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// DTO cho thống kê tổng hợp video
    /// </summary>
    public class VideoStatisticsSummaryDto
    {
        /// <summary>
        /// Tổng số video
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// Số video cần xử lý (problematic)
        /// </summary>
        public int ProblematicCount { get; set; }

        /// <summary>
        /// Phần trăm video cần xử lý
        /// </summary>
        public decimal ProblematicPercentage { get; set; }

        /// <summary>
        /// Số video đang hoạt động tốt
        /// </summary>
        public int HealthyCount { get; set; }

        /// <summary>
        /// Phần trăm video đang hoạt động tốt
        /// </summary>
        public decimal HealthyPercentage { get; set; }
    }
}
