$(function () {
    var l = abp.localization.getResource('TikTok');

    // Search parameters
    var searchParams = {
        filter: ''
    };

    var dataTable = $('#AssetsTable').DataTable(
        abp.libs.datatables.normalizeConfiguration({
            serverSide: true,
            paging: true,
            order: [[1, 'asc']],
            searching: false, // Disable default search since we use custom search
            scrollX: true,
            ajax: abp.libs.datatables.createAjax(tikTok.assets.asset.getList, function () {
                console.log('Search params being sent:', searchParams);
                return searchParams;
            }),
            columnDefs: [
                {
                    title: l('Asset:AssetId'),
                    data: 'assetId',
                },
                {
                    title: l('Asset:AssetName'),
                    data: 'assetName',
                },
                {
                    title: l('Asset:AssetType'),
                    data: 'assetType',
                    render: function (data) {
                        return l('Asset:AssetType:' + data);
                    },
                },
                {
                    title: l('Asset:RelationType'),
                    data: 'relationType',
                    render: function (data) {
                        return data ? l('Asset:RelationType:' + data) : '';
                    },
                },
                {
                    title: l('BcId'),
                    data: 'bcId',
                },
                {
                    title: l('Asset:OwnerBcName'),
                    data: 'ownerBcName',
                },
                {
                    title: l('Actions'),
                    rowAction: {
                        items: [
                            {
                                text: l('Edit'),
                                visible:
                                    abp.auth.isGranted('TikTok.Assets.Edit'),
                                action: function (data) {
                                    editModal.open({ id: data.record.id });
                                },
                            },
                            {
                                text: l('Delete'),
                                visible: abp.auth.isGranted(
                                    'TikTok.Assets.Delete'
                                ),
                                confirmMessage: function (data) {
                                    return l(
                                        'AssetDeletionConfirmationMessage',
                                        data.record.assetName
                                    );
                                },
                                action: function (data) {
                                    tikTok.assets.asset
                                        .delete(data.record.id)
                                        .then(function () {
                                            abp.notify.info(
                                                l('SuccessfullyDeleted')
                                            );
                                            dataTable.ajax.reload();
                                        });
                                },
                            },
                        ],
                    },
                },
            ],
        })
    );

    var createModal = new abp.ModalManager(abp.appPath + 'Assets/CreateModal');
    var editModal = new abp.ModalManager(abp.appPath + 'Assets/EditModal');

    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    $('#NewAssetButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });

    // Search functionality
    function saveSearchParams() {
        searchParams.filter = $('#FilterText').val() || '';
        console.log('saveSearchParams called, filter:', searchParams.filter);
    }

    function clearSearchParams() {
        searchParams.filter = '';
        $('#FilterText').val('');
        console.log('clearSearchParams called');
    }

    // Event handlers
    $('#SearchForm').on('submit', function (e) {
        e.preventDefault();
        saveSearchParams();
        dataTable.ajax.reload();
    });

    $('#ClearSearchBtn').on('click', function (e) {
        e.preventDefault();
        clearSearchParams();
        dataTable.ajax.reload();
    });

    // Enter key on search field
    $('#FilterText').on('keypress', function (e) {
        if (e.which === 13) {
            e.preventDefault();
            saveSearchParams();
            dataTable.ajax.reload();
        }
    });



    // Auto-search on input change (with debounce)
    var searchTimeout;
    function debouncedSearch() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function () {
            saveSearchParams();
            dataTable.ajax.reload();
        }, 500);
    }

    // Bind auto-search to filter input
    $('#FilterText').on('input', debouncedSearch);
});
