/**
 * 🎯 Rankings Section - Đơn giản
 * Tự call API + tự render
 */

class RankingsSection {
    constructor() {
        this.container = '#rankings-container';
        this.initialized = false;
    }

    /**
     * 🎯 Khởi tạo section
     */
    async init() {
        if (this.initialized) {
            return;
        }

        try {
            await this.loadData();
            this.initialized = true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 🎯 Load data từ API
     */
    async loadData() {
        try {
            // Show loading
            this.showLoading();

            // Get currency and call API
            const currency = localStorage.getItem('tiktok_currency') || 'USD';
            const response = await fetch(
                `/api/fact-gmv-max-product/rankings?currency=${currency}`
            );
            if (!response.ok) {
                throw new Error(
                    `HTTP ${response.status}: ${response.statusText}`
                );
            }

            const data = await response.json();

            // Render data
            await this.render(data);
        } catch (error) {
            this.showError(error);
            throw error;
        }
    }

    /**
     * 🎯 Render rankings data
     */
    async render(data) {
        // Get currency from localStorage or default to USD
        const currency =
            (typeof localStorage !== 'undefined'
                ? localStorage.getItem('tiktok_currency')
                : null) || 'USD';

        // Create store revenue rankings
        const revenueContainer = document.getElementById(
            'storeRankingsContainer'
        );
        if (revenueContainer) {
            this.createStoreRankings(
                revenueContainer,
                data,
                'revenue',
                currency
            );
        }

        // Create store cost rankings
        const costContainer = document.getElementById(
            'storeCostRankingsContainer'
        );
        if (costContainer) {
            this.createStoreRankings(costContainer, data, 'cost', currency);
        }
    }

    /**
     * 🎯 Create store rankings with multiple weeks
     * @param {HTMLElement} container - Container element
     * @param {Object} data - Dashboard data
     * @param {string} type - Type of ranking (revenue/cost)
     * @param {string} currency - Current currency
     */
    createStoreRankings(container, data, type, currency) {
        const today = new Date();
        const currentWeekStart = new Date(today);
        const dayIndex = (today.getDay() + 6) % 7;
        currentWeekStart.setDate(today.getDate() - dayIndex);

        const weeks = [
            {
                label: this.getWeekLabel(currentWeekStart),
                data:
                    type === 'cost'
                        ? data.currentWeekStoreCostRanking || []
                        : data.currentWeekStoreRanking || [],
            },
            {
                label: this.getWeekLabel(
                    new Date(
                        currentWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000
                    )
                ),
                data:
                    type === 'cost'
                        ? data.oneWeekAgoStoreCostRanking || []
                        : data.oneWeekAgoStoreRanking || [],
            },
            {
                label: this.getWeekLabel(
                    new Date(
                        currentWeekStart.getTime() - 14 * 24 * 60 * 60 * 1000
                    )
                ),
                data:
                    type === 'cost'
                        ? data.twoWeeksAgoStoreCostRanking || []
                        : data.twoWeeksAgoStoreRanking || [],
            },
        ];

        const rankingsHtml = weeks
            .map((week) => {
                if (!week.data || week.data.length === 0) {
                    return `
                    <div class="ranking-card">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <h6 class="card-title text-center mb-3">
                                    <i class="fas fa-calendar-week text-primary"></i> ${week.label}
                                </h6>
                                <p class="text-muted">Không có dữ liệu</p>
                            </div>
                        </div>
                    </div>
                `;
                }

                const top3 = week.data.slice(0, 3);
                const rest = week.data.slice(3);
                const medals = ['🥇', '🥈', '🥉'];

                return `
                <div class="ranking-card">
                    <div class="card h-100">
                        <div class="card-body">
                            <h6 class="card-title text-center mb-3">
                                <i class="fas fa-calendar-week text-primary"></i> ${
                                    week.label
                                }
                            </h6>
                            <div class="top3-container">
                                <div class="rank-1">
                                    <div class="fs-1">${medals[0]}</div>
                                    <div class="fw-bold">${
                                        top3[0]?.storeName || 'N/A'
                                    }</div>
                                    <div class="h5 text-primary">${this.formatCurrency(
                                        top3[0]?.totalRevenue ||
                                            top3[0]?.totalCost ||
                                            0,
                                        currency
                                    )}</div>
                                </div>
                                <div class="ranks-2-3">
                                    <div class="rank-item">
                                        <div class="fs-1">${medals[1]}</div>
                                        <div class="fw-bold">${
                                            top3[1]?.storeName || 'N/A'
                                        }</div>
                                        <div class="h5 text-primary">${this.formatCurrency(
                                            top3[1]?.totalRevenue ||
                                                top3[1]?.totalCost ||
                                                0,
                                            currency
                                        )}</div>
                                    </div>
                                    <div class="rank-item">
                                        <div class="fs-1">${medals[2]}</div>
                                        <div class="fw-bold">${
                                            top3[2]?.storeName || 'N/A'
                                        }</div>
                                        <div class="h5 text-primary">${this.formatCurrency(
                                            top3[2]?.totalRevenue ||
                                                top3[2]?.totalCost ||
                                                0,
                                            currency
                                        )}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <tbody>
                                        ${rest
                                            .map(
                                                (item, index) => `
                                            <tr>
                                                <td class="text-center">${
                                                    index + 4
                                                }</td>
                                                <td>${item.storeName}</td>
                                                <td class="text-end">${this.formatCurrency(
                                                    item.totalRevenue ||
                                                        item.totalCost ||
                                                        0,
                                                    currency
                                                )}</td>
                                            </tr>
                                        `
                                            )
                                            .join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            })
            .join('');

        container.innerHTML = rankingsHtml;
    }

    /**
     * 🎯 Get week label for rankings
     * @param {Date} weekStartDate - Week start date
     * @returns {string} Week label
     */
    getWeekLabel(weekStartDate) {
        const weekEndDate = new Date(weekStartDate);
        weekEndDate.setDate(weekStartDate.getDate() + 6);

        const monthNames = [
            'tháng 1',
            'tháng 2',
            'tháng 3',
            'tháng 4',
            'tháng 5',
            'tháng 6',
            'tháng 7',
            'tháng 8',
            'tháng 9',
            'tháng 10',
            'tháng 11',
            'tháng 12',
        ];

        const month = monthNames[weekEndDate.getMonth()];
        const monthStart = new Date(
            weekEndDate.getFullYear(),
            weekEndDate.getMonth(),
            1
        );
        const monthStartDayIndex = (monthStart.getDay() + 6) % 7;
        const firstMonday = new Date(monthStart);
        firstMonday.setDate(1 + ((7 - monthStartDayIndex) % 7));

        let weekNumber = 1;
        if (weekEndDate >= firstMonday) {
            const diffDays = Math.floor(
                (weekEndDate - firstMonday) / (24 * 3600 * 1000)
            );
            weekNumber = Math.floor(diffDays / 7) + 1;
        }

        const startDateStr = weekStartDate.toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
        });
        const endDateStr = weekEndDate.toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
        });

        return `Tuần ${weekNumber} ${month} (${startDateStr} -> ${endDateStr})`;
    }

    /**
     * 🎯 Show loading indicator
     */
    showLoading() {
        // Show loading for both ranking containers
        const costContainer = document.getElementById(
            'storeCostRankingsContainer'
        );
        const revenueContainer = document.getElementById(
            'storeRankingsContainer'
        );

        const loadingHtml = `
            <div class="text-center">
                <div class="spinner-border text-danger" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Đang tải xếp hạng...</p>
            </div>
        `;

        if (costContainer) {
            costContainer.innerHTML = loadingHtml;
        }
        if (revenueContainer) {
            revenueContainer.innerHTML = loadingHtml;
        }
    }

    /**
     * 🎯 Show error message
     */
    showError(error) {
        const revenueContainer = document.getElementById(
            'storeRankingsContainer'
        );
        const costContainer = document.getElementById(
            'storeCostRankingsContainer'
        );

        const errorHtml = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Lỗi tải Rankings</h6>
                <p class="mb-0">${error.message}</p>
            </div>
        `;

        if (revenueContainer) {
            revenueContainer.innerHTML = errorHtml;
        }
        if (costContainer) {
            costContainer.innerHTML = errorHtml;
        }
    }

    /**
     * 🎯 Format currency
     */
    formatCurrency(amount, currency = 'USD') {
        if (
            window.sharedCurrencyManager &&
            window.sharedCurrencyManager.formatCurrency
        ) {
            return window.sharedCurrencyManager.formatCurrency(
                amount || 0,
                currency
            );
        }
        if (!amount) return currency === 'VND' ? '₫0' : '$0';
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    }

    /**
     * 🎯 Format number
     */
    formatNumber(number) {
        return new Intl.NumberFormat('vi-VN').format(number);
    }

    /**
     * 🎯 Refresh data
     */
    async refresh() {
        await this.loadData();
    }
}

// Export for global use
window.RankingsSection = RankingsSection;
