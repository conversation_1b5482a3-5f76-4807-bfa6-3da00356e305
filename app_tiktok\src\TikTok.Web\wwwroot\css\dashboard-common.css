.ranking-card .card-body {
    padding: 1rem 1rem 0.75rem 1rem;
}

.ranking-card h6.card-title {
    font-weight: 600;
}

.ranking-card .fs-1 {
    line-height: 1;
}

.ranking-card .table tbody tr td {
    padding-top: 0.35rem;
    padding-bottom: 0.35rem;
}

.ranking-card .h5.text-primary {
    margin-bottom: 0.25rem;
}

.ranking-card .fw-bold {
    color: #212529;
}

.ranking-card .small.text-muted {
    color: #6c757d !important;
}

/* ✅ Top 3 rankings layout - ranks 2&3 on same row */
.ranking-card .top3-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1rem;
}

.ranking-card .rank-1 {
    margin-bottom: 0.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.ranking-card .ranks-2-3 {
    display: flex;
    justify-content: space-around;
    width: 100%;
    gap: 1rem;
}

.ranking-card .rank-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.ranking-card .rank-item .fs-1 {
    font-size: 2rem !important;
    margin-bottom: 0.25rem;
}

.ranking-card .rank-item .fw-bold {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.ranking-card .rank-item .h5 {
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* ✅ Container for 3 ranking cards in a row */
#storeRankingsContainer,
#storeCostRankingsContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: space-between;
}

/* ✅ Border around each ranking card */
.ranking-card {
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    flex: 1;
    min-width: 300px;
    max-width: calc(33.333% - 1rem);
}

.ranking-card:hover {
    border-color: #adb5bd;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* ✅ Responsive design for ranking cards */
@media (max-width: 1200px) {
    .ranking-card {
        max-width: calc(50% - 0.5rem);
        min-width: 280px;
    }
}

@media (max-width: 768px) {
    #storeRankingsContainer,
    #storeCostRankingsContainer {
        flex-direction: column;
    }

    .ranking-card {
        max-width: 100%;
        min-width: auto;
    }
}

/* ✅ Remove bold from ranks 4+ */
.ranking-card .table tbody tr td {
    font-weight: normal !important;
}
/* GMV Max Dashboard Common Styles - Shared across GMV Max Campaign and Product screens */
/* Based on fact-gmv-max-campaign.css with shared functionality */

@import url('./pivot-common.css');

/* Enhanced Filter Styles - Native HTML Controls Only */

/* Form Controls - Keep only essential non-Syncfusion styling */
.form-label.small {
    font-size: 0.875rem !important;
    margin-bottom: 0.25rem !important;
}

/* Shared Summary Card Colors */
.summary-card.gmv-revenue {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.summary-card.gmv-cost {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, #ffffff 100%);
}

.summary-card.roas-high {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.summary-card.roas-medium {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
}

.summary-card.roas-low {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, #ffffff 100%);
}

.summary-card.acos-low {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.summary-card.acos-medium {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
}

.summary-card.acos-high {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, #ffffff 100%);
}

.business-actions {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.metric-indicator {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    margin-left: 8px;
}

.metric-indicator.critical {
    background: #ffcdd2;
    color: #c62828;
}

.metric-indicator.warning {
    background: #ffe0b2;
    color: #ef6c00;
}

.metric-indicator.good {
    background: #c8e6c9;
    color: #2e7d32;
}

/* Dashboard Header: use shared styles from pivot-common */

/* Summary Cards: use shared styles from pivot-common */

/* Controls & Quick Filters: use shared styles from pivot-common */

/* Pivot Container: use shared styles from pivot-common */

.pivot-container {
    margin-bottom: 2rem;
}

.pivot-container .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.pivot-container .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Cards: use shared styles from pivot-common */

/* Shared Performance Indicators */
.performance-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    margin-left: 8px;
}

.performance-indicator.excellent {
    background: #e8f5e8;
    color: #2e7d32;
}

.performance-indicator.good {
    background: #f1f8e9;
    color: #388e3c;
}

.performance-indicator.fair {
    background: #fff8e1;
    color: #ef6c00;
}

.performance-indicator.poor {
    background: #ffebee;
    color: #c62828;
}

/* ROAS and ACOS specific styling */
.roas-value {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
}

.roas-value.high {
    background: #e8f5e8;
    color: #2e7d32;
}

.roas-value.medium {
    background: #fff8e1;
    color: #ef6c00;
}

.roas-value.low {
    background: #ffebee;
    color: #c62828;
}

.acos-value {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
}

.acos-value.low {
    background: #e8f5e8;
    color: #2e7d32;
}

.acos-value.medium {
    background: #fff8e1;
    color: #ef6c00;
}

.acos-value.high {
    background: #ffebee;
    color: #c62828;
}

/* Content Type badges */
.content-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.content-type-badge.video {
    background: #e3f2fd;
    color: #1565c0;
}

.content-type-badge.product-card {
    background: #f3e5f5;
    color: #7b1fa2;
}

.content-type-badge.other {
    background: #f5f5f5;
    color: #616161;
}

/* Enhanced table styling */
.table-enhanced {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table-enhanced thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--primary-color);
}

.table-enhanced tbody tr:hover {
    background-color: #f8f9fa;
}

/* Metrics grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.metric-card {
    background: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary-color);
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-card .metric-title {
    font-size: 0.9rem;
    color: var(--dark-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.metric-card .metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.metric-card .metric-change {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

/* Responsive design improvements */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1.5rem;
    }

    .dashboard-header h1 {
        font-size: 2rem;
    }

    .summary-cards .summary-card__col {
        width: 50%;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .summary-cards .summary-card__col {
        width: 100%;
    }

    .dashboard-header {
        padding: 1rem;
    }

    .dashboard-header h1 {
        font-size: 1.75rem;
    }
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 10px;
    min-width: 300px;
}

.toast-header {
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 0.5rem 1rem;
    font-weight: 600;
}

.toast-body {
    padding: 1rem;
    color: var(--dark-color);
}

/* Enhanced button styles */
.btn-enhanced {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-enhanced.btn-primary {
    background: linear-gradient(
        135deg,
        var(--primary-color) 0%,
        var(--secondary-color) 100%
    );
    border: none;
}

.btn-enhanced.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
    border: none;
}

.btn-enhanced.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f1c40f 100%);
    border: none;
}

.btn-enhanced.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
    border: none;
}

/* Value Selection Modal Styles */
#valueSelectionModal > .modal-dialog {
    max-width: min(1500px, 80vw);
}

/* Dashboard Summary Section Styles */
.dashboard-summary-section {
    margin-bottom: 2rem;
}

.dashboard-summary-section .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    padding: 0.25rem;
}

.dashboard-summary-section .card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Monthly Cards Styles */
.summary-card {
    padding: 0.25rem;
}
.monthly-card {
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-left: 4px solid #4e73df;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.monthly-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.monthly-card.current-month {
    border-left-color: #1cc88a;
    background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.monthly-card.last-month {
    border-left-color: #f6c23e;
    background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
}

/* Weekly Cards Styles */
.weekly-card {
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-left: 4px solid #36b9cc;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.weekly-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Weekly section styles */
.weekly-section {
    margin-top: 1rem;
}

.weekly-section h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Current month weekly cards */
.weekly-card.current-month-week {
    border-left-color: #1cc88a;
    background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.weekly-card.current-month-week h4 {
    color: #1cc88a;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Last month weekly cards */
.weekly-card.last-month-week {
    border-left-color: #f6c23e;
    background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
}

.weekly-card.last-month-week h4 {
    color: #f6c23e;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Compact weekly card values */
.weekly-card .value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #5a5c69;
    margin-bottom: 0.25rem;
}

.weekly-card .change {
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 0;
}

/* Summary Card Value Styles */
.summary-card .value {
    font-size: 2rem;
    font-weight: 700;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.summary-card .change {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0;
}

.summary-card .change.positive {
    color: #1cc88a;
}

.summary-card .change.negative {
    color: #e74a3b;
}

.summary-card .change.neutral {
    color: #858796;
}

.summary-card h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #5a5c69;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

/* Charts Section Styles */
.charts-section .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.charts-section .card-header[data-bs-toggle='collapse'] {
    transition: all 0.2s ease;
    cursor: pointer;
}

.charts-section .card-header[data-bs-toggle='collapse']:hover {
    background-color: rgba(0, 0, 0, 0.1) !important;
}

.charts-section .card-header i.fa-chevron-down {
    transition: transform 0.2s ease;
}

.charts-section .card-header[aria-expanded='true'] i.fa-chevron-down {
    transform: rotate(180deg);
}

/* Chart Container Styles */
#weeklyCurrentChart,
#weeklyLastChart,
#monthlyChart {
    border-radius: 0.375rem;
    background: white;
    border: 1px solid #e3e6f0;
}

/* Chart Title Styles */
.charts-section h6 {
    color: #5a5c69;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e3e6f0;
}

/* Responsive Design for Dashboard */
@media (max-width: 768px) {
    .dashboard-summary-section .col-md-6 {
        margin-bottom: 1rem;
    }

    .dashboard-summary-section .col-md-3 {
        margin-bottom: 1rem;
    }

    .summary-card .value {
        font-size: 1.5rem;
    }

    .summary-card h3 {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .charts-section .col-md-6 {
        margin-bottom: 2rem;
    }

    #weeklyChart,
    #monthlyChart {
        height: 250px !important;
    }
}

/* Pivot Table Specific Styles */
.pivot-table-container {
    padding: 0;
}

.pivot-table-container .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.pivot-table-container .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.pivot-table-container .card-body {
    padding: 0.5rem;
}

/* Footer Info Styles */
.footer-info {
    margin-top: 2rem;
}

.footer-info .card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.footer-info .card-body {
    padding: 1.5rem;
}

.footer-info small {
    color: #6c757d;
    line-height: 1.5;
}

.footer-info strong {
    color: #495057;
}

/* Alert Badge Styles */
#alert-count-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Data Status Indicator */
#data-status-indicator {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

/* Auto Refresh Toggle */
#auto-refresh-toggle {
    font-size: 0.875rem;
}

/* Help Button */
#help-toggle {
    font-size: 0.875rem;
}

/* ✅ Shared Dashboard Summary Cards */
.dashboard-summary-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.dashboard-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.dashboard-summary-card.revenue::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.dashboard-summary-card.roi::before {
    background: linear-gradient(90deg, #007bff, #6610f2);
}

.dashboard-summary-card.campaigns::before {
    background: linear-gradient(90deg, #fd7e14, #e83e8c);
}

.dashboard-summary-card.orders::before {
    background: linear-gradient(90deg, #6f42c1, #e83e8c);
}

.dashboard-summary-card.spent-estimate::before {
    background: linear-gradient(90deg, #dc3545, #fd7e14);
}

.dashboard-summary-card.total-spent::before {
    background: linear-gradient(90deg, #6c757d, #495057);
}

.card-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0;
}

/* ✅ Shared Summary Cards */
.summary-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.monthly-card {
    border-left: 4px solid #007bff;
}

.monthly-card.current-month {
    border-left-color: #28a745;
}

.monthly-card.last-month {
    border-left-color: #6c757d;
}

.weekly-card {
    border-left: 4px solid #ffc107;
}

.weekly-card.current-month-week {
    border-left-color: #28a745;
}

.weekly-card.last-month-week {
    border-left-color: #6c757d;
}

.summary-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.summary-card .value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.summary-card .change {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0;
}

.summary-card .change.positive {
    color: #28a745;
}

.summary-card .change.negative {
    color: #dc3545;
}

.summary-card .change.neutral {
    color: #6c757d;
}

/* ✅ Shared Chart Cards */
.chart-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.chart-card .card-header {
    background: transparent;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.chart-card .card-header h6 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0;
}

/* ✅ Shared Weekly Section */
.weekly-section {
    margin-top: 1rem;
}

.weekly-section h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

/* ✅ Shared Ranking Section Header Styles */
.ranking-section-header {
    cursor: pointer;
    transition: all 0.2s ease;
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    color: #fff;
    border: none;
}

.ranking-section-header:hover {
    opacity: 0.9;
}

.ranking-section-header i.fa-chevron-down {
    transition: transform 0.2s ease;
    color: #fff;
}

.ranking-section-header[aria-expanded='true'] i.fa-chevron-down {
    transform: rotate(180deg);
}

.ranking-section-header h5 {
    color: #fff !important;
}

.ranking-section-header h5 i {
    color: #ffc107;
    margin-right: 0.5rem;
}

/* ✅ Shared Pivot Table Header Style */
.pivot-table-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* ✅ Shared Dashboard Summary Header Style */
.dashboard-summary-header {
    background: linear-gradient(135deg, #4e73df 0%, #2c5aa0 100%);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dashboard-summary-header:hover {
    opacity: 0.9;
}

.dashboard-summary-header i.fa-chevron-down {
    transition: transform 0.2s ease;
}

.dashboard-summary-header[aria-expanded='true'] i.fa-chevron-down {
    transform: rotate(180deg);
}

/* ✅ Shared Charts Header Style */
.charts-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.charts-header:hover {
    opacity: 0.9;
}

.charts-header i.fa-chevron-down {
    transition: transform 0.2s ease;
}

.charts-header[aria-expanded='true'] i.fa-chevron-down {
    transform: rotate(180deg);
}

/* ✅ Dashboard Summary Section Styles */
.dashboard-summary-section {
    margin-bottom: 2rem;
}

.dashboard-summary-section .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.dashboard-summary-section .card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Dashboard Summary Cards Grid */
#dashboard-summary-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.dashboard-summary-card {
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-left: 4px solid #4e73df;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    text-align: center;
}

.dashboard-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dashboard-summary-card .card-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.dashboard-summary-card .card-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #5a5c69;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.dashboard-summary-card .card-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #5a5c69;
    margin-bottom: 0.25rem;
}

.dashboard-summary-card .card-subtitle {
    font-size: 0.75rem;
    color: #858796;
    margin-bottom: 0;
}

/* Specific card colors */
.dashboard-summary-card.revenue {
    border-left-color: #1cc88a;
}

.dashboard-summary-card.revenue .card-icon {
    color: #1cc88a;
}

.dashboard-summary-card.roi {
    border-left-color: #36b9cc;
}

.dashboard-summary-card.roi .card-icon {
    color: #36b9cc;
}

.dashboard-summary-card.campaigns {
    border-left-color: #f6c23e;
}

.dashboard-summary-card.campaigns .card-icon {
    color: #f6c23e;
}

.dashboard-summary-card.orders {
    border-left-color: #e74a3b;
}

.dashboard-summary-card.orders .card-icon {
    color: #e74a3b;
}

.dashboard-summary-card.spent-estimate {
    border-left-color: #6f42c1;
}

.dashboard-summary-card.spent-estimate .card-icon {
    color: #6f42c1;
}

.dashboard-summary-card.total-spent {
    border-left-color: #fd7e14;
}

.dashboard-summary-card.total-spent .card-icon {
    color: #fd7e14;
}

/* Responsive design for dashboard summary */
@media (max-width: 768px) {
    #dashboard-summary-cards-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .dashboard-summary-card {
        padding: 1rem;
    }

    .dashboard-summary-card .card-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    #dashboard-summary-cards-container {
        grid-template-columns: 1fr;
    }

    .dashboard-summary-card .card-value {
        font-size: 1.25rem;
    }
}

/* ✅ Ranking Tabs Styling */
.ranking-section-header .nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 0.5rem 0.5rem 0 0.5rem;
}

.ranking-section-header .nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border-radius: 0.375rem 0.375rem 0 0;
    margin-right: 0.25rem;
}

.ranking-section-header .nav-tabs .nav-link:hover {
    border-bottom-color: #ffc107;
    color: #495057;
    background-color: rgba(255, 193, 7, 0.1);
}

.ranking-section-header .nav-tabs .nav-link.active {
    color: #fff;
    border-bottom-color: #198754;
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    box-shadow: 0 2px 4px rgba(25, 135, 84, 0.3);
}

.ranking-section-header .nav-tabs .nav-link.active i {
    color: #fff;
}

.ranking-section-header .nav-tabs .nav-link i {
    margin-right: 0.5rem;
    transition: color 0.3s ease;
}

.ranking-section-header .tab-content {
    padding-top: 0;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-top: none;
}

.ranking-section-header .tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

/* ✅ Ranking Section Header Color */
.ranking-section-header {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    color: #fff;
    border: none;
}

.ranking-section-header h5 {
    color: #fff !important;
}

.ranking-section-header h5 i {
    color: #ffc107;
    margin-right: 0.5rem;
}

.ranking-section-header .fas.fa-chevron-down {
    color: #fff;
    transition: transform 0.3s ease;
}

.ranking-section-header[aria-expanded='true'] .fas.fa-chevron-down {
    transform: rotate(180deg);
}

/* ✅ Chart Tabs Styling */
.charts-header .nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 0.5rem 0.5rem 0 0.5rem;
}

.charts-header .nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border-radius: 0.375rem 0.375rem 0 0;
    margin-right: 0.25rem;
}

.charts-header .nav-tabs .nav-link:hover {
    border-bottom-color: #ffc107;
    color: #495057;
    background-color: rgba(255, 193, 7, 0.1);
}

.charts-header .nav-tabs .nav-link.active {
    color: #fff;
    border-bottom-color: #198754;
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    box-shadow: 0 2px 4px rgba(25, 135, 84, 0.3);
}

.charts-header .nav-tabs .nav-link.active i {
    color: #fff;
}

.charts-header .nav-tabs .nav-link i {
    margin-right: 0.5rem;
    transition: color 0.3s ease;
}

.charts-header .tab-content {
    padding-top: 0;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-top: none;
}

.charts-header .tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

/* ✅ Chart Cards Styling */
.charts-header .card {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.charts-header .card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.charts-header .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0;
}

.charts-header .card-header h6 {
    color: #495057;
    font-weight: 600;
}

.charts-header .card-header i {
    margin-right: 0.5rem;
}

/* ✅ Shared Responsive Design */
@media (max-width: 768px) {
    .dashboard-header h1 {
        font-size: 2rem;
    }

    .dashboard-header p {
        font-size: 1rem;
    }

    .card-value {
        font-size: 1.5rem;
    }

    .ranks-2-3 {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}

/* ✅ Shared Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-summary-card,
.summary-card,
.chart-card,
.ranking-card {
    animation: fadeInUp 0.6s ease-out;
}
