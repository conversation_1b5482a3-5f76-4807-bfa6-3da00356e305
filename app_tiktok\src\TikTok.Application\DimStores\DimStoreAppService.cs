using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Authorization.Permissions;
using TikTok.Entities.Dim;
using TikTok.Permissions;

namespace TikTok.DimStores
{
    /// <summary>
    /// Application service for DimStore operations
    /// </summary>
    public class DimStoreAppService : ApplicationService, IDimStoreAppService
    {
        private readonly IRepository<DimStoreEntity, Guid> _dimStoreRepository;
        private readonly IPermissionChecker _permissionChecker;

        public DimStoreAppService(
            IRepository<DimStoreEntity, Guid> dimStoreRepository,
            IPermissionChecker permissionChecker)
        {
            _dimStoreRepository = dimStoreRepository;
            _permissionChecker = permissionChecker;
        }

        /// <summary>
        /// Get all active stores for dropdown/multiselect
        /// </summary>
        public async Task<List<DimStoreDto>> GetActiveStoresAsync()
        {
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new Volo.Abp.Authorization.AbpAuthorizationException("Bạn không có quyền truy cập danh sách shop");
            }

            var query = await _dimStoreRepository.GetQueryableAsync();
            
            var activeStores = query
                .Where(s => s.Status == "ACTIVE" && s.EffectiveEndDate == null)
                .OrderBy(s => s.StoreName)
                .Select(s => new DimStoreDto
                {
                    Id = s.Id,
                    StoreId = s.StoreId,
                    StoreName = s.StoreName,
                    StoreDescription = s.Description,
                    StoreType = s.StoreType,
                    Status = s.Status,
                    Country = s.Country,
                    Currency = null, // Not in entity, can be added if needed
                    Timezone = null, // Not in entity, can be added if needed
                    CreatedDate = s.CreatedAt,
                    LastModifiedDate = s.UpdatedAt
                })
                .ToList();

            return activeStores;
        }

        /// <summary>
        /// Get stores by country
        /// </summary>
        public async Task<List<DimStoreDto>> GetStoresByCountryAsync(string country)
        {
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new Volo.Abp.Authorization.AbpAuthorizationException("Bạn không có quyền truy cập danh sách shop theo quốc gia");
            }

            if (string.IsNullOrWhiteSpace(country))
            {
                throw new ArgumentException("Country code is required");
            }

            var query = await _dimStoreRepository.GetQueryableAsync();
            
            var stores = query
                .Where(s => s.Status == "ACTIVE" && 
                           s.EffectiveEndDate == null && 
                           s.Country == country)
                .OrderBy(s => s.StoreName)
                .Select(s => new DimStoreDto
                {
                    Id = s.Id,
                    StoreId = s.StoreId,
                    StoreName = s.StoreName,
                    StoreDescription = s.Description,
                    StoreType = s.StoreType,
                    Status = s.Status,
                    Country = s.Country,
                    Currency = null,
                    Timezone = null,
                    CreatedDate = s.CreatedAt,
                    LastModifiedDate = s.UpdatedAt
                })
                .ToList();

            return stores;
        }
    }
}
