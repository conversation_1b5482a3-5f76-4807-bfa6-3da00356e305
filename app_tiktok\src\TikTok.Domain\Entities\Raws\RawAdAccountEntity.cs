using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using TikTok.AdAccounts;
using TikTok.Entities.AdAccounts;
using TikTok.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Entity đại diện cho tài khoản quảng cáo (Ad Account)
    /// </summary>
    public class RawAdAccountEntity : AuditedEntity<Guid>
    {
        /// <summary>
        /// ID duy nhất của tài khoản quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID của Business Center sở hữu tài khoản
        /// </summary>
        [Required]
        [StringLength(100)]
        public string OwnerBcId { get; set; }

        /// <summary>
        /// Trạng thái tài khoản
        /// </summary>
        [Required]
        public AdAccountStatus Status { get; set; }

        /// <summary>
        /// Vai trò tài khoản
        /// </summary>
        [Required]
        public AdAccountRole Role { get; set; }

        /// <summary>
        /// Lý do bị từ chối (nếu có)
        /// </summary>
        [StringLength(500)]
        public string? RejectionReason { get; set; }

        /// <summary>
        /// Tên tài khoản quảng cáo
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        /// <summary>
        /// Múi giờ gốc với GMT offset hoặc định dạng "Region/City" (để tham chiếu khi convert từ UTC)
        /// </summary>
        [StringLength(50)]
        public string? Timezone { get; set; }

        /// <summary>
        /// Tên múi giờ hiển thị theo định dạng "Region/City"
        /// </summary>
        [StringLength(100)]
        public string? DisplayTimezone { get; set; }

        /// <summary>
        /// Tên công ty của tài khoản
        /// </summary>
        [StringLength(255)]
        public string? Company { get; set; }

        /// <summary>
        /// Có thể chỉnh sửa tên công ty qua API hay không
        /// </summary>
        public bool CompanyNameEditable { get; set; }

        /// <summary>
        /// Mã danh mục ngành nghề
        /// </summary>
        [StringLength(50)]
        public string? Industry { get; set; }

        /// <summary>
        /// Địa chỉ tài khoản
        /// </summary>
        [StringLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// Mã quốc gia đăng ký (ví dụ: US, CN)
        /// </summary>
        [StringLength(50)]
        public string? Country { get; set; }

        /// <summary>
        /// Loại tài khoản
        /// </summary>
        [Required]
        public AdAccountType AdvertiserAccountType { get; set; }

        /// <summary>
        /// Loại tiền tệ theo mã ISO 4217 (ví dụ: USD, EUR)
        /// </summary>
        [StringLength(10)]
        public string? Currency { get; set; }

        /// <summary>
        /// Tên người liên hệ (đã che)
        /// </summary>
        [StringLength(255)]
        public string? Contacter { get; set; }

        /// <summary>
        /// Email liên hệ (đã che)
        /// </summary>
        [StringLength(255)]
        [EmailAddress]
        public string? Email { get; set; }

        /// <summary>
        /// Số điện thoại di động (đã che)
        /// </summary>
        [StringLength(20)]
        public string? CellphoneNumber { get; set; }

        /// <summary>
        /// Số điện thoại cố định (đã che)
        /// </summary>
        [StringLength(20)]
        public string? TelephoneNumber { get; set; }

        /// <summary>
        /// Mã ngôn ngữ sử dụng (ví dụ: en, zh)
        /// </summary>
        [StringLength(10)]
        public string? Language { get; set; }

        /// <summary>
        /// Số giấy phép kinh doanh
        /// </summary>
        [StringLength(100)]
        public string? LicenseNo { get; set; }

        /// <summary>
        /// URL xem trước giấy phép (có hiệu lực 1 giờ)
        /// </summary>
        [StringLength(500)]
        public string? LicenseUrl { get; set; }

        /// <summary>
        /// Mô tả thương hiệu/công ty
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Số dư khả dụng của tài khoản
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// Thời gian tạo tài khoản (lưu trữ dưới dạng UTC, được convert từ Unix timestamp)
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Cờ đánh dấu đã bị xóa khỏi Business Center
        /// </summary>
        public bool IsRemoved { get; set; }

        /// <summary>
        /// Thời gian xóa khỏi Business Center (UTC)
        /// </summary>
        public DateTime? RemovedAt { get; set; }
        /// <summary>
        /// Quan hệ với BC
        /// </summary>
        public RelationType? RelationType { get; set; }

        #region DATA_FILTER_IMPLEMENTATION

        /// <summary>
        /// Data Filter Implementation - Multiple Supporters
        /// </summary>
        [NotMapped]
        public List<Guid> SupporterIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Data Filter is enabled when there are supporter IDs
        /// </summary>
        [NotMapped]
        public bool IsEnabled => SupporterIds?.Any() == true;

        /// <summary>
        /// Navigation to supporters (many-to-many relationship)
        /// </summary>
        public virtual ICollection<AdAccountSupporterEntity> Supporters { get; set; } = new List<AdAccountSupporterEntity>();
        #endregion

        /// <summary>
        /// Constructor mặc định
        /// </summary>
        public RawAdAccountEntity()
        {
        }

        /// <summary>
        /// Constructor với ID
        /// </summary>
        /// <param name="id">ID của entity</param>
        public RawAdAccountEntity(Guid id) : base(id)
        {
        }

        // Kiểm tra xem có thay đổi không
        public bool HasIsChanged(RawAdAccountEntity other)
        {
            return this.AdvertiserId != other.AdvertiserId ||
                this.OwnerBcId != other.OwnerBcId ||
                this.Status != other.Status ||
                this.Role != other.Role ||
                this.RejectionReason != other.RejectionReason ||
                this.Name != other.Name ||
                this.Timezone != other.Timezone ||
                this.DisplayTimezone != other.DisplayTimezone ||
                this.Company != other.Company ||
                this.CompanyNameEditable != other.CompanyNameEditable ||
                this.Industry != other.Industry ||
                this.Address != other.Address ||
                this.Country != other.Country ||
                this.AdvertiserAccountType != other.AdvertiserAccountType ||
                this.Currency != other.Currency ||
                this.Contacter != other.Contacter ||
                this.Email != other.Email ||
                this.CellphoneNumber != other.CellphoneNumber ||
                this.TelephoneNumber != other.TelephoneNumber ||
                this.Language != other.Language ||
                this.LicenseNo != other.LicenseNo ||
                this.LicenseUrl != other.LicenseUrl ||
                this.Description != other.Description ||
                this.Balance != other.Balance ||
                this.CreateTime != other.CreateTime;
        }

        /// <summary>
        /// Cập nhật entity từ một entity khác
        /// </summary>
        /// <param name="source">Entity nguồn để cập nhật dữ liệu</param>
        public void UpdateFrom(RawAdAccountEntity source)
        {
            if (source == null) return;

            AdvertiserId = source.AdvertiserId;
            OwnerBcId = source.OwnerBcId;
            Status = source.Status;
            Role = source.Role;
            RejectionReason = source.RejectionReason;
            Name = source.Name;
            Timezone = source.Timezone;
            DisplayTimezone = source.DisplayTimezone;
            Company = source.Company;
            CompanyNameEditable = source.CompanyNameEditable;
            Industry = source.Industry;
            Address = source.Address;
            Country = source.Country;
            AdvertiserAccountType = source.AdvertiserAccountType;
            Currency = source.Currency;
            Contacter = source.Contacter;
            Email = source.Email;
            CellphoneNumber = source.CellphoneNumber;
            TelephoneNumber = source.TelephoneNumber;
            Language = source.Language;
            LicenseNo = source.LicenseNo;
            LicenseUrl = source.LicenseUrl;
            Description = source.Description;
            Balance = source.Balance;
            CreateTime = source.CreateTime;
            IsRemoved = source.IsRemoved;
            RemovedAt = source.RemovedAt;
            RelationType = source.RelationType;
        }

        /// <summary>
        /// Cập nhật entity từ AssetEntity
        /// </summary>
        /// <param name="asset">Asset entity để cập nhật dữ liệu</param>
        public void UpdateFromAsset(RawAssetEntity asset)
        {
            if (asset == null) return;

            // Cập nhật các trường có thể map từ Asset
            AdvertiserId = asset.AssetId ?? AdvertiserId;
            OwnerBcId = asset.BcId ?? OwnerBcId;
            Status = asset.AdvertiserStatus ?? Status;
            Name = asset.AssetName ?? Name;
            AdvertiserAccountType = asset.AdvertiserAccountType ?? AdvertiserAccountType;
            IsRemoved = asset.IsRemoved;
            RemovedAt = asset.RemovedAt;
            RelationType = asset.RelationType;
        }

        /// <summary>
        /// Tạo một entity mới từ AssetEntity (Static Method)
        /// </summary>
        /// <param name="asset">Asset entity để tạo AdAccount</param>
        /// <param name="bcId">ID của Business Center (fallback nếu asset.BcId null)</param>
        /// <returns>Entity AdAccount mới</returns>
        public static RawAdAccountEntity CreateFromAsset(RawAssetEntity asset, string bcId = "")
        {
            if (asset == null)
                throw new ArgumentNullException(nameof(asset), "Asset không được null");

            return new RawAdAccountEntity(Guid.NewGuid())
            {
                AdvertiserId = asset.AssetId ?? string.Empty,
                OwnerBcId = asset.BcId ?? bcId,
                Status = asset.AdvertiserStatus ?? AdAccountStatus.STATUS_ENABLE,
                Role = AdAccountRole.ROLE_ADVERTISER,
                Name = asset.AssetName ?? string.Empty,
                CompanyNameEditable = false,
                AdvertiserAccountType = asset.AdvertiserAccountType ?? AdAccountType.AUCTION,
                Balance = 0,
                CreateTime = asset.CreationTime,
                IsRemoved = asset.IsRemoved,
                RemovedAt = asset.RemovedAt,
                RelationType = asset.RelationType
            };
        }
    }
}