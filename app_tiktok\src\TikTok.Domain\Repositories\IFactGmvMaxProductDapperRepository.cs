using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Facts.FactGmvMaxCampaign;
using TikTok.Facts.FactGmvMaxProduct;

namespace TikTok.Domain.Repositories
{
    public interface IFactGmvMaxProductDapperRepository
    {
        Task<IEnumerable<FactGmvMaxProductEntity>> GetListByDateRangeAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductTrendDto>> GetTrendsAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null);
        Task<IEnumerable<GmvMaxProductTopSellingDto>> GetTopSellingAsync(DateTime from, DateTime to, int limit, List<string>? allowedAdvertiserIds = null);
        Task<GmvMaxProductDashboardDto> GetDashboardAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        
        // ✅ NEW: Section-specific methods for independent loading
        Task<SummaryCardsDto> GetSummaryCardsAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<DetailedChartsDto> GetDetailedChartsAsync(List<string>? allowedAdvertiserIds = null);
        Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
    }
}


