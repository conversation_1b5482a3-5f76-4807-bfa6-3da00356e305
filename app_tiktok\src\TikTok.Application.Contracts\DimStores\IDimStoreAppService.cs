using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DimStores
{
    /// <summary>
    /// Service interface for DimStore operations
    /// </summary>
    public interface IDimStoreAppService : IApplicationService
    {
        /// <summary>
        /// Get all active stores for dropdown/multiselect
        /// </summary>
        Task<List<DimStoreDto>> GetActiveStoresAsync();

        /// <summary>
        /// Get stores by country
        /// </summary>
        Task<List<DimStoreDto>> GetStoresByCountryAsync(string country);
    }
}
