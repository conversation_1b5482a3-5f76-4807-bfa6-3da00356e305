using System;
using System.Collections.Generic;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimCampaigns;
using TikTok.DimDates;
using TikTok.DimStores;

namespace TikTok.FactGmvMaxCampaigns.Dtos
{
    public class GetFactGmvMaxCampaignDataResponse
    {
        /// <summary>
        /// Ng<PERSON>y bắt đầu của dữ liệu
        /// </summary>
        public DateTime From { get; set; }

        /// <summary>
        /// Ng<PERSON>y kết thúc của dữ liệu
        /// </summary>
        public DateTime To { get; set; }

        /// <summary>
        /// Tiền tệ sử dụng (USD/VND)
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Danh sách dữ liệu fact GMV Max Campaign
        /// </summary>
        public List<FactGmvMaxCampaignDto> FactGmvMaxCampaigns { get; set; } = new();

        /// <summary>
        /// Dimension ngày tháng
        /// </summary>
        public List<DimDateDto> DimDates { get; set; } = new();

        /// <summary>
        /// Dimension tài khoản quảng cáo
        /// </summary>
        public List<DimAdAccountDto> DimAdAccounts { get; set; } = new();

        /// <summary>
        /// Dimension trung tâm kinh doanh
        /// </summary>
        public List<DimBusinessCenterDto> DimBusinessCenters { get; set; } = new();

        /// <summary>
        /// Dimension chiến dịch
        /// </summary>
        public List<DimCampaignDto> DimCampaigns { get; set; } = new();

        /// <summary>
        /// Dimension store
        /// </summary>
        public List<DimStoreDto> DimStores { get; set; } = new();

    }
}
