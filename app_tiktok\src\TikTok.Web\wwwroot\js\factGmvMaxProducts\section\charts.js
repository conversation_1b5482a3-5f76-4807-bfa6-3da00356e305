/**
 * 🎯 Charts Section - Đơn gi<PERSON>n
 * Tự call API + tự render
 */

// ✅ Font configuration constants for easy maintenance
(function () {
    window.CHART_FONT_CONFIG = {
        fontFamily: 'Inter, sans-serif',
        fontSize: {
            title: '16px',
            axisTitle: '14px',
            axisLabel: '12px',
        },
        fontWeight: {
            title: '600',
            axisTitle: '500',
        },
    };

    // ✅ Base style với font chung
    window.BASE_FONT_STYLE = {
        fontFamily: window.CHART_FONT_CONFIG.fontFamily,
    };
})();

class ChartsSection {
    constructor() {
        this.chartsContainer = '#charts-container';
        this.detailedChartsContainer = '#detailed-charts-container';
        this.initialized = false;

        // ✅ OPTIMIZED: Add data processing cache
        this.dataCache = new Map();
        this.processedDataCache = new Map();

        // ✅ OPTIMIZED: Add performance monitoring
        this.performanceMetrics = {
            chartCreationTimes: new Map(),
            dataProcessingTimes: new Map(),
            cacheHitRates: { hits: 0, misses: 0 },
        };
    }

    /**
     * 🎯 Khởi tạo section
     */
    async init() {
        if (this.initialized) {
            return;
        }

        try {
            await this.loadData();
            this.initialized = true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 🎯 Load data từ API (OPTIMIZED - 1 API call duy nhất)
     */
    async loadData() {
        try {
            // Show loading for both containers
            this.showLoading();

            // ✅ OPTIMIZED: Check cache first
            const cacheKey = 'charts_data';
            let combinedData = this.dataCache.get(cacheKey);

            if (!combinedData) {
                // ✅ OPTIMIZED: Chỉ gọi 1 API duy nhất với currency
                const currency =
                    localStorage.getItem('tiktok_currency') || 'USD';
                const response = await fetch(
                    `/api/fact-gmv-max-product/charts?currency=${currency}`
                );

                if (!response.ok) {
                    throw new Error(
                        `Charts API failed: ${response.status} ${response.statusText}`
                    );
                }

                combinedData = await response.json();

                // ✅ OPTIMIZED: Cache the data for 5 minutes
                this.dataCache.set(cacheKey, combinedData);
                setTimeout(() => {
                    this.dataCache.delete(cacheKey);
                }, 5 * 60 * 1000); // 5 minutes TTL
            }

            // ✅ OPTIMIZED: Render cả overview và detailed charts từ 1 response
            await this.renderCharts(combinedData);
            await this.renderDetailedCharts(combinedData);
        } catch (error) {
            this.showError(error);
            throw error;
        }
    }

    /**
     * 🎯 Render overview charts
     */
    async renderCharts(data) {
        const container = document.querySelector(this.chartsContainer);
        if (!container) {
            return;
        }

        const now = new Date();
        const currentMonthName = this.getVietnameseMonthName(now);
        const lastMonthDate = new Date(
            now.getFullYear(),
            now.getMonth() - 1,
            1
        );
        const lastMonthName = this.getVietnameseMonthName(lastMonthDate);

        container.innerHTML = `
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-line text-success"></i> Chi tiêu theo tuần - ${currentMonthName}</h6>
                        </div>
                        <div class="card-body">
                            <div id="weeklyCurrentChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-line text-warning"></i> Chi tiêu theo tuần - ${lastMonthName}</h6>
                        </div>
                        <div class="card-body">
                            <div id="weeklyLastChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-bar text-primary"></i> Chi tiêu theo tháng </h6>
                        </div>
                        <div class="card-body">
                            <div id="monthlyChart" style="height: 400px;"></div>
                        </div>
                        </div>
                    </div>
                </div>
        `;

        // ✅ OPTIMIZED: Use requestAnimationFrame for better performance
        requestAnimationFrame(async () => {
            await this.initializeSyncfusionCharts(data);
        });
    }

    /**
     * 🎯 Render detailed charts
     */
    async renderDetailedCharts(data) {
        const container = document.querySelector(this.detailedChartsContainer);
        if (!container) {
            return;
        }

        container.innerHTML = `
            <!-- Financial Chart - Full Width -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-dollar-sign text-success"></i> Phân tích tài chính (7 ngày gần nhất)</h5>
                            <small class="text-muted">Chi phí, Doanh thu và ROI theo từng ngày</small>
                        </div>
                        <div class="card-body">
                            <div id="financialChart" style="height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Orders Chart - Full Width -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-shopping-cart text-primary"></i> Phân tích đơn hàng (7 ngày gần nhất)</h5>
                            <small class="text-muted">Số lượng đơn hàng và Chi phí mỗi đơn hàng (CPA) theo từng ngày</small>
                        </div>
                        <div class="card-body">
                            <div id="ordersChart" style="height: 400px;"></div>
                        </div>
                        </div>
                    </div>
                </div>
            
            <!-- Performance Chart - Full Width -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-pie text-warning"></i> Hiệu suất sản phẩm (7 ngày gần nhất)</h5>
                            <small class="text-muted">ROI và TACOS theo từng ngày</small>
                        </div>
                        <div class="card-body">
                            <div id="performanceChart" style="height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // ✅ OPTIMIZED: Smart lazy loading for detailed charts
        this.setupDetailedChartsLazyLoading(data);
    }

    /**
     * ✅ OPTIMIZED: Setup smart lazy loading for detailed charts
     */
    setupDetailedChartsLazyLoading(data) {
        const detailedTab = document.getElementById('detailed-chart-tab');
        const detailedContainer = document.getElementById(
            'detailed-charts-container'
        );

        if (detailedTab && detailedContainer) {
            // ✅ OPTIMIZED: Only load when tab is actually clicked
            detailedTab.addEventListener(
                'shown.bs.tab',
                function () {
                    // Check if charts are already loaded
                    if (
                        detailedContainer.querySelector('#financialChart')
                            ?.ej2_instances?.length > 0
                    ) {
                        return;
                    }
                    requestAnimationFrame(async () => {
                        await this.initializeDetailedCharts(data);
                    });
                }.bind(this),
                { once: true }
            ); // ✅ OPTIMIZED: Only listen once

            // ✅ OPTIMIZED: Preload if tab is already active
            if (detailedTab.classList.contains('active')) {
                requestAnimationFrame(async () => {
                    await this.initializeDetailedCharts(data);
                });
            }
        } else {
            // ✅ OPTIMIZED: Fallback with requestAnimationFrame
            requestAnimationFrame(async () => {
                await this.initializeDetailedCharts(data);
            });
        }
    }

    /**
     * 🎯 Get Vietnamese month name
     */
    getVietnameseMonthName(date) {
        if (
            window.sharedDateHelper &&
            window.sharedDateHelper.getVietnameseMonthName
        ) {
            return window.sharedDateHelper.getVietnameseMonthName(
                date.toISOString()
            );
        }
        const months = [
            'Tháng 1',
            'Tháng 2',
            'Tháng 3',
            'Tháng 4',
            'Tháng 5',
            'Tháng 6',
            'Tháng 7',
            'Tháng 8',
            'Tháng 9',
            'Tháng 10',
            'Tháng 11',
            'Tháng 12',
        ];
        return months[date.getMonth()] || 'Tháng không xác định';
    }

    /**
     * ✅ OPTIMIZED: Get processed chart data with caching
     */
    getProcessedChartData(data) {
        const startTime = performance.now();
        const cacheKey = `processed_${JSON.stringify(data).slice(0, 100)}`;

        if (this.processedDataCache.has(cacheKey)) {
            this.performanceMetrics.cacheHitRates.hits++;
            return this.processedDataCache.get(cacheKey);
        }

        this.performanceMetrics.cacheHitRates.misses++;

        const now = new Date();
        const currentMonth = now.getMonth() + 1;
        const currentYear = now.getFullYear();
        const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1;
        const lastYear = currentMonth === 1 ? currentYear - 1 : currentYear;

        const processedData = {
            currentMonthWeeklyData:
                data.weeklyData
                    ?.filter(
                        (w) =>
                            w.year === currentYear && w.month === currentMonth
                    )
                    .map((w) => ({
                        period: `Tuần ${w.week}`,
                        value: w.totalCost || 0,
                    })) || [],

            lastMonthWeeklyData:
                data.weeklyData
                    ?.filter(
                        (w) => w.year === lastYear && w.month === lastMonth
                    )
                    .map((w) => ({
                        period: `Tuần ${w.week}`,
                        value: w.totalCost || 0,
                    })) || [],

            monthlyData:
                data.monthlyData?.map((m) => ({
                    period: m.monthName || `Tháng ${m.month}`,
                    value: m.totalCost || 0,
                })) || [],
        };

        // Cache for 2 minutes (shorter than data cache)
        this.processedDataCache.set(cacheKey, processedData);
        setTimeout(() => {
            this.processedDataCache.delete(cacheKey);
        }, 2 * 60 * 1000);

        // ✅ OPTIMIZED: Track processing time
        const endTime = performance.now();
        this.performanceMetrics.dataProcessingTimes.set(
            cacheKey,
            endTime - startTime
        );

        return processedData;
    }

    /**
     * ✅ OPTIMIZED: Destroy existing charts with proper cleanup
     */
    destroyExistingCharts() {
        const chartIds = [
            'weeklyCurrentChart',
            'weeklyLastChart',
            'monthlyChart',
            'financialChart',
            'ordersChart',
            'performanceChart',
        ];

        chartIds.forEach((id) => {
            const element = document.getElementById(id);
            if (element && element.ej2_instances) {
                element.ej2_instances.forEach((instance) => {
                    if (instance.destroy) {
                        instance.destroy();
                    }
                });

                // ✅ OPTIMIZED: Clear references to prevent memory leaks
                element.ej2_instances = null;

                // ✅ OPTIMIZED: Clear DOM content
                element.innerHTML = '';
            }
        });
    }

    /**
     * ✅ OPTIMIZED: Create all overview charts in parallel
     */
    async createAllOverviewCharts(chartData) {
        const chartPromises = [];

        // Get currency from localStorage or default to USD
        const currency =
            (typeof localStorage !== 'undefined'
                ? localStorage.getItem('tiktok_currency')
                : null) || 'USD';
        const symbol = currency === 'VND' ? '₫' : '$';

        // ✅ OPTIMIZED: Create charts in parallel if data exists
        if (chartData.currentMonthWeeklyData.length > 0) {
            chartPromises.push(
                this.createSyncfusionChart(
                    '#weeklyCurrentChart',
                    chartData.currentMonthWeeklyData,
                    'Chi phí theo tuần',
                    symbol
                )
            );
        } else {
            this.showNoDataMessage(
                '#weeklyCurrentChart',
                'Không có dữ liệu tuần hiện tại'
            );
        }

        if (chartData.lastMonthWeeklyData.length > 0) {
            chartPromises.push(
                this.createSyncfusionChart(
                    '#weeklyLastChart',
                    chartData.lastMonthWeeklyData,
                    'Chi phí theo tuần',
                    symbol
                )
            );
        } else {
            this.showNoDataMessage(
                '#weeklyLastChart',
                'Không có dữ liệu tuần trước'
            );
        }

        if (chartData.monthlyData.length > 0) {
            chartPromises.push(
                this.createSyncfusionChart(
                    '#monthlyChart',
                    chartData.monthlyData,
                    'Chi phí theo tháng',
                    symbol
                )
            );
        } else {
            this.showNoDataMessage('#monthlyChart', 'Không có dữ liệu tháng');
        }

        // ✅ OPTIMIZED: Execute all chart creations in parallel
        if (chartPromises.length > 0) {
            await Promise.all(chartPromises);
        }
    }

    /**
     * ✅ OPTIMIZED: Initialize Syncfusion charts
     */
    async initializeSyncfusionCharts(data) {
        if (typeof ej === 'undefined' || !ej.charts) {
            return;
        }

        // Destroy existing charts first
        this.destroyExistingCharts();

        // ✅ OPTIMIZED: Use cached processed data
        const processedData = this.getProcessedChartData(data);

        // ✅ OPTIMIZED: Process weekly data for current month
        const currentMonthWeeklyData = processedData.currentMonthWeeklyData;

        // ✅ OPTIMIZED: Process weekly data for last month
        const lastMonthWeeklyData = processedData.lastMonthWeeklyData;

        // ✅ OPTIMIZED: Process monthly data
        const monthlyData = processedData.monthlyData;

        // ✅ OPTIMIZED: Batch chart creation with Promise.all()
        await this.createAllOverviewCharts({
            currentMonthWeeklyData,
            lastMonthWeeklyData,
            monthlyData,
        });
    }

    /**
     * 🎯 Show no data message
     */
    showNoDataMessage(selector, message) {
        const container = document.querySelector(selector);
        if (!container) return;

        container.innerHTML = `
            <div class="d-flex align-items-center justify-content-center" style="height: 100%; min-height: 200px;">
                <div class="text-center text-muted">
                    <i class="fas fa-chart-line fa-3x mb-3 opacity-50"></i>
                    <h6 class="mb-2">${message}</h6>
                    <small>Vui lòng kiểm tra lại dữ liệu hoặc thời gian</small>
                </div>
            </div>
        `;
    }

    /**
     * ✅ OPTIMIZED: Setup smart lazy loading for detailed charts
     */
    setupDetailedChartsLazyLoading(data) {
        const detailedTab = document.getElementById('detailed-chart-tab');
        const detailedContainer = document.getElementById(
            'detailed-charts-container'
        );

        if (detailedTab && detailedContainer) {
            // ✅ OPTIMIZED: Only load when tab is actually clicked
            detailedTab.addEventListener(
                'shown.bs.tab',
                function () {
                    // Check if charts are already loaded
                    if (
                        detailedContainer.querySelector('#financialChart')
                            ?.ej2_instances?.length > 0
                    ) {
                        return;
                    }
                    // Load detailed charts
                    this.createDetailedCharts(data);
                }.bind(this)
            );
        }
    }

    /**
     * 🎯 Create overview charts
     */
    async createOverviewCharts(data) {
        try {
            // Weekly Trend Chart
            await this.createWeeklyTrendChart(data.weeklyData, data.currency);

            // Monthly Trend Chart
            await this.createMonthlyTrendChart(data.monthlyData, data.currency);
        } catch (error) {
            // Error creating overview charts
        }
    }

    /**
     * 🎯 Create detailed charts
     */
    async createDetailedCharts(data) {
        try {
            // Financial Chart
            if (data.financialAnalysis?.dailyData?.length > 0) {
                await this.createFinancialChart(
                    data.financialAnalysis.dailyData,
                    data.currency
                );
            } else {
                this.showNoDataMessage(
                    '#financialChart',
                    'Không có dữ liệu tài chính'
                );
            }

            // Orders Chart
            if (data.ordersAnalysis?.dailyData?.length > 0) {
                await this.createOrdersChart(
                    data.ordersAnalysis.dailyData,
                    data.currency
                );
            } else {
                this.showNoDataMessage(
                    '#ordersChart',
                    'Không có dữ liệu đơn hàng'
                );
            }

            // Performance Chart
            if (data.financialAnalysis?.dailyData?.length > 0) {
                await this.createPerformanceChart(
                    data.financialAnalysis.dailyData,
                    data.currency
                );
            } else {
                this.showNoDataMessage(
                    '#performanceChart',
                    'Không có dữ liệu hiệu suất'
                );
            }
        } catch (error) {
            // Error creating detailed charts
        }
    }

    /**
     * 🎯 Create weekly chart using Syncfusion
     */
    async createWeeklyChart(weeklyData, currency) {
        const container = document.getElementById('weeklyChart');
        if (!container || !weeklyData) return;

        const currencySymbol = currency === 'VND' ? '₫' : '$';

        // Process weekly data
        const chartData = weeklyData.map((item) => ({
            x: `Tuần ${item.week} (${item.month}/${item.year})`,
            revenue: item.totalRevenue || 0,
            cost: item.totalCost || 0,
        }));

        // Create Syncfusion Chart
        const chart = new ej.charts.Chart({
            primaryXAxis: {
                title: 'Tuần',
                valueType: 'Category',
            },
            primaryYAxis: {
                title: `Số tiền (${currencySymbol})`,
                labelFormat: currencySymbol + '{value}',
            },
            series: [
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'revenue',
                    name: `Doanh thu (${currencySymbol})`,
                    type: 'Line',
                    fill: '#28a745',
                    border: { color: '#28a745', width: 2 },
                },
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'cost',
                    name: `Chi phí (${currencySymbol})`,
                    type: 'Line',
                    fill: '#dc3545',
                    border: { color: '#dc3545', width: 2 },
                },
            ],
            title: 'Xu hướng Doanh thu & Chi phí theo Tuần',
            legendSettings: { visible: true, position: 'Top' },
            tooltip: {
                enable: true,
                format: '${series.name}: ' + currencySymbol + '${point.y}',
            },
        });

        chart.appendTo('#weeklyChart');
    }

    /**
     * 🎯 Create monthly chart using Syncfusion
     */
    async createMonthlyChart(monthlyData, currency) {
        const container = document.getElementById('monthlyChart');
        if (!container || !monthlyData) return;

        const currencySymbol = currency === 'VND' ? '₫' : '$';

        // Process monthly data
        const chartData = monthlyData.map((item) => ({
            x: `${item.monthName} ${item.year}`,
            revenue: item.totalRevenue || 0,
            cost: item.totalCost || 0,
        }));

        // Create Syncfusion Chart
        const chart = new ej.charts.Chart({
            primaryXAxis: {
                title: 'Tháng',
                valueType: 'Category',
            },
            primaryYAxis: {
                title: `Số tiền (${currencySymbol})`,
                labelFormat: currencySymbol + '{value}',
            },
            series: [
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'revenue',
                    name: `Doanh thu (${currencySymbol})`,
                    type: 'Column',
                    fill: '#28a745',
                },
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'cost',
                    name: `Chi phí (${currencySymbol})`,
                    type: 'Column',
                    fill: '#dc3545',
                },
            ],
            title: 'Xu hướng Doanh thu & Chi phí theo Tháng',
            legendSettings: { visible: true, position: 'Top' },
            tooltip: {
                enable: true,
                format: '${series.name}: ' + currencySymbol + '${point.y}',
            },
        });

        chart.appendTo('#monthlyChart');
    }

    /**
     * 🎯 Create weekly trend chart using Syncfusion
     */
    async createWeeklyTrendChart(weeklyData, currency) {
        const container = document.getElementById('weeklyTrendChart');
        if (!container || !weeklyData) return;

        const currencySymbol = currency === 'VND' ? '₫' : '$';

        // Process weekly data
        const chartData = weeklyData.map((item) => ({
            x: `Week ${item.week} (${item.month}/${item.year})`,
            revenue: item.totalRevenue || 0,
            cost: item.totalCost || 0,
        }));

        // Create Syncfusion Chart
        const chart = new ej.charts.Chart({
            primaryXAxis: {
                title: 'Week',
                valueType: 'Category',
            },
            primaryYAxis: {
                title: `Amount (${currencySymbol})`,
                labelFormat: currencySymbol + '{value}',
            },
            series: [
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'revenue',
                    name: `Revenue (${currencySymbol})`,
                    type: 'Line',
                    fill: '#28a745',
                    border: { color: '#28a745', width: 2 },
                },
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'cost',
                    name: `Cost (${currencySymbol})`,
                    type: 'Line',
                    fill: '#dc3545',
                    border: { color: '#dc3545', width: 2 },
                },
            ],
            title: 'Weekly Revenue & Cost Trend',
            legendSettings: { visible: true, position: 'Top' },
            tooltip: {
                enable: true,
                format: '${series.name}: ' + currencySymbol + '${point.y}',
            },
        });

        chart.appendTo('#weeklyTrendChart');
    }

    /**
     * 🎯 Create monthly trend chart using Syncfusion
     */
    async createMonthlyTrendChart(monthlyData, currency) {
        const container = document.getElementById('monthlyTrendChart');
        if (!container || !monthlyData) return;

        const currencySymbol = currency === 'VND' ? '₫' : '$';

        // Process monthly data
        const chartData = monthlyData.map((item) => ({
            x: `${item.monthName} ${item.year}`,
            revenue: item.totalRevenue || 0,
            cost: item.totalCost || 0,
        }));

        // Create Syncfusion Chart
        const chart = new ej.charts.Chart({
            primaryXAxis: {
                title: 'Month',
                valueType: 'Category',
            },
            primaryYAxis: {
                title: `Amount (${currencySymbol})`,
                labelFormat: currencySymbol + '{value}',
            },
            series: [
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'revenue',
                    name: `Revenue (${currencySymbol})`,
                    type: 'Column',
                    fill: '#28a745',
                },
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'cost',
                    name: `Cost (${currencySymbol})`,
                    type: 'Column',
                    fill: '#dc3545',
                },
            ],
            title: 'Monthly Revenue & Cost Trend',
            legendSettings: { visible: true, position: 'Top' },
            tooltip: {
                enable: true,
                format: '${series.name}: ' + currencySymbol + '${point.y}',
            },
        });

        chart.appendTo('#monthlyTrendChart');
    }

    /**
     * 🎯 Create financial chart using Syncfusion
     */
    async createFinancialChart(dailyData, currency) {
        const container = document.getElementById('financialChart');
        if (!container || !dailyData) return;

        const currencySymbol = currency === 'VND' ? '₫' : '$';

        // Process daily data
        const chartData = dailyData.map((item) => ({
            x: item.period || '',
            revenue: item.revenue || 0,
            cost: item.cost || 0,
            roi: item.roi || 0,
        }));

        // Create Syncfusion Chart
        const chart = new ej.charts.Chart({
            primaryXAxis: {
                title: 'Ngày',
                valueType: 'Category',
            },
            primaryYAxis: {
                title: `Số tiền (${currencySymbol})`,
                labelFormat: currencySymbol + '{value}',
            },
            axes: [
                {
                    name: 'yAxis1',
                    title: 'ROI (%)',
                    labelFormat: '{value}%',
                    opposedPosition: true,
                },
            ],
            series: [
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'revenue',
                    name: `Doanh thu (${currencySymbol})`,
                    type: 'Line',
                    fill: '#28a745',
                    border: { color: '#28a745', width: 2 },
                },
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'cost',
                    name: `Chi phí (${currencySymbol})`,
                    type: 'Line',
                    fill: '#dc3545',
                    border: { color: '#dc3545', width: 2 },
                },
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'roi',
                    name: 'ROI (%)',
                    type: 'Line',
                    fill: '#ffc107',
                    border: { color: '#ffc107', width: 2 },
                    yAxisName: 'yAxis1',
                },
            ],
            title: 'Phân tích Tài chính Hàng ngày',
            legendSettings: { visible: true, position: 'Top' },
            tooltip: {
                enable: true,
                format: '${series.name}: ${point.y}',
            },
        });

        chart.appendTo('#financialChart');
    }

    /**
     * 🎯 Create orders chart using Syncfusion
     */
    async createOrdersChart(dailyData, currency) {
        const container = document.getElementById('ordersChart');
        if (!container || !dailyData) return;

        const currencySymbol = currency === 'VND' ? '₫' : '$';

        // Process daily data
        const chartData = dailyData.map((item) => ({
            x: item.period || '',
            orders: item.orders || 0,
            cpa: item.cpa || 0,
        }));

        // Create Syncfusion Chart
        const chart = new ej.charts.Chart({
            primaryXAxis: {
                title: 'Ngày',
                valueType: 'Category',
            },
            primaryYAxis: {
                title: 'Số đơn hàng',
                labelFormat: '{value}',
            },
            axes: [
                {
                    name: 'yAxis1',
                    title: `CPA (${currencySymbol})`,
                    labelFormat: currencySymbol + '{value}',
                    opposedPosition: true,
                },
            ],
            series: [
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'orders',
                    name: 'Đơn hàng',
                    type: 'Column',
                    fill: '#17a2b8',
                },
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'cpa',
                    name: `CPA (${currencySymbol})`,
                    type: 'Line',
                    fill: '#ffc107',
                    border: { color: '#ffc107', width: 2 },
                    yAxisName: 'yAxis1',
                },
            ],
            title: 'Phân tích Đơn hàng Hàng ngày',
            legendSettings: { visible: true, position: 'Top' },
            tooltip: {
                enable: true,
                format: '${series.name}: ${point.y}',
            },
        });

        chart.appendTo('#ordersChart');
    }

    /**
     * 🎯 Create performance chart using Syncfusion
     */
    async createPerformanceChart(dailyData, currency) {
        const container = document.getElementById('performanceChart');
        if (!container || !dailyData) return;

        const currencySymbol = currency === 'VND' ? '₫' : '$';

        // Process daily data
        const chartData = dailyData.map((item) => ({
            x: item.period || '',
            roi: item.roi || 0,
            tacos: (item.tacos || 0) * 100, // Convert to percentage
        }));

        // Create Syncfusion Chart
        const chart = new ej.charts.Chart({
            primaryXAxis: {
                title: 'Ngày',
                valueType: 'Category',
            },
            primaryYAxis: {
                title: 'ROI (%)',
                labelFormat: '{value}%',
            },
            axes: [
                {
                    name: 'yAxis1',
                    title: 'TACOS (%)',
                    labelFormat: '{value}%',
                    opposedPosition: true,
                },
            ],
            series: [
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'roi',
                    name: 'ROI (%)',
                    type: 'Line',
                    fill: '#28a745',
                    border: { color: '#28a745', width: 2 },
                },
                {
                    dataSource: chartData,
                    xName: 'x',
                    yName: 'tacos',
                    name: 'TACOS (%)',
                    type: 'Line',
                    fill: '#dc3545',
                    border: { color: '#dc3545', width: 2 },
                    yAxisName: 'yAxis1',
                },
            ],
            title: 'Phân tích Hiệu suất Hàng ngày',
            legendSettings: { visible: true, position: 'Top' },
            tooltip: {
                enable: true,
                format: '${series.name}: ${point.y}%',
            },
        });

        chart.appendTo('#performanceChart');
    }

    /**
     * 🎯 Calculate average daily value
     */
    calculateAverageDaily(dailyData, field) {
        if (!dailyData || dailyData.length === 0) return '0';

        const sum = dailyData.reduce(
            (acc, item) => acc + (item[field] || 0),
            0
        );
        const average = sum / dailyData.length;

        return new Intl.NumberFormat().format(Math.round(average));
    }

    /**
     * 🎯 Show loading
     */
    showLoading() {
        const containers = [this.chartsContainer, this.detailedChartsContainer];

        containers.forEach((containerSelector) => {
            const container = document.querySelector(containerSelector);
            if (!container) return;

            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Loading charts data...</p>
                </div>
            `;
        });
    }

    /**
     * 🎯 Hide loading
     */
    hideLoading() {
        // Loading will be replaced by actual content
    }

    /**
     * 🎯 Show error
     */
    showError(error) {
        const containers = [this.chartsContainer, this.detailedChartsContainer];

        containers.forEach((containerSelector) => {
            const container = document.querySelector(containerSelector);
            if (!container) return;

            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">Error Loading Charts Data</h4>
                    <p>${error.message || 'An unexpected error occurred'}</p>
                    <hr>
                    <p class="mb-0">
                        <button class="btn btn-outline-danger btn-sm" onclick="window.location.reload()">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                    </p>
                </div>
            `;
        });
    }

    /**
     * 🎯 Refresh data
     */
    async refresh() {
        // Clear cache
        this.dataCache.clear();
        this.processedDataCache.clear();

        this.initialized = false;
        await this.init();
    }
}

// Export for global use
window.ChartsSection = ChartsSection;
