using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Facts.FactGmvMaxCampaign;

namespace TikTok.Domain.Repositories
{
    public interface IFactGmvMaxCampaignDapperRepository
    {
        // Existing methods
        Task<IEnumerable<FactGmvMaxCampaignEntity>> GetListByDateRangeAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<GmvMaxCampaignDashboardDto> GetDashboardAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<object> GetDetailedAnalysisDataAsync(List<string>? allowedAdvertiserIds = null);

        // ✅ NEW: Section-specific methods for independent loading
        /// <summary>
        /// Get summary cards data - optimized for quick loading
        /// </summary>
        /// <param name="currency">Currency (USD/VND)</param>
        /// <param name="allowedAdvertiserIds">User's allowed advertiser IDs</param>
        /// <param name="shoppingAdsType">Shopping ads type (PRODUCT/LIVE)</param>
        /// <param name="fromDate">From date</param>
        /// <param name="toDate">To date</param>
        /// <param name="searchText">Search text</param>
        /// <param name="shopIds">Shop IDs</param>
        /// <returns>Summary cards data</returns>
        Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD", 
            List<string>? allowedAdvertiserIds = null,
            string? shoppingAdsType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<Guid>? shopIds = null);

        /// <summary>
        /// Get overview section data - current month, last month, weekly data
        /// </summary>
        /// <param name="currency">Currency (USD/VND)</param>
        /// <param name="allowedAdvertiserIds">User's allowed advertiser IDs</param>
        /// <returns>Overview section data</returns>
        Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);

        /// <summary>
        /// Get charts data - weekly and monthly data for charts
        /// </summary>
        /// <param name="currency">Currency (USD/VND)</param>
        /// <param name="allowedAdvertiserIds">User's allowed advertiser IDs</param>
        /// <returns>Charts data</returns>
        Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);

        /// <summary>
        /// Get detailed charts data - financial, orders, live performance analysis
        /// </summary>
        /// <param name="allowedAdvertiserIds">User's allowed advertiser IDs</param>
        /// <returns>Detailed charts data</returns>
        Task<DetailedChartsDto> GetDetailedChartsAsync(List<string>? allowedAdvertiserIds = null);

        /// <summary>
        /// Get rankings data - store rankings by revenue and cost
        /// </summary>
        /// <param name="currency">Currency (USD/VND)</param>
        /// <param name="allowedAdvertiserIds">User's allowed advertiser IDs</param>
        /// <returns>Rankings data</returns>
        Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);

    }
}
