<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>TikTok</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\TikTok.Domain\TikTok.Domain.csproj" />
		<PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.PermissionManagement.EntityFrameworkCore" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.EntityFrameworkCore" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Identity.EntityFrameworkCore" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.AuditLogging.EntityFrameworkCore" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.EntityFrameworkCore" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.FeatureManagement.EntityFrameworkCore" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.OpenIddict.EntityFrameworkCore" Version="8.1.4" />
		<PackageReference Include="Elsa.Persistence.EntityFramework.Core" Version="2.12.0" />
		<PackageReference Include="Tsp.Zalo.EntityFrameworkCore" Version="1.0.0-prerelease-5916" />
    <PackageReference Include="Tsp.Module.Notifications.EntityFrameworkCore" Version="1.0.18-prerelease-6047" />
		<PackageReference Include="Dapper" Version="2.1.35" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
	</ItemGroup>

</Project>
