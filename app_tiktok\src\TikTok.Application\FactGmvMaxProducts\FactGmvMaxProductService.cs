using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Application.Services;
using TikTok.FactGmvMaxProducts.Dtos;
using TikTok.DimProducts;
using TikTok.DimStores;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Entities.Dim;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimCampaigns;
using TikTok.DimDates;
using Microsoft.Extensions.Logging;
using TikTok.Domain.Repositories;
using TikTok.Facts.FactGmvMaxProduct;
using TikTok.Permissions;
using Volo.Abp.Authorization;
using Volo.Abp.Authorization.Permissions;
using TikTok.Facts.FactGmvMaxCampaign;

namespace TikTok.FactGmvMaxProducts
{
    public class FactGmvMaxProductService : ApplicationService, IFactGmvMaxProductService
    {
        private readonly IRepository<FactGmvMaxProductEntity, Guid> _factGmvMaxProductRepository;
        private readonly IRepository<DimDateEntity, int> _dimDateRepository;
        private readonly IRepository<DimBusinessCenterEntity, Guid> _dimBusinessCenterRepository;
        private readonly IRepository<DimAdAccountEntity, Guid> _dimAdAccountRepository;
        private readonly IRepository<DimCampaignEntity, Guid> _dimCampaignRepository;
        private readonly IRepository<DimStoreEntity, Guid> _dimStoreRepository;
        private readonly IRepository<DimProductEntity, Guid> _dimProductRepository;
        private readonly IFactGmvMaxProductDapperRepository _factGmvMaxProductDapperRepository;
        private readonly IPermissionChecker _permissionChecker;
        private readonly PermissionFieldHelper _permissionFieldHelper;

        public FactGmvMaxProductService(
            IRepository<FactGmvMaxProductEntity, Guid> factGmvMaxProductRepository,
            IRepository<DimDateEntity, int> dimDateRepository,
            IRepository<DimBusinessCenterEntity, Guid> dimBusinessCenterRepository,
            IRepository<DimAdAccountEntity, Guid> dimAdAccountRepository,
            IRepository<DimCampaignEntity, Guid> dimCampaignRepository,
            IRepository<DimStoreEntity, Guid> dimStoreRepository,
            IRepository<DimProductEntity, Guid> dimProductRepository,
            IFactGmvMaxProductDapperRepository factGmvMaxProductDapperRepository,
            IPermissionChecker permissionChecker,
            PermissionFieldHelper permissionFieldHelper)
        {
            _factGmvMaxProductRepository = factGmvMaxProductRepository;
            _dimDateRepository = dimDateRepository;
            _dimBusinessCenterRepository = dimBusinessCenterRepository;
            _dimAdAccountRepository = dimAdAccountRepository;
            _dimCampaignRepository = dimCampaignRepository;
            _dimStoreRepository = dimStoreRepository;
            _dimProductRepository = dimProductRepository;
            _factGmvMaxProductDapperRepository = factGmvMaxProductDapperRepository;
            _permissionChecker = permissionChecker;
            _permissionFieldHelper = permissionFieldHelper;
        }

        public async Task<GetFactGmvMaxProductDataResponse> GetListAsync(DateTime fromDate, DateTime toDate, string? currency = "USD", bool hasViewAllAdvertisers = false)
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                var result = new GetFactGmvMaxProductDataResponse()
                {
                    From = fromDate,
                    To = toDate,
                    Currency = currency,
                    FactGmvMaxProducts = new List<FactGmvMaxProductDto>(),
                    DimAdAccounts = new List<DimAdAccountDto>(),
                    DimBusinessCenters = new List<DimBusinessCenterDto>(),
                    DimCampaigns = new List<DimCampaignDto>(),
                    DimStores = new List<DimStoreDto>(),
                    DimProducts = new List<DimProductDto>(),
                    DimDates = new List<DimDateDto>()
                };

                // ✅ Use Dapper repository with permission filtering and currency
                var factGmvMaxProducts = await _factGmvMaxProductDapperRepository.GetListByDateRangeAsync(fromDate, toDate, allowedAdvertiserIds, currency);
                var factList = factGmvMaxProducts is List<FactGmvMaxProductEntity> list ? list : new List<FactGmvMaxProductEntity>(factGmvMaxProducts);

                if (!factList.Any())
                {
                    // Return empty response if no data found
                    Logger.LogWarning($"No FactGmvMaxProduct data found for date range {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                    return result;
                }

                // Map fact data
                result.FactGmvMaxProducts = ObjectMapper.Map<List<FactGmvMaxProductEntity>, List<FactGmvMaxProductDto>>(factList);

                // Get all unique DimDateId values from factGmvMaxProducts
                var uniqueDimDateIds = factList.Select(f => f.DimDateId).Distinct().ToList();

                // Get all dim-dates that are included in factGmvMaxProducts data
                if (uniqueDimDateIds.Any())
                {
                    var dimDates = await _dimDateRepository.GetListAsync(d => uniqueDimDateIds.Contains(d.Id));
                    result.DimDates = ObjectMapper.Map<List<DimDateEntity>, List<DimDateDto>>(dimDates);
                }

                // Get all unique DimBusinessCenterId values from factGmvMaxProducts
                var uniqueDimBusinessCenterIds = factList.Select(f => f.DimBusinessCenterId).Distinct().ToList();

                // Get all dim-businesscenters that are included in factGmvMaxProducts data
                if (uniqueDimBusinessCenterIds.Any())
                {
                    var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync(d => uniqueDimBusinessCenterIds.Contains(d.Id));
                    result.DimBusinessCenters = ObjectMapper.Map<List<DimBusinessCenterEntity>, List<DimBusinessCenterDto>>(dimBusinessCenters);
                }

                // Get all unique DimAdAccountId values from factGmvMaxProducts
                var uniqueDimAdAccountIds = factList.Select(f => f.DimAdAccountId).Distinct().ToList();

                // Get all dim-adaccounts that are included in factGmvMaxProducts data
                if (uniqueDimAdAccountIds.Any())
                {
                    var dimAdAccounts = await _dimAdAccountRepository.GetListAsync(d => uniqueDimAdAccountIds.Contains(d.Id));
                    result.DimAdAccounts = ObjectMapper.Map<List<DimAdAccountEntity>, List<DimAdAccountDto>>(dimAdAccounts);
                }

                // Get all unique DimCampaignId values from factGmvMaxProducts
                var uniqueDimCampaignIds = factList.Select(f => f.DimCampaignId).Distinct().ToList();

                // Get all dim-campaigns that are included in factGmvMaxProducts data
                if (uniqueDimCampaignIds.Any())
                {
                    var dimCampaigns = await _dimCampaignRepository.GetListAsync(d => uniqueDimCampaignIds.Contains(d.Id));
                    result.DimCampaigns = ObjectMapper.Map<List<DimCampaignEntity>, List<DimCampaignDto>>(dimCampaigns);
                }

                // Get all unique DimStoreId values from factGmvMaxProducts
                var uniqueDimStoreIds = factList.Select(f => f.DimStoreId).Distinct().ToList();

                // Get all dim-stores that are included in factGmvMaxProducts data
                if (uniqueDimStoreIds.Any())
                {
                    var dimStores = await _dimStoreRepository.GetListAsync(d => uniqueDimStoreIds.Contains(d.Id));
                    result.DimStores = ObjectMapper.Map<List<DimStoreEntity>, List<DimStoreDto>>(dimStores);
                }

                // Get all unique DimProductId values from factGmvMaxProducts
                var uniqueDimProductIds = factList.Select(f => f.DimProductId).Distinct().ToList();

                // Get all dim-products that are included in factGmvMaxProducts data
                if (uniqueDimProductIds.Any())
                {
                    var dimProducts = await _dimProductRepository.GetListAsync(d => uniqueDimProductIds.Contains(d.Id));
                    result.DimProducts = ObjectMapper.Map<List<DimProductEntity>, List<DimProductDto>>(dimProducts);
                }

                Logger.LogInformation($"Successfully loaded {result.FactGmvMaxProducts.Count} GMV Max Product records and dimension data");

                return result;
            }
            catch (Exception ex)
            {
                // Log the exception (similar to FactGmvMaxCampaignService)
                Logger.LogError(ex, "Error retrieving FactGmvMaxProduct data from database");
                throw new ValidationException($"Error retrieving GMV Max Product data: {ex.Message}");
            }
        }

        public async Task<IEnumerable<GmvMaxProductTrendDto>> GetTrendsAsync(DateTime fromDate, DateTime toDate)
        {
            // ✅ Check permissions for trends access
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);
            
            // ✅ Get user's allowed advertiser IDs for permission filtering
            var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);
            
            return await _factGmvMaxProductDapperRepository.GetTrendsAsync(fromDate, toDate, allowedAdvertiserIds);
        }

        public async Task<IEnumerable<GmvMaxProductTopSellingDto>> GetTopSellingAsync(DateTime fromDate, DateTime toDate, int limit)
        {
            // ✅ Check permissions for top selling access
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);
            
            // ✅ Get user's allowed advertiser IDs for permission filtering
            var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);
            
            return await _factGmvMaxProductDapperRepository.GetTopSellingAsync(fromDate, toDate, limit, allowedAdvertiserIds);
        }

        public async Task<GmvMaxProductDashboardDto> GetDashboardAsync(string? currency = "USD")
        {
            // ✅ Check permissions for dashboard access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập dashboard GMV Max Product");
            }

            // ✅ Get user's allowed advertiser IDs for permission filtering
            var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

            var dashboardData = await _factGmvMaxProductDapperRepository.GetDashboardAsync(currency, allowedAdvertiserIds);
            
            // ✅ Filter dashboard data based on permissions
            return FilterDashboardDataByPermissions(dashboardData, hasViewSpending, hasViewMetrics, hasViewAll);
        }

        public async Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD")
        {
            // ✅ Check permissions for dashboard summary access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập dashboard summary GMV Max Product");
            }

            // ✅ Get user's allowed advertiser IDs for permission filtering
            var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

            var summaryData = await _factGmvMaxProductDapperRepository.GetDashboardSummaryAsync(currency, allowedAdvertiserIds);
            
            // ✅ Filter summary data based on permissions
            return FilterDashboardSummaryByPermissions(summaryData, hasViewSpending, hasViewMetrics, hasViewAll);
        }

        public async Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD")
        {
            // ✅ Check permissions for detailed analysis access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập detailed analysis GMV Max Product");
            }

            // ✅ Get user's allowed advertiser IDs for permission filtering
            var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

            var detailedData = await _factGmvMaxProductDapperRepository.GetDetailedAnalysisDataAsync(currency, allowedAdvertiserIds);
            
            // ✅ Filter detailed analysis data based on permissions
            return FilterDetailedAnalysisByPermissions(detailedData, hasViewSpending, hasViewMetrics, hasViewAll);
        }

        public Task<FactGmvMaxProductSummaryDto> GetSummaryAsync(DateTime fromDate, DateTime toDate)
        {
            // TODO: Change Entity
            throw new NotImplementedException();
        }

        public async Task<GetFactGmvMaxProductDataResponse> GetListWithPermissionsAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            try
            {
                // Check permissions
                var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // If no permissions, throw authorization exception
                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    throw new AbpAuthorizationException("You don't have permission to view GMV Max Product data");
                }

                // Get data using existing method
                var result = await GetListAsync(fromDate, toDate, currency, hasViewAllAdvertisers);

                // Filter data based on permissions
                if (hasViewAll || hasViewAllAdvertisers)
                {
                    // ViewAll or ViewAllAdvertisers - return all data as is
                    Logger.LogInformation($"User has ViewAll or ViewAllAdvertisers permission - returning all {result.FactGmvMaxProducts.Count} records");
                    return result;
                }

                // Filter fields based on permissions
                var allowedFields = PermissionFieldHelper.GetAllowedFields(hasViewSpending, hasViewMetrics, hasViewAll);
                
                // Create filtered DTOs
                var filteredProducts = new List<FactGmvMaxProductDto>();
                foreach (var product in result.FactGmvMaxProducts)
                {
                    var filteredProduct = FilterProductDtoByPermissions(product, allowedFields);
                    filteredProducts.Add(filteredProduct);
                }

                result.FactGmvMaxProducts = filteredProducts;

                Logger.LogInformation($"Filtered GMV Max Product data based on permissions - returning {filteredProducts.Count} records with {allowedFields.Count} allowed fields");

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Product data with permissions from {From} to {To}", fromDate, toDate);
                throw new ValidationException($"Error retrieving GMV Max Product data with permissions: {ex.Message}");
            }
        }

        private FactGmvMaxProductDto FilterProductDtoByPermissions(FactGmvMaxProductDto product, HashSet<string> allowedFields)
        {
            // Clone the original product DTO
            var filteredProduct = new FactGmvMaxProductDto();
            
            // Use reflection to copy only allowed fields
            var productType = typeof(FactGmvMaxProductDto);
            var properties = productType.GetProperties();
            
            foreach (var property in properties)
            {
                var propertyName = property.Name;
                
                // Copy field if it's allowed
                if (PermissionFieldHelper.IsFieldAllowed(propertyName, allowedFields))
                {
                    var value = property.GetValue(product);
                    property.SetValue(filteredProduct, value);
                }
            }
            
            return filteredProduct;
        }

        // ✅ Dashboard Data Filtering Methods
        private GmvMaxProductDashboardDto FilterDashboardDataByPermissions(GmvMaxProductDashboardDto data, bool hasViewSpending, bool hasViewMetrics, bool hasViewAll)
        {
            if (hasViewAll) return data;

            // Create a copy to avoid modifying original data
            var filteredData = new GmvMaxProductDashboardDto
            {
                CurrentMonth = data.CurrentMonth,
                LastMonth = data.LastMonth,
                WeeklyData = data.WeeklyData,
                MonthlyData = data.MonthlyData,
                CurrentWeekShopRanking = data.CurrentWeekShopRanking,
                TwoWeeksAgoShopRanking = data.TwoWeeksAgoShopRanking,
                OneWeekAgoShopRanking = data.OneWeekAgoShopRanking,
                CurrentWeekShopCostRanking = data.CurrentWeekShopCostRanking,
                TwoWeeksAgoShopCostRanking = data.TwoWeeksAgoShopCostRanking,
                OneWeekAgoShopCostRanking = data.OneWeekAgoShopCostRanking
            };

            // Filter based on permissions
            if (!hasViewSpending)
            {
                // Remove cost-related data
                filteredData.CurrentWeekShopCostRanking = new List<DashboardShopCostRanking>();
                filteredData.TwoWeeksAgoShopCostRanking = new List<DashboardShopCostRanking>();
                filteredData.OneWeekAgoShopCostRanking = new List<DashboardShopCostRanking>();
            }

            if (!hasViewMetrics)
            {
                // Remove metrics-related data
                filteredData.CurrentWeekShopRanking = new List<DashboardShopRanking>();
                filteredData.TwoWeeksAgoShopRanking = new List<DashboardShopRanking>();
                filteredData.OneWeekAgoShopRanking = new List<DashboardShopRanking>();
            }

            return filteredData;
        }

        private DashboardSummaryDto FilterDashboardSummaryByPermissions(DashboardSummaryDto summaryData, bool hasViewSpending, bool hasViewMetrics, bool hasViewAll)
        {
            if (hasViewAll) return summaryData;

            // Create filtered summary
            var filteredSummary = new DashboardSummaryDto
            {
                Month = summaryData.Month,
                Year = summaryData.Year,
                MonthName = summaryData.MonthName,
                FromDate = summaryData.FromDate,
                ToDate = summaryData.ToDate
            };

            // Check if spending fields should be visible
            if (hasViewSpending || hasViewAll)
            {
                filteredSummary.TotalCost = summaryData.TotalCost;
                filteredSummary.TotalNetCost = summaryData.TotalNetCost;
            }

            // Check if metrics fields should be visible
            if (hasViewMetrics || hasViewAll)
            {
                filteredSummary.TotalGrossRevenue = summaryData.TotalGrossRevenue;
                filteredSummary.AverageROAS = summaryData.AverageROAS;
                filteredSummary.AverageTACOS = summaryData.AverageTACOS;
                filteredSummary.TotalOrders = summaryData.TotalOrders;
            }

            // Always include basic fields
            filteredSummary.ProductCount = summaryData.ProductCount;
            filteredSummary.ActiveStores = summaryData.ActiveStores;
            filteredSummary.ActiveAdvertisers = summaryData.ActiveAdvertisers;
            filteredSummary.DataPointCount = summaryData.DataPointCount;

            return filteredSummary;
        }

        private object FilterDetailedAnalysisByPermissions(object detailedData, bool hasViewSpending, bool hasViewMetrics, bool hasViewAll)
        {
            if (hasViewAll) return detailedData;

            // For detailed analysis, we might need to filter arrays of objects
            // This is a simplified implementation - you may need to adjust based on actual data structure
            return detailedData;
        }

        // ✅ NEW: Section-specific methods for independent loading
        public async Task<SummaryCardsDto> GetSummaryCardsAsync(string? currency = "USD")
        {
            // ✅ Check permissions for summary cards access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập summary cards GMV Max Product");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Product summary cards data with currency: {Currency}", currency);
                var summaryCards = await _factGmvMaxProductDapperRepository.GetSummaryCardsAsync(currency, allowedAdvertiserIds);
                
                Logger.LogInformation("Successfully loaded GMV Max Product summary cards data");
                return summaryCards;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Product summary cards data");
                throw new ValidationException($"Error retrieving summary cards data: {ex.Message}");
            }
        }

        public async Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD")
        {
            // ✅ Check permissions for overview access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập overview GMV Max Product");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Product overview section data with currency: {Currency}", currency);
                var overview = await _factGmvMaxProductDapperRepository.GetOverviewSectionAsync(currency, allowedAdvertiserIds);
                
                Logger.LogInformation("Successfully loaded GMV Max Product overview section data");
                return overview;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Product overview section data");
                throw new ValidationException($"Error retrieving overview section data: {ex.Message}");
            }
        }

        public async Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD")
        {
            // ✅ Check permissions for charts access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập charts GMV Max Product");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Product charts data with currency: {Currency}", currency);
                var chartsData = await _factGmvMaxProductDapperRepository.GetChartsDataAsync(currency, allowedAdvertiserIds);
                
                Logger.LogInformation("Successfully loaded GMV Max Product charts data");
                return chartsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Product charts data");
                throw new ValidationException($"Error retrieving charts data: {ex.Message}");
            }
        }

        public async Task<DetailedChartsDto> GetDetailedChartsAsync()
        {
            // ✅ Check permissions for detailed charts access
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập detailed charts GMV Max Product");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Product detailed charts data for last 7 days");
                var detailedCharts = await _factGmvMaxProductDapperRepository.GetDetailedChartsAsync(allowedAdvertiserIds);
                
                Logger.LogInformation("Successfully loaded GMV Max Product detailed charts data");
                return detailedCharts;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Product detailed charts data");
                throw new ValidationException($"Error retrieving detailed charts data: {ex.Message}");
            }
        }

        public async Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD")
        {
            // ✅ Check permissions for rankings access - requires ViewAll or ViewAllAdvertisers
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập rankings GMV Max Product");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Product rankings data with currency: {Currency}", currency);
                var rankingsData = await _factGmvMaxProductDapperRepository.GetRankingsDataAsync(currency, allowedAdvertiserIds);
                
                Logger.LogInformation("Successfully loaded GMV Max Product rankings data");
                return rankingsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Product rankings data");
                throw new ValidationException($"Error retrieving rankings data: {ex.Message}");
            }
        }

    }
}


