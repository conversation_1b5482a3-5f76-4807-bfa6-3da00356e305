﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TikTok.Migrations
{
    /// <inheritdoc />
    public partial class RemoveAdvertiserIdIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AdAccountSupporters_AbpUsers_SupporterId",
                table: "AdAccountSupporters");

            migrationBuilder.DropForeignKey(
                name: "FK_AdAccountSupporters_Raw_RawAdAccounts_AdvertiserId",
                table: "AdAccountSupporters");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Raw_RawAdAccounts_AdvertiserId",
                table: "Raw_RawAdAccounts");

            migrationBuilder.DropIndex(
                name: "IX_AdAccountSupporters_SupporterId",
                table: "AdAccountSupporters");

            migrationBuilder.AddColumn<Guid>(
                name: "RawAdAccountEntityId",
                table: "AdAccountSupporters",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AdAccountSupporters_RawAdAccountEntityId",
                table: "AdAccountSupporters",
                column: "RawAdAccountEntityId");

            migrationBuilder.AddForeignKey(
                name: "FK_AdAccountSupporters_Raw_RawAdAccounts_RawAdAccountEntityId",
                table: "AdAccountSupporters",
                column: "RawAdAccountEntityId",
                principalTable: "Raw_RawAdAccounts",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AdAccountSupporters_Raw_RawAdAccounts_RawAdAccountEntityId",
                table: "AdAccountSupporters");

            migrationBuilder.DropIndex(
                name: "IX_AdAccountSupporters_RawAdAccountEntityId",
                table: "AdAccountSupporters");

            migrationBuilder.DropColumn(
                name: "RawAdAccountEntityId",
                table: "AdAccountSupporters");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Raw_RawAdAccounts_AdvertiserId",
                table: "Raw_RawAdAccounts",
                column: "AdvertiserId");

            migrationBuilder.CreateIndex(
                name: "IX_AdAccountSupporters_SupporterId",
                table: "AdAccountSupporters",
                column: "SupporterId");

            migrationBuilder.AddForeignKey(
                name: "FK_AdAccountSupporters_AbpUsers_SupporterId",
                table: "AdAccountSupporters",
                column: "SupporterId",
                principalTable: "AbpUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AdAccountSupporters_Raw_RawAdAccounts_AdvertiserId",
                table: "AdAccountSupporters",
                column: "AdvertiserId",
                principalTable: "Raw_RawAdAccounts",
                principalColumn: "AdvertiserId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
