/**
 * 🎯 Overview Section - Đơn gi<PERSON>n
 * Tự call API + tự render
 */

class OverviewSection {
    constructor() {
        this.container = '#overview-container';
        this.initialized = false;
    }

    /**
     * 🎯 Khởi tạo section
     */
    async init() {
        if (this.initialized) {
            return;
        }

        try {
            await this.loadData();
            this.initialized = true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 🎯 Load data từ API
     */
    async loadData() {
        try {
            // Show loading
            this.showLoading();

            // Call only overview API with currency - charts will be handled by ChartsSection
            const currency = localStorage.getItem('tiktok_currency') || 'USD';
            const overviewResponse = await fetch(
                `/api/fact-gmv-max-product/overview?currency=${currency}`
            );

            if (!overviewResponse.ok) {
                throw new Error(
                    `Overview API failed: ${overviewResponse.status} ${overviewResponse.statusText}`
                );
            }

            const overviewData = await overviewResponse.json();

            // Use overview data directly - weeklyData will be empty initially
            const mergedData = {
                ...overviewData,
                weeklyData: overviewData.weeklyData || [],
            };

            // Render data
            await this.render(mergedData);
        } catch (error) {
            this.showError(error);
            throw error;
        }
    }

    /**
     * 🎯 Render overview data
     */
    async render(data) {
        const container = document.querySelector(this.container);
        if (!container) {
            return;
        }

        const formatCurrency = (value) => {
            if (
                window.sharedCurrencyManager &&
                window.sharedCurrencyManager.formatCurrency
            ) {
                return window.sharedCurrencyManager.formatCurrency(
                    value || 0,
                    'USD'
                );
            }
            if (!value) return 'USD' === 'VND' ? '₫0' : '$0';
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
            }).format(value);
        };

        // Fallback data for testing
        const currentMonthData = data.currentMonth || {
            totalCost: 0,
            year: new Date().getFullYear(),
            month: new Date().getMonth() + 1,
        };
        const lastMonthData = data.lastMonth || {
            totalCost: 0,
            year: new Date().getFullYear(),
            month: new Date().getMonth(),
        };

        const monthlyData = {
            thisMonth: currentMonthData.totalCost || 0,
            lastMonth: lastMonthData.totalCost || 0,
            change:
                (lastMonthData.totalCost || 0) > 0
                    ? (((currentMonthData.totalCost || 0) -
                          (lastMonthData.totalCost || 0)) /
                          (lastMonthData.totalCost || 0)) *
                      100
                    : 0,
        };

        // Helper functions
        const getVietnameseMonthName = (monthIndex) => {
            const months = [
                'Tháng 1',
                'Tháng 2',
                'Tháng 3',
                'Tháng 4',
                'Tháng 5',
                'Tháng 6',
                'Tháng 7',
                'Tháng 8',
                'Tháng 9',
                'Tháng 10',
                'Tháng 11',
                'Tháng 12',
            ];
            return months[monthIndex] || 'Tháng không xác định';
        };

        const getWeeklyDataForMonth = (weeklyData, year, month) => {
            if (!weeklyData || !year || !month) {
                return [
                    { label: 'Tuần 1', value: 0 },
                    { label: 'Tuần 2', value: 0 },
                    { label: 'Tuần 3', value: 0 },
                    { label: 'Tuần 4', value: 0 },
                ];
            }

            const weeks = [0, 0, 0, 0];
            const filteredData = weeklyData.filter((w) => {
                return w.year === year && w.month === month;
            });

            filteredData.forEach((w) => {
                const weekIndex = Math.min(w.week - 1, 3);
                weeks[weekIndex] += w.totalCost || 0;
            });

            return weeks.map((value, index) => ({
                label: `Tuần ${index + 1}`,
                value: value,
            }));
        };

        const now = new Date();
        const currentMonthName = getVietnameseMonthName(now.getMonth());
        const lastMonthDate = new Date(
            now.getFullYear(),
            now.getMonth() - 1,
            1
        );
        const lastMonthName = getVietnameseMonthName(lastMonthDate.getMonth());

        // Tạo dữ liệu tuần cho tháng hiện tại và tháng trước
        const weeklyData = data.weeklyData || data.WeeklyData || [];

        const currentMonthWeeks = getWeeklyDataForMonth(
            weeklyData,
            currentMonthData.year,
            currentMonthData.month
        );
        const lastMonthWeeks = getWeeklyDataForMonth(
            weeklyData,
            lastMonthData.year,
            lastMonthData.month
        );

        container.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="summary-card monthly-card current-month mb-3">
                        <h3>Tổng chi tiêu GMV Max Product - ${currentMonthName}</h3>
                        <p class="value">${formatCurrency(
                            monthlyData.thisMonth
                        )}</p>
                        <p class="change ${
                            monthlyData.change >= 0 ? 'positive' : 'negative'
                        }">
                            ${
                                monthlyData.change > 0 ? '+' : ''
                            }${monthlyData.change.toFixed(
            1
        )}% so với ${lastMonthName}
                        </p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="summary-card monthly-card last-month mb-3">
                        <h3>Tổng chi tiêu GMV Max Product - ${lastMonthName}</h3>
                        <p class="value">${formatCurrency(
                            monthlyData.lastMonth
                        )}</p>
                        <p class="change neutral">Tham khảo</p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary mb-3"><i class="fas fa-calendar-week"></i> GMV Max Product các tuần - ${currentMonthName}</h6>
                    <div class="row">
                        ${currentMonthWeeks
                            .map(
                                (week, index) => `
                                    <div class="col-6 mb-2">
                                        <div class="summary-card weekly-card current-month-week">
                                            <h4>${week.label}</h4>
                                            <p class="value">${formatCurrency(
                                                week.value
                                            )}</p>
                                            <p class="change neutral">${currentMonthName}</p>
                                        </div>
                                    </div>
                                `
                            )
                            .join('')}
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="text-warning mb-3"><i class="fas fa-calendar-week"></i> GMV Max Product các tuần - ${lastMonthName}</h6>
                    <div class="row">
                        ${lastMonthWeeks
                            .map(
                                (week, index) => `
                                    <div class="col-6 mb-2">
                                        <div class="summary-card weekly-card last-month-week">
                                            <h4>${week.label}</h4>
                                            <p class="value">${formatCurrency(
                                                week.value
                                            )}</p>
                                            <p class="change neutral">${lastMonthName}</p>
                                        </div>
                                    </div>
                                `
                            )
                            .join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 🎯 Show loading indicator
     */
    showLoading() {
        const container = document.querySelector(this.container);
        if (container) {
            container.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải dữ liệu tổng quan...</p>
                </div>
            `;
        }
    }

    /**
     * 🎯 Show error message
     */
    showError(error) {
        const container = document.querySelector(this.container);
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Lỗi tải Overview</h6>
                    <p class="mb-0">${error.message}</p>
                </div>
            `;
        }
    }

    /**
     * 🎯 Format currency
     */
    formatCurrency(amount) {
        if (!amount) return '$0';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    }

    /**
     * 🎯 Format number
     */
    formatNumber(number) {
        return new Intl.NumberFormat('vi-VN').format(number);
    }

    /**
     * 🎯 Refresh data
     */
    async refresh() {
        await this.loadData();
    }
}

// Export for global use
window.OverviewSection = OverviewSection;
