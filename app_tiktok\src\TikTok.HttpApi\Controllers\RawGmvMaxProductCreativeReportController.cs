using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TikTok.Controllers;
using TikTok.Permissions;
using TikTok.RawGmvMaxProductCreativeReports;
using Volo.Abp.Application.Dtos;

namespace TikTok.HttpApi.Controllers
{
    /// <summary>
    /// Controller cho RawGmvMaxProductCreativeReport
    /// </summary>
    [Route("api/raw-gmv-max-product-creative-report")]
    [Authorize(TikTokPermissions.FactGmvMax.ViewVideo)]
    public class RawGmvMaxProductCreativeReportController : TikTokController
    {
        private readonly IRawGmvMaxProductCreativeReportAppService _appService;

        public RawGmvMaxProductCreativeReportController(
            IRawGmvMaxProductCreativeReportAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// L<PERSON>y danh sách RawGmvMaxProductCreativeReport với phân trang và lọc
        /// </summary>
        /// <param name="input">Thông tin phân trang và lọc</param>
        /// <returns>Danh sách DTO với thông tin phân trang</returns>
        [HttpGet]
        public async Task<PagedResultDto<RawGmvMaxProductCreativeReportDto>> GetListAsync(
            [FromQuery] GetRawGmvMaxProductCreativeReportListDto input)
        {
            return await _appService.GetListAsync(input);
        }

        /// <summary>
        /// Lấy thông tin chi tiết RawGmvMaxProductCreativeReport theo ID
        /// </summary>
        /// <param name="id">ID của entity</param>
        /// <returns>DTO hoặc null nếu không tìm thấy</returns>
        [HttpGet("{id}")]
        public async Task<RawGmvMaxProductCreativeReportDto?> GetAsync(Guid id)
        {
            return await _appService.GetAsync(id);
        }

        /// <summary>
        /// Lấy danh sách Campaign IDs để sử dụng trong dropdown filter
        /// </summary>
        /// <returns>Danh sách Campaign IDs</returns>
        [HttpGet("campaign-ids")]
        public async Task<List<string>> GetCampaignIdsAsync()
        {
            return await _appService.GetCampaignIdsAsync();
        }

        /// <summary>
        /// Lấy thống kê tổng hợp video theo trạng thái (cho video status cards)
        /// </summary>
        /// <param name="input">Thông tin lọc</param>
        /// <returns>Thống kê video theo trạng thái</returns>
        [HttpGet("video-status-statistics")]
        public async Task<VideoStatusStatisticsDto> GetVideoStatusStatisticsAsync(
            [FromQuery] GetRawGmvMaxProductCreativeReportListDto input)
        {
            return await _appService.GetVideoStatusStatisticsAsync(input);
        }

        /// <summary>
        /// Lấy danh sách video cần xử lý (chỉ các trạng thái problematic)
        /// </summary>
        /// <param name="input">Thông tin phân trang và lọc</param>
        /// <returns>Danh sách video cần xử lý với phân trang</returns>
        [HttpGet("problematic-videos")]
        public async Task<PagedResultDto<RawGmvMaxProductCreativeReportDto>> GetProblematicVideosAsync(
            [FromQuery] GetRawGmvMaxProductCreativeReportListDto input)
        {
            return await _appService.GetProblematicVideosAsync(input);
        }
    }
}
