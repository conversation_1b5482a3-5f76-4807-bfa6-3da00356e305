using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using TikTok.BusinessApplications;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;
using Tsp.Zalo.IService;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using static TikTok.Permissions.TikTokPermissions;

namespace TikTok.BusinessApplications
{
    /// <summary>
    /// Service implementation cho ứng dụng Business
    /// </summary>
    public class BusinessApplicationAppService :
        CrudAppService<
            BusinessApplicationEntity,
            BusinessApplicationDto,
            Guid,
            GetBusinessApplicationListDto,
            CreateBusinessApplicationDto,
            UpdateBusinessApplicationDto>,
        IBusinessApplicationAppService
    {
        private readonly IBusinessApplicationRepository _businessApplicationRepository;
        private readonly IConfiguration _configuration;
        private readonly ILogger<BusinessApplicationAppService> _logger;
        private readonly IJobRepository _jobRepository;
        private readonly ISyncRawToFactBusinessCenterService _syncRawToFactBusinessCenterService;
        private readonly ICookieEncryptionService _cookieEncryptionService;
        private readonly IAdAccountSyncService _adAccountSyncService;  
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="businessApplicationRepository">Business Application Repository</param>
        /// <param name="configuration">Configuration</param>
        /// <param name="logger">Logger</param>
        /// <param name="jobRepository">Job Repository</param>
        /// <param name="syncRawToFactBusinessCenterService">Sync Raw To Fact Business Center Service</param>
        /// <param name="adAccountSyncService">Ad Account Sync Service</param>
        public BusinessApplicationAppService(
            IRepository<BusinessApplicationEntity, Guid> repository,
            IBusinessApplicationRepository businessApplicationRepository,
            IConfiguration configuration,
            ILogger<BusinessApplicationAppService> logger,
            IJobRepository jobRepository,
            ISyncRawToFactBusinessCenterService syncRawToFactBusinessCenterService,
            ICookieEncryptionService cookieEncryptionService,
            IAdAccountSyncService adAccountSyncService) : base(repository)
        {
            _businessApplicationRepository = businessApplicationRepository;
            _configuration = configuration;
            _logger = logger;
            _jobRepository = jobRepository;
            _syncRawToFactBusinessCenterService = syncRawToFactBusinessCenterService;
            _cookieEncryptionService = cookieEncryptionService;
            _adAccountSyncService = adAccountSyncService;
            CreatePolicyName = TikTokPermissions.BusinessApplications.Create;
            UpdatePolicyName = TikTokPermissions.BusinessApplications.Edit;
            DeletePolicyName = TikTokPermissions.BusinessApplications.Delete;
            GetPolicyName = TikTokPermissions.BusinessApplications.Default;
            GetListPolicyName = TikTokPermissions.BusinessApplications.Default;
        }

        /// <summary>
        /// Lấy ứng dụng Business theo Application ID
        /// </summary>
        /// <param name="applicationId">ID của ứng dụng</param>
        /// <returns>Ứng dụng Business</returns>
        [Authorize(TikTokPermissions.BusinessApplications.Default)]
        public async Task<BusinessApplicationDto> GetByApplicationIdAsync(string applicationId)
        {
            var entity = await _businessApplicationRepository.GetByApplicationIdAsync(applicationId);
            return ObjectMapper.Map<BusinessApplicationEntity, BusinessApplicationDto>(entity);
        }

        /// <summary>
        /// Lấy danh sách ứng dụng Business theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách ứng dụng Business</returns>
        [Authorize(TikTokPermissions.BusinessApplications.Default)]
        public async Task<BusinessApplicationDto> GetByBcIdAsync(string bcId)
        {
            var entity = await _businessApplicationRepository.GetByBcIdAsync(bcId);
            var dtos = ObjectMapper.Map<BusinessApplicationEntity, BusinessApplicationDto>(entity);
            return dtos;
        }

        /// <summary>
        /// Lấy redirect URI được cấu hình cho TikTok authorization
        /// </summary>
        /// <returns>Redirect URI</returns>
        public async Task<RedirectUriResponseDto> GetRedirectUriAsync()
        {
            var redirectUri = _configuration["TikTok:Auth:RedirectUri"];
            if (string.IsNullOrEmpty(redirectUri))
            {
                throw new UserFriendlyException("TikTok redirect URI chưa được cấu hình");
            }

            return await Task.FromResult(new RedirectUriResponseDto
            {
                RedirectUri = redirectUri
            });
        }

        /// <summary>
        /// Xử lý callback từ TikTok sau khi user authorize
        /// </summary>
        /// <param name="request">Thông tin callback request</param>
        /// <returns>Kết quả xử lý callback</returns>
        public async Task<TikTokCallbackResponseDto> ProcessTikTokCallbackAsync(TikTokCallbackRequestDto request)
        {
            try
            {
                _logger.LogDebug("Bắt đầu xử lý TikTok callback cho BusinessApplicationId: {BusinessApplicationId}", request.BusinessApplicationId);

                // 1. Validate input
                if (!Guid.TryParse(request.BusinessApplicationId, out var businessApplicationId))
                {
                    return new TikTokCallbackResponseDto
                    {
                        IsSuccess = false,
                        ErrorMessage = "Business Application ID không hợp lệ"
                    };
                }

                // 2. Lấy Business Application từ database
                var businessApplication = await Repository.GetAsync(businessApplicationId);
                if (businessApplication == null)
                {
                    return new TikTokCallbackResponseDto
                    {
                        IsSuccess = false,
                        ErrorMessage = "Không tìm thấy Business Application"
                    };
                }

                // 3. Tạo TikTok client để gọi API lấy access token
                var tikTokClient = new TikTokBusinessApiClient();

                // 4. Gọi API để lấy long-term access token
                var tokenRequest = new LongTermAccessTokenRequest
                {
                    AppId = businessApplication.ApplicationId,
                    Secret = businessApplication.Secret,
                    AuthCode = request.AuthCode
                };

                var tokenResponse = await tikTokClient.Authentication.GetLongTermAccessTokenAsync(tokenRequest);

                if (tokenResponse?.Data?.AccessToken == null)
                {
                    return new TikTokCallbackResponseDto
                    {
                        IsSuccess = false,
                        ErrorMessage = "Không thể lấy access token từ TikTok"
                    };
                }

                // 5. Cập nhật access token vào database
                businessApplication.AccessToken = await _cookieEncryptionService.EncryptAsync(tokenResponse.Data.AccessToken);
                businessApplication.AccessTokenCreatedAt = DateTime.UtcNow;

                await Repository.UpdateAsync(businessApplication);

                _logger.LogDebug("Đã cập nhật access token thành công cho BusinessApplicationId: {BusinessApplicationId}", request.BusinessApplicationId);

                return new TikTokCallbackResponseDto
                {
                    IsSuccess = true,
                    AccessTokenCreatedAt = businessApplication.AccessTokenCreatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xử lý TikTok callback cho BusinessApplicationId: {BusinessApplicationId}", request.BusinessApplicationId);
                return new TikTokCallbackResponseDto
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách ứng dụng Business</returns>
        public override async Task<PagedResultDto<BusinessApplicationDto>> GetListAsync(GetBusinessApplicationListDto input)
        {
            // Sử dụng repository để lấy danh sách và tổng số
            var entities = await _businessApplicationRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.Filter,
                applicationId: input.ApplicationId,
                bcId: input.BcId,
                bcName: input.BcName,
                comment: input.Comment,
                isActive: input.IsActive,
                status: input.Status,
                accessTokenCreatedAtFrom: input.AccessTokenCreatedAtFrom,
                accessTokenCreatedAtTo: input.AccessTokenCreatedAtTo);

            var totalCount = await _businessApplicationRepository.GetCountAsync(
                filter: input.Filter,
                applicationId: input.ApplicationId,
                bcId: input.BcId,
                bcName: input.BcName,
                comment: input.Comment,
                isActive: input.IsActive,
                status: input.Status,
                accessTokenCreatedAtFrom: input.AccessTokenCreatedAtFrom,
                accessTokenCreatedAtTo: input.AccessTokenCreatedAtTo);

            var dtos = ObjectMapper.Map<BusinessApplicationEntity[], BusinessApplicationDto[]>(entities.ToArray());

            return new PagedResultDto<BusinessApplicationDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        /// <summary>
        /// Tạo job cho Business Application với CommandType cụ thể
        /// </summary>
        /// <param name="businessApplicationId">ID của Business Application</param>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Kết quả tạo job</returns>
        [Authorize(TikTokPermissions.BusinessApplications.Edit)]
        public async Task<bool> CreateJobAsync(Guid businessApplicationId, CommandType commandType)
        {
            try
            {
                // Lấy thông tin Business Application
                var businessApplication = await _businessApplicationRepository.GetAsync(businessApplicationId);
                if (businessApplication == null)
                {
                    throw new UserFriendlyException("Business Application not found");
                }

                // Kiểm tra xem đã có job đang chờ hoặc đang xử lý cho CommandType này chưa
                var existingJobs = await _jobRepository.GetByBusinessApplicationAsync(
                    businessApplicationId, 
                    commandType, 
                    new System.Collections.Generic.List<JobStatus> { JobStatus.Pending, JobStatus.InProcess });
                
                if (existingJobs.Count > 0)
                {
                    _logger.LogWarning("Đã có job {CommandType} đang hoạt động cho BusinessApplication {BusinessApplicationId}", 
                        commandType, businessApplicationId);
                    return false;
                }

                // Tạo job mới
                var job = new JobEntity
                {
                    CommandType = commandType,
                    Status = JobStatus.Pending,
                    BusinessApplicationId = businessApplicationId,
                    Priority = 1, // Priority mặc định
                    Notes = $"Manual job {commandType} for {businessApplication.ApplicationId}",
                    Parameters = JsonSerializer.Serialize(new { BcId = businessApplication.BcId, SyncDate = DateTime.Now })
                };

                await _jobRepository.InsertAsync(job);

                _logger.LogInformation("Created manual job {CommandType} for BusinessApplication {BusinessApplicationId}", 
                    commandType, businessApplicationId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating job {CommandType} for BusinessApplication {BusinessApplicationId}", 
                    commandType, businessApplicationId);
                throw;
            }
        }

        /// <summary>
        /// Đồng bộ dữ liệu từ Raw sang Fact cho Business Center cụ thể
        /// </summary>
        /// <param name="businessApplicationId">ID của Business Application</param>
        /// <returns>Kết quả đồng bộ</returns>
        [Authorize(TikTokPermissions.BusinessApplications.Edit)]
        public async Task<BusinessCenterSyncResult> SyncRawToFactAsync(Guid businessApplicationId)
        {
            try
            {
                // Lấy thông tin Business Application
                var businessApplication = await _businessApplicationRepository.GetAsync(businessApplicationId);
                if (businessApplication == null)
                {
                    throw new UserFriendlyException("Business Application not found");
                }

                _logger.LogInformation("Starting Raw to Fact sync for Business Application {BusinessApplicationId} with BC ID {BcId}", 
                    businessApplicationId, businessApplication.BcId);

                // Gọi sync service với BC ID cụ thể
                var result = await _syncRawToFactBusinessCenterService.SyncAllAsync(businessApplication.BcId);

                _logger.LogInformation("Completed Raw to Fact sync for Business Application {BusinessApplicationId}. " +
                    "BC Count: {BusinessCenterCount}, AdAccount Count: {AdAccountCount}, New: {NewRecords}, Updated: {UpdatedRecords}", 
                    businessApplicationId, result.BusinessCenterCount, result.AdAccountCount, result.NewRecords, result.UpdatedRecords);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing Raw to Fact for BusinessApplication {BusinessApplicationId}", 
                    businessApplicationId);
                throw;
            }
        }

        /// <summary>
        /// Tạo job đồng bộ lùi cho Business Center với khoảng thời gian và các loại lệnh cụ thể
        /// </summary>
        /// <param name="request">Thông tin cài đặt đồng bộ lùi</param>
        /// <returns>Kết quả tạo job đồng bộ lùi</returns>
        public async Task<SyncBackResponseDto> CreateSyncBackJobsAsync(SyncBackRequestDto request)
        {
            try
            {
                _logger.LogInformation("Starting sync back jobs creation for Business Center {BcId} from {StartDate} to {EndDate} with {CommandTypeCount} command types", 
                    request.BcId, request.StartDate, request.EndDate, request.CommandTypes.Count);

                // Validate input
                if (request.StartDate > request.EndDate)
                {
                    throw new UserFriendlyException("Start date must be before end date");
                }

                if (request.CommandTypes == null || request.CommandTypes.Count == 0)
                {
                    throw new UserFriendlyException("At least one command type must be selected");
                }

                // Lấy thông tin Business Application theo BcId
                var businessApplication = await _businessApplicationRepository.GetByBcIdAsync(request.BcId);
                if (businessApplication == null)
                {
                    throw new UserFriendlyException("Business Application not found for the specified Business Center");
                }

                var response = new SyncBackResponseDto
                {
                    BcId = request.BcId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    CreatedCommandTypes = new List<CommandType>(),
                    TotalJobsCreated = 0,
                    IsSuccess = false,
                    Message = "Sync back jobs creation completed"
                };
                var jobs = new List<JobEntity>();
                // Tạo job cho từng CommandType
                foreach (var commandType in request.CommandTypes)
                {
                    try
                    {
                        // Kiểm tra xem đã có job đang chờ hoặc đang xử lý cho CommandType này chưa
                        var existingJobs = await _jobRepository.GetByBusinessApplicationAsync(
                            businessApplication.Id, 
                            commandType, 
                            new System.Collections.Generic.List<JobStatus> { JobStatus.Pending, JobStatus.InProcess });
                        
                        if (existingJobs.Count > 0)
                        {
                            _logger.LogWarning("Job {CommandType} already exists for Business Center {BcId}, skipping", 
                                commandType, request.BcId);
                            continue;
                        }

                        // TODO: Tạo job với thông tin ngày bắt đầu và kết thúc
                        // Hiện tại chỉ tạo job đơn giản như method CreateJobAsync hiện tại
                        // Trong tương lai cần mở rộng để hỗ trợ date range
                        var job = new JobEntity
                        {
                            CommandType = commandType,
                            Status = JobStatus.Pending,
                            BusinessApplicationId = businessApplication.Id,
                            Priority = 1, // Priority mặc định
                            Notes = $"Manual job {commandType} for {businessApplication.ApplicationId}",
                            Parameters = JsonSerializer.Serialize(new { BcId = businessApplication.BcId, SyncDate = DateTime.Now,
                                StartDate = request.StartDate, EndDate = request.EndDate })
                        };
                        jobs.Add(job);
                        _logger.LogInformation("Creating sync back job for CommandType {CommandType} from {StartDate} to {EndDate}", 
                            commandType, request.StartDate, request.EndDate);
                        
                        // Thêm vào danh sách đã tạo
                        response.CreatedCommandTypes.Add(commandType);
                        response.TotalJobsCreated++;
                        
                        _logger.LogInformation("Successfully created sync back job for CommandType {CommandType}", commandType);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error creating sync back job for CommandType {CommandType}", commandType);
                        // Tiếp tục với các CommandType khác
                    }
                }
                if (!jobs.IsNullOrEmpty())
                {
                    await _jobRepository.InsertManyAsync(jobs);
                }
                response.IsSuccess = response.TotalJobsCreated > 0;
                response.Message = response.IsSuccess 
                    ? $"Successfully created {response.TotalJobsCreated} sync back jobs"
                    : "No sync back jobs were created";

                _logger.LogInformation("Completed sync back jobs creation for Business Center {BcId}. Created: {TotalJobsCreated} jobs", 
                    request.BcId, response.TotalJobsCreated);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sync back jobs for Business Center {BcId}", 
                    request.BcId);
                throw;
            }
        }

        /// <summary>
        /// Cập nhật danh sách tài khoản quảng cáo từ tài sản cho Business Application
        /// </summary>
        /// <param name="businessApplicationId">ID của Business Application</param>
        /// <returns>Kết quả cập nhật</returns>
        public async Task<AdAccountSyncResult> UpdateAdAccountsFromAssetsAsync(Guid businessApplicationId)
        {
            try
            {
                // Lấy thông tin Business Application
                var businessApplication = await _businessApplicationRepository.GetAsync(businessApplicationId);
                if (businessApplication == null)
                {
                    throw new UserFriendlyException("Business Application not found");
                }

                _logger.LogInformation("Starting UpdateAdAccountsFromAssets for Business Application {BusinessApplicationId} with BC ID {BcId}", 
                    businessApplicationId, businessApplication.BcId);

                // Gọi AdAccountSyncService với BC ID
                var result = await _adAccountSyncService.UpdateAdAccountsFromAssetsAsync(businessApplication.BcId);

                _logger.LogInformation("Completed UpdateAdAccountsFromAssets for Business Application {BusinessApplicationId}. " +
                    "AdAccount Count: {AdAccountCount}, New: {NewRecords}, Updated: {UpdatedRecords}", 
                    businessApplicationId, result.AdAccountCount, result.NewRecords, result.UpdatedRecords);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating AdAccounts from Assets for BusinessApplication {BusinessApplicationId}", 
                    businessApplicationId);
                throw;
            }
        }
    }
}