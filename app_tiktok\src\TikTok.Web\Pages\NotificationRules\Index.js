$(function () {
    const l = abp.localization.getResource('TikTok');
    const currentUserId = abp.currentUser?.id;
    const currentLang = localStorage.getItem('lpx:lang') === 'EN' ? 'en-US' : 'vi-VN';

    // Filter variables
    let currentFilters = {
        ruleName: '',
        targetEntity: '',
        notificationFrequency: '',
        isDefault: ''
    };

    const dataTable = $('#RulesTable').DataTable(
        abp.libs.datatables.normalizeConfiguration({
            serverSide: true,
            paging: true,
            order: [[0, 'asc']],
            searching: true,
            scrollX: true,
            ajax: abp.libs.datatables.createAjax(function (input) {
                // Add custom filters to the input
                if (currentFilters.ruleName) {
                    input.ruleName = currentFilters.ruleName;
                }
                if (currentFilters.targetEntity !== '') {
                    input.targetEntity = currentFilters.targetEntity;
                }
                if (currentFilters.notificationFrequency !== '') {
                    input.notificationFrequency = currentFilters.notificationFrequency;
                }
                if (currentFilters.isDefault !== '') {
                    input.isDefault = currentFilters.isDefault;
                }


                return abp.ajax({
                    url: abp.appPath + 'api/app/rules',
                    type: 'GET',
                    data: input,
                });
            }),
            columnDefs: [
                {
                    title: 'Tên quy tắc',
                    data: 'ruleName',
                },
                {
                    title: 'Đối tượng',
                    data: 'targetEntity',
                    render: function (data) {
                        const entityMap = {
                            0: 'Mặc định',
                            1: 'Số dư',
                        };
                        return entityMap[data] || 'Không xác định';
                    },
                },
                {
                    title: 'Số điều kiện',
                    data: 'condition',
                    render: function (data) {
                        if (!data) return 0;
                        try {
                            const conditions = JSON.parse(data);
                            if (
                                conditions.rules &&
                                Array.isArray(conditions.rules)
                            ) {
                                return conditions.rules.length;
                            }
                            return 0;
                        } catch (e) {
                            return 0;
                        }
                    },
                },
                {
                    title: 'Tần suất thông báo',
                    data: 'notificationFrequency',
                    render: function (data) {
                        const frequencyMap = {
                            0: '1 lần duy nhất (once)',
                            1: '1 ngày 1 lần (daily)',
                            2: '1 giờ một lần (hourly)',
                            3: '2 giờ một lần (2hour)',
                            4: '30 phút/1 lần (30min)',
                            5: '10 phút/1 lần (10min)',
                            6: '5 phút/1 lần (5min)',
                        };
                        return frequencyMap[data] || 'Không xác định';
                    },
                },
                {
                    title: 'Mặc định',
                    data: 'isDefault',
                    render: function (data) {
                        return data
                            ? '<span class="badge bg-success">Có</span>'
                            : '<span class="badge bg-secondary">Không</span>';
                    },
                },
                {
                    title: 'Chủ sở hữu',
                    data: 'ownerName',
                    render: function (data) {
                        return data || 'Hệ thống';
                    },
                },
                {
                    title: 'Ngày tạo',
                    data: 'creationTime',
                    render: function (data) {
                        return moment(data).format('DD/MM/YYYY HH:mm');
                    },
                },
                {
                    title: 'Thao tác',
                    data: 'ownerId',
                    render: function (data, type, row) {
                        let container = '<div class="d-flex gap-1">';
                        let buttons = '<div class="btn-group" role="group">';
                        let canEditDelete = window.notificationRulePermission && (window.notificationRulePermission.canManageRule || (window.notificationRulePermission.canEdit && data === currentUserId));
                        // Edit button - only show if user has edit permission
                        if (canEditDelete) {
                            buttons += `<button type="button" class="btn btn-sm btn-outline-primary" onclick="openEditModal(${JSON.stringify(row).replace(/"/g, '&quot;')})">
                                <i class="fas fa-edit"></i> Chỉnh sửa
                            </button>`;
                        }

                        // Rule Consumers button - show if user has any notification rule permission
                        if (window.notificationRulePermission && window.notificationRulePermission.hasAnyPermission()) {
                            buttons += `<button type="button" class="btn btn-sm btn-outline-info" onclick="openRuleConsumersModal('${row.id}', '${row.ruleName.replace(/'/g, "\\'")}')">
                                <i class="fas fa-users"></i> Đối tượng sử dụng
                            </button>`;
                        }

                        // Delete button - only show if user has delete permission
                        if (canEditDelete) {
                            buttons += `<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteRule('${row.id}', '${row.ruleName.replace(/'/g, "\\'")}')">
                                <i class="fas fa-trash"></i> Xóa
                            </button>`;
                        }

                        buttons += '</div>';
                        container += buttons;

                        let additionalDropdownButtons = '';
                        // Additional dropdown buttons (render only; behavior to be implemented later)
                        // - Make a Duplicate Rule
                        if (window.notificationRulePermission.canView) {
                            additionalDropdownButtons += `
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end">
                                        <a class="dropdown-item" href="javascript:void(0)" 
                                            data-action="duplicate-rule" data-rule-id="${row.id}">
                                            <i class="fas fa-copy"></i> Nhân bản
                                        </a>
                                    </div>
                                </div>`;
                        }

                        container += additionalDropdownButtons;
                        container += '</div>';
                        return container;
                    },
                },
            ],
        })
    );

    // Filter event handlers
    $('#filterRuleName').on('input', function () {
        currentFilters.ruleName = $(this).val();
        dataTable.ajax.reload();
    });

    $('#filterTargetEntity').on('change', function () {
        currentFilters.targetEntity = $(this).val();
        dataTable.ajax.reload();
    });

    $('#filterNotificationFrequency').on('change', function () {
        currentFilters.notificationFrequency = $(this).val();
        dataTable.ajax.reload();
    });

    $('#filterIsDefault').on('change', function () {
        currentFilters.isDefault = $(this).val();
        dataTable.ajax.reload();
    });



    // Clear all filters
    $('#clearFilters').on('click', function () {
        // Reset filter values
        $('#filterRuleName').val('');
        $('#filterTargetEntity').val('');
        $('#filterNotificationFrequency').val('');
        $('#filterIsDefault').val('');

        // Reset current filters object
        currentFilters = {
            ruleName: '',
            targetEntity: '',
            notificationFrequency: '',
            isDefault: ''
        };

        // Reload table
        dataTable.ajax.reload();
    });

    let queryBuilder = null;
    let editQueryBuilder = null;
    let currentEditRule = null;

    let notificationAccounts = [];
    let selectedNotificationAccounts = [];

    // Function to get notification accounts
    function getNotificationAccounts() {
        return abp.ajax({
            url: abp.appPath + 'api/app/rules/notification-accounts',
            type: 'GET',
        });
    }

    // Function to render notification accounts selection UI
    function renderNotificationAccountsSelection() {
        const container = $('#list-notification-accounts');
        if (!notificationAccounts || notificationAccounts.length === 0) {
            container.html(
                '<div class="alert alert-info">Không có tài khoản thông báo nào.</div>'
            );
            return;
        }

        let html = `
            <div class="mb-2 col-12 col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control form-control-sm" id="create-notification-accounts-search" 
                           placeholder="Tìm kiếm theo tên hoặc số điện thoại..." 
                           autocomplete="off">
                    <button class="btn btn-outline-secondary" type="button" id="create-clear-search">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="notification-accounts-grid" id="create-notification-accounts-container">
        `;

        notificationAccounts.forEach((account) => {
            const isSelected = selectedNotificationAccounts.some(
                (selected) => selected.id === account.id
            );
            const displayName =
                account.displayName ||
                account.zaloName ||
                account.userName ||
                'Unknown';
            const avatarHtml = getAvatarHtml(account.avatarUrl, displayName);
            const mainBadge = account.isMain
                ? '<span class="badge bg-primary ms-1">Chính</span>'
                : '';

            html += `
                <div class="notification-account-item ${isSelected ? 'selected' : ''
                }" data-account-id="${account.id}" 
                    data-display-name="${displayName.toLowerCase()}" 
                    data-phone-number="${(account.phoneNumber || '').toLowerCase()}">
                    <div class="card overflow-hidden">
                        <img src="/images/logo/Zalo.png" alt="Zalo" class="img-fluid" style="width: 30px; height: 30px; object-fit: cover; position: absolute; bottom: 5px; right: 5px; z-index: 50;">

                        <div class="card-body">
                            <div class="row mx-0 text-center p-2">
                                <div class="d-flex p-0 w-100 mx-0 mb-1">
                                    <div class="avatar-container px-0" style="width: 50px; height: 50px; aspect-ratio: 1/1;">
                                        ${avatarHtml}
                                    </div>
                                    <h6 class="card-title pe-0 ps-1 d-inline-flex flex-column w-100">${displayName}${mainBadge}</h6>
                                </div>
                                <div class="px-1 min-w-100">
                                    <small class="fw-bold d-block">${account.phoneNumber || 'N/A'
                }</small>
                                </div>
                                <div class="form-check p-0">
                                    <input hidden class="form-check-input" type="checkbox" 
                                        id="account-${account.id}" 
                                        value="${account.id}" 
                                        ${isSelected ? 'checked' : ''}>
                                    <label data-label-for="${account.id}" class="form-check-label ${isSelected ? 'text-success fw-bold' : ''}" for="account-${account.id
                }">
                                        ${isSelected ? 'Đã chọn' : 'Chọn'}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.html(html);

        // Bind search functionality
        $('#create-notification-accounts-search').on('input', function () {
            filterCreateNotificationAccounts($(this).val().toLowerCase());
        });

        // Clear search button
        $('#create-clear-search').on('click', function () {
            $('#create-notification-accounts-search').val('').trigger('input');
        });

        // Bind checkbox events
        container.find('input[type="checkbox"]').on('change', function () {
            const accountId = $(this).val();
            const account = notificationAccounts.find(
                (acc) => acc.id === accountId
            );

            if ($(this).is(':checked')) {
                if (
                    !selectedNotificationAccounts.some(
                        (selected) => selected.id === accountId
                    )
                ) {
                    selectedNotificationAccounts.push(account);
                }
                $(this)
                    .closest('.notification-account-item')
                    .addClass('selected');
            } else {
                selectedNotificationAccounts =
                    selectedNotificationAccounts.filter(
                        (selected) => selected.id !== accountId
                    );
                $(this)
                    .closest('.notification-account-item')
                    .removeClass('selected');
            }
        });

        // Bind click events to notification account items
        container.find('.notification-account-item').on('click', function (e) {
            // Don't trigger if clicking on the checkbox, label, or any interactive element
            if (
                $(e.target).is('input[type="checkbox"]') ||
                $(e.target).is('label') ||
                $(e.target).closest('label').length > 0 ||
                $(e.target).closest('.form-check').length > 0
            ) {
                return;
            }

            const accountId = $(this).data('account-id');
            const checkbox = $(this).find('input[type="checkbox"]');

            // Toggle checkbox state
            if (checkbox.is(':checked')) {
                checkbox.prop('checked', false).trigger('change');
                $(`label[data-label-for="${accountId}"]`).text('Chọn').removeClass('text-success fw-bold');
            } else {
                checkbox.prop('checked', true).trigger('change');
                $(`label[data-label-for="${accountId}"]`).text('Đã chọn').addClass('text-success fw-bold');
            }
        });
    }

    // Function to get avatar HTML
    function getAvatarHtml(avatarUrl, displayName) {
        const name = displayName || 'Unknown';
        const initial = name.charAt(0).toUpperCase();

        if (avatarUrl && avatarUrl !== '') {
            return `<img src="${avatarUrl}" alt="${name}" class="avatar-img rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">`;
        } else {
            return `<div class="avatar-img rounded-circle d-flex align-items-center justify-content-center bg-secondary text-white" style="width: 50px; height: 50px;">${initial}</div>`;
        }
    }

    // Load notification accounts when modal is shown
    $('#createRuleModal').on('show.bs.modal', function () {
        // Load notification accounts
        getNotificationAccounts()
            .then(function (response) {
                notificationAccounts = response.items || [];
                // Pre-select accounts when copying a rule
                selectedNotificationAccounts = [];
                if (
                    Array.isArray(window.initialCreateNotificationAccountOwnIds) &&
                    window.initialCreateNotificationAccountOwnIds.length > 0
                ) {
                    selectedNotificationAccounts = notificationAccounts.filter(function (acc) {
                        return window.initialCreateNotificationAccountOwnIds.includes(acc.ownId);
                    });
                }
                renderNotificationAccountsSelection();
            })
            .catch(function (error) {
                console.error('Error loading notification accounts:', error);
                $('#list-notification-accounts').html(
                    '<div class="alert alert-danger">Không thể tải danh sách tài khoản thông báo.</div>'
                );
            });
    });

    // Load notification accounts when edit modal is shown
    $('#editRuleModal').on('show.bs.modal', function () {
        // Load notification accounts and populate selected ones for edit
        getNotificationAccounts()
            .then(function (response) {
                notificationAccounts = response.items || [];

                // Initialize selected accounts based on currentEditRule
                selectedNotificationAccounts = [];
                if (
                    currentEditRule &&
                    currentEditRule.notificationAccountIds &&
                    currentEditRule.notificationAccountIds.length > 0
                ) {
                    // Map the notificationAccountIds (which are ownIds) to full account objects
                    currentEditRule.notificationAccountIds.forEach((ownId) => {
                        const account = notificationAccounts.find(
                            (acc) => acc.ownId === ownId
                        );
                        if (account) {
                            selectedNotificationAccounts.push(account);
                        }
                    });
                }

                renderEditNotificationAccountsSelection();
            })
            .catch(function (error) {
                console.error('Error loading notification accounts:', error);
                $('#edit-list-notification-accounts').html(
                    '<div class="alert alert-danger">Không thể tải danh sách tài khoản thông báo.</div>'
                );
            });
    });

    // Function to render notification accounts selection for edit modal
    function renderEditNotificationAccountsSelection() {
        const container = $('#edit-list-notification-accounts');
        if (!notificationAccounts || notificationAccounts.length === 0) {
            container.html(
                '<div class="alert alert-info">Không có tài khoản thông báo nào.</div>'
            );
            return;
        }

        let html = `
            <div class="mb-2 col-12 col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control form-control-sm" id="notification-accounts-search" 
                           placeholder="Tìm kiếm theo tên hoặc số điện thoại..." 
                           autocomplete="off">
                    <button class="btn btn-outline-secondary" type="button" id="clear-search">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="notification-accounts-grid" id="notification-accounts-container">
        `;

        notificationAccounts.forEach((account) => {
            const isSelected = selectedNotificationAccounts.some(
                (selected) => selected.ownId === account.ownId
            );
            const displayName =
                account.displayName ||
                account.zaloName ||
                account.userName ||
                'Unknown';
            const avatarHtml = getAvatarHtml(account.avatarUrl, displayName);
            const mainBadge = account.isMain
                ? '<span class="badge bg-primary mt-1">Chính</span>'
                : '';

            html += `
                <div class="notification-account-item ${isSelected ? 'selected' : ''
                }" data-account-own-id="${account.ownId}" 
                    data-display-name="${displayName.toLowerCase()}" 
                    data-phone-number="${(account.phoneNumber || '').toLowerCase()}">
                    <div class="card overflow-hidden">
                        <img src="/images/logo/Zalo.png" alt="Zalo" class="img-fluid" style="width: 30px; height: 30px; object-fit: cover; position: absolute; bottom: 5px; right: 5px; z-index: 50;">

                        <div class="card-body">
                            <div class="row mx-0 text-center p-2">
                                <div class="d-flex p-0 w-100 mx-0 mb-1">
                                    <div class="avatar-container px-0" style="width: 50px; height: 50px; aspect-ratio: 1/1;">
                                        ${avatarHtml}
                                    </div>
                                    <h6 class="card-title pe-0 ps-1 d-inline-flex flex-column w-100">${displayName}${mainBadge}</h6>
                                </div>
                                <div class="px-1 min-w-100">
                                    <small class="fw-bold d-block">${account.phoneNumber || 'N/A'
                }</small>
                                </div>
                                <div class="form-check p-0">
                                    <input hidden class="form-check-input" type="checkbox" 
                                        id="edit-account-${account.ownId}" 
                                        value="${account.ownId}" 
                                        ${isSelected ? 'checked' : ''}>
                                    <label data-label-for="${account.ownId}" class="form-check-label ${isSelected ? 'text-success fw-bold' : ''}" for="edit-account-${account.ownId
                }">
                                        ${isSelected ? 'Đã chọn' : 'Chọn'}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.html(html);

        // Bind search functionality
        $('#notification-accounts-search').on('input', function () {
            filterNotificationAccounts($(this).val().toLowerCase());
        });

        // Clear search button
        $('#clear-search').on('click', function () {
            $('#notification-accounts-search').val('').trigger('input');
        });

        // Bind checkbox events
        container.find('input[type="checkbox"]').on('change', function () {
            const accountOwnId = $(this).val();
            const account = notificationAccounts.find(
                (acc) => acc.ownId === accountOwnId
            );

            if ($(this).is(':checked')) {
                if (
                    !selectedNotificationAccounts.some(
                        (selected) => selected.ownId === accountOwnId
                    )
                ) {
                    selectedNotificationAccounts.push(account);
                }
                $(this)
                    .closest('.notification-account-item')
                    .addClass('selected');
            } else {
                selectedNotificationAccounts =
                    selectedNotificationAccounts.filter(
                        (selected) => selected.ownId !== accountOwnId
                    );
                $(this)
                    .closest('.notification-account-item')
                    .removeClass('selected');
            }
        });

        // Bind click events to notification account items
        container.find('.notification-account-item').on('click', function (e) {
            // Don't trigger if clicking on the checkbox, label, or any interactive element
            if (
                $(e.target).is('input[type="checkbox"]') ||
                $(e.target).is('label') ||
                $(e.target).closest('label').length > 0 ||
                $(e.target).closest('.form-check').length > 0
            ) {
                return;
            }

            const accountOwnId = $(this).data('account-own-id');
            const checkbox = $(this).find('input[type="checkbox"]');

            // Toggle checkbox state
            if (checkbox.is(':checked')) {
                checkbox.prop('checked', false).trigger('change');
                $(`label[data-label-for="${accountOwnId}"]`).text('Chọn').removeClass('text-success fw-bold');
            } else {
                checkbox.prop('checked', true).trigger('change');
                $(`label[data-label-for="${accountOwnId}"]`).text('Đã chọn').addClass('text-success fw-bold');
            }
        });
    }

    function filterNotificationAccounts(searchTerm) {
        const items = $('.notification-account-item');

        items.each(function () {
            const item = $(this);
            const displayName = item.data('display-name');
            const phoneNumber = item.data('phone-number');

            const matchesSearch = !searchTerm ||
                displayName.includes(searchTerm) ||
                phoneNumber.includes(searchTerm);

            if (matchesSearch) {
                item.show();
            } else {
                item.hide();
            }
        });

        // Show "no results" message if no items match
        const visibleItems = items.filter(':visible');
        const noResultsMsg = $('#no-results-message');

        if (visibleItems.length === 0 && searchTerm) {
            if (noResultsMsg.length === 0) {
                $('#notification-accounts-container').append(
                    '<div id="no-results-message" class="alert alert-warning text-center">Không tìm thấy tài khoản nào phù hợp với từ khóa tìm kiếm.</div>'
                );
            }
        } else {
            noResultsMsg.remove();
        }
    }

    function filterCreateNotificationAccounts(searchTerm) {
        const items = $('#create-notification-accounts-container .notification-account-item');

        items.each(function () {
            const item = $(this);
            const displayName = item.data('display-name');
            const phoneNumber = item.data('phone-number');

            const matchesSearch = !searchTerm ||
                displayName.includes(searchTerm) ||
                phoneNumber.includes(searchTerm);

            if (matchesSearch) {
                item.show();
            } else {
                item.hide();
            }
        });

        // Show "no results" message if no items match
        const visibleItems = items.filter(':visible');
        const noResultsMsg = $('#create-no-results-message');

        if (visibleItems.length === 0 && searchTerm) {
            if (noResultsMsg.length === 0) {
                $('#create-notification-accounts-container').append(
                    '<div id="create-no-results-message" class="alert alert-warning text-center">Không tìm thấy tài khoản nào phù hợp với từ khóa tìm kiếm.</div>'
                );
            }
        } else {
            noResultsMsg.remove();
        }
    }

    // Reset selected accounts when modal is hidden
    $('#createRuleModal').on('hidden.bs.modal', function () {
        selectedNotificationAccounts = [];
    });

    // Reset selected accounts when edit modal is hidden
    $('#editRuleModal').on('hidden.bs.modal', function () {
        selectedNotificationAccounts = [];
    });

    // Reset global rule info when rule consumers modal is hidden
    $('#ruleConsumersModal').on('hidden.bs.modal', function () {
        currentRuleIdForConsumers = null;
        currentRuleNameForConsumers = null;
    });

    // Handle real-time filtering for ad accounts in Rule Consumers modal
    $(document).on('input', '#adAccountSearch', function () {
        const searchTerm = $(this).val().trim().toLowerCase();
        if (consumersDataViewMode === 'table') {
            filterAdAccountsInModalWithTableView(searchTerm);
        } else {
            filterAdAccountsInModal(searchTerm);
        }
    });

    // Handle real-time filtering for business centers in Rule Consumers modal
    $(document).on('input', '#businessCenterSearch', function () {
        const searchTerm = $(this).val().trim().toLowerCase();
        if (consumersDataViewMode === 'table') {
            filterBusinessCentersInModalWithTableView(searchTerm);
        } else {
            filterBusinessCentersInModal(searchTerm);
        }
    });

    // Function to filter ad accounts displayed in the Rule Consumers modal
    function filterAdAccountsInModal(searchTerm) {
        const adAccountsList = $('#adAccountsList');
        const adAccountItems = adAccountsList.find('.consumer-item');

        if (searchTerm.length === 0) {
            // Show all items if search term is empty
            adAccountItems.show();
            adAccountItems.each(function () {
                $(this).removeClass('d-none');
            });
            return;
        }

        let visibleCount = 0;
        let validAdAccountIds = [];

        adAccountItems.each(function () {
            const item = $(this);
            const accountName = item.find('.consumer-name').text().toLowerCase();
            const advertiserId = item.find('.consumer-id').text().toLowerCase();
            const ownerBcId = item.find('.consumer-detail-value').text().toLowerCase();

            // Check if any field contains the search term
            const isVisible = accountName.includes(searchTerm) ||
                advertiserId.includes(searchTerm) ||
                ownerBcId.includes(searchTerm);

            if (isVisible) {
                validAdAccountIds.push(advertiserId);
                item.show();
                item.removeClass('d-none');
                visibleCount++;
            } else {
                item.hide();
                item.addClass('d-none');
            }
        });

        // Show "no results" message if no items match
        if (visibleCount === 0) {
            if (adAccountsList.find('.no-results-message').length === 0) {
                adAccountsList.append(`
                    <div class="no-results-message alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Không tìm thấy tài khoản quảng cáo nào phù hợp với từ khóa: "${searchTerm}"
                    </div>
                `);
            }
        } else {
            adAccountsList.find('.no-results-message').remove();
        }
    }

    function filterAdAccountsInModalWithTableView(searchTerm) {
        const adAccountsList = $('#adAccountsList');
        const adAccountRows = adAccountsList.find('tbody tr');

        if (searchTerm.length === 0) {
            // Show all rows if search term is empty
            adAccountRows.show();
            adAccountRows.each(function () {
                $(this).removeClass('d-none');
            });
            // Remove any existing no-results message
            adAccountsList.find('.no-results-message').remove();
            return;
        }

        let visibleCount = 0;

        adAccountRows.each(function () {
            const row = $(this);
            // Get text content from relevant table cells for searching
            const accountName = row.find('td:nth-child(2)').text().toLowerCase(); // Tên tài khoản column
            const advertiserId = row.find('td:nth-child(3)').text().toLowerCase(); // ID tài khoản quảng cáo column
            const ownerBcId = row.find('td:nth-child(4)').text().toLowerCase(); // ID BC column
            const status = row.find('td:nth-child(5)').text().toLowerCase(); // Trạng thái column

            // Check if any field contains the search term
            const isVisible = accountName.includes(searchTerm) ||
                advertiserId.includes(searchTerm) ||
                ownerBcId.includes(searchTerm) ||
                status.includes(searchTerm);

            if (isVisible) {
                row.show();
                row.removeClass('d-none');
                visibleCount++;
            } else {
                row.hide();
                row.addClass('d-none');
            }
        });

        // Show "no results" message if no rows match
        if (visibleCount === 0) {
            if (adAccountsList.find('.no-results-message').length === 0) {
                adAccountsList.append(`
                    <div class="no-results-message alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Không tìm thấy tài khoản quảng cáo nào phù hợp với từ khóa: "${searchTerm}"
                    </div>
                `);
            }
        } else {
            adAccountsList.find('.no-results-message').remove();
        }
    }

    // Function to filter business centers displayed in the Rule Consumers modal
    function filterBusinessCentersInModal(searchTerm) {
        const businessCentersList = $('#businessCentersList');
        const businessCenterItems = businessCentersList.find('.consumer-item');

        if (searchTerm.length === 0) {
            // Show all items if search term is empty
            businessCenterItems.show();
            businessCenterItems.each(function () {
                $(this).removeClass('d-none');
            });
            return;
        }

        let visibleCount = 0;

        businessCenterItems.each(function () {
            const item = $(this);
            const centerName = item.find('.consumer-name').text().toLowerCase();
            const centerId = item.find('.consumer-id').text().toLowerCase();
            const company = item.find('.consumer-detail-value').first().text().toLowerCase();
            const currency = item.find('.consumer-detail-value').eq(1).text().toLowerCase();
            const area = item.find('.consumer-detail-value').eq(2).text().toLowerCase();

            // Check if any field contains the search term
            const isVisible = centerName.includes(searchTerm) ||
                centerId.includes(searchTerm) ||
                company.includes(searchTerm) ||
                currency.includes(searchTerm) ||
                area.includes(searchTerm);

            if (isVisible) {
                item.show();
                item.removeClass('d-none');
                visibleCount++;
            } else {
                item.hide();
                item.addClass('d-none');
            }
        });

        // Show "no results" message if no items match
        if (visibleCount === 0) {
            if (businessCentersList.find('.no-results-message').length === 0) {
                businessCentersList.append(`
                    <div class="no-results-message alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Không tìm thấy trung tâm kinh doanh nào phù hợp với từ khóa: "${searchTerm}"
                    </div>
                `);
            }
        } else {
            businessCentersList.find('.no-results-message').remove();
        }
    }

    // Function to filter business centers displayed in the Rule Consumers modal with table view
    function filterBusinessCentersInModalWithTableView(searchTerm) {
        const businessCentersList = $('#businessCentersList');
        const businessCenterRows = businessCentersList.find('tbody tr');

        if (searchTerm.length === 0) {
            // Show all rows if search term is empty
            businessCenterRows.show();
            businessCenterRows.each(function () {
                $(this).removeClass('d-none');
            });
            // Remove any existing no-results message
            businessCentersList.find('.no-results-message').remove();
            return;
        }

        let visibleCount = 0;

        businessCenterRows.each(function () {
            const row = $(this);
            // Get text content from relevant table cells for searching
            const centerName = row.find('td:nth-child(2)').text().toLowerCase(); // Tên trung tâm column
            const centerId = row.find('td:nth-child(3)').text().toLowerCase(); // ID column
            const company = row.find('td:nth-child(4)').text().toLowerCase(); // Công ty column
            const currency = row.find('td:nth-child(5)').text().toLowerCase(); // Tiền tệ column
            const area = row.find('td:nth-child(6)').text().toLowerCase(); // Khu vực column

            // Check if any field contains the search term
            const isVisible = centerName.includes(searchTerm) ||
                centerId.includes(searchTerm) ||
                company.includes(searchTerm) ||
                currency.includes(searchTerm) ||
                area.includes(searchTerm);

            if (isVisible) {
                row.show();
                row.removeClass('d-none');
                visibleCount++;
            } else {
                row.hide();
                row.addClass('d-none');
            }
        });

        // Show "no results" message if no rows match
        if (visibleCount === 0) {
            if (businessCentersList.find('.no-results-message').length === 0) {
                businessCentersList.append(`
                    <div class="no-results-message alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Không tìm thấy trung tâm kinh doanh nào phù hợp với từ khóa: "${searchTerm}"
                    </div>
                `);
            }
        } else {
            businessCentersList.find('.no-results-message').remove();
        }
    }

    // Helper function to get localized text
    const getLocalizedText = (enText, viText) => currentLang === 'en-US' ? enText : viText;

    const syncfusionLocalization = {
        dateOperators: {
            equal: getLocalizedText('Equal', 'Bằng'),
            greaterthan: getLocalizedText('Greater than', 'Lớn hơn'),
            greaterthanorequal: getLocalizedText('Greater than or equal', 'Lớn hơn hoặc bằng'),
            lessthan: getLocalizedText('Less than', 'Nhỏ hơn'),
            lessthanorequal: getLocalizedText('Less than or equal', 'Nhỏ hơn hoặc bằng'),
            notequal: getLocalizedText('Not equal', 'Không bằng'),
            between: getLocalizedText('Between', 'Giữa'),
            notbetween: getLocalizedText('Not between', 'Không ở giữa'),
        },
        stringOperators: {
            equal: getLocalizedText('Equal', 'Bằng'),
            notequal: getLocalizedText('Not equal', 'Không bằng'),
            contains: getLocalizedText('Contains', 'Chứa'),
            notcontains: getLocalizedText('Not contains', 'Không chứa'),
            startswith: getLocalizedText('Starts with', 'Bắt đầu với'),
            endswith: getLocalizedText('Ends with', 'Kết thúc với'),
        },
        numberOperators: {
            equal: getLocalizedText('Equal', 'Bằng'),
            notequal: getLocalizedText('Not equal', 'Không bằng'),
            greaterthan: getLocalizedText('Greater than', 'Lớn hơn'),
            greaterthanorequal: getLocalizedText('Greater than or equal', 'Lớn hơn hoặc bằng'),
            lessthan: getLocalizedText('Less than', 'Nhỏ hơn'),
            lessthanorequal: getLocalizedText('Less than or equal', 'Nhỏ hơn hoặc bằng'),
            between: getLocalizedText('Between', 'Giữa'),
            notbetween: getLocalizedText('Not between', 'Không ở giữa'),
        },
        booleanOperators: {
            equal: getLocalizedText('Equal', 'Bằng'),
            notequal: getLocalizedText('Not equal', 'Không bằng'),
        },
    }
    // Initialize QueryBuilder when modal is shown
    const dateOperators = [
        { value: 'equal', key: syncfusionLocalization.dateOperators.equal },
        { value: 'greaterthan', key: syncfusionLocalization.dateOperators.greaterthan },
        { value: 'greaterthanorequal', key: syncfusionLocalization.dateOperators.greaterthanorequal },
        { value: 'lessthan', key: syncfusionLocalization.dateOperators.lessthan },
        { value: 'lessthanorequal', key: syncfusionLocalization.dateOperators.lessthanorequal },
        { value: 'notequal', key: syncfusionLocalization.dateOperators.notequal },
        { value: 'between', key: syncfusionLocalization.dateOperators.between },
        { value: 'notbetween', key: syncfusionLocalization.dateOperators.notbetween },
    ];

    const stringOperators = [
        { value: 'equal', key: syncfusionLocalization.stringOperators.equal },
        { value: 'notequal', key: syncfusionLocalization.stringOperators.notequal },
        { value: 'contains', key: syncfusionLocalization.stringOperators.contains },
        { value: 'notcontains', key: syncfusionLocalization.stringOperators.notcontains },
        { value: 'startswith', key: syncfusionLocalization.stringOperators.startswith },
        { value: 'endswith', key: syncfusionLocalization.stringOperators.endswith },
    ];

    const numberOperators = [
        { value: 'equal', key: syncfusionLocalization.numberOperators.equal },
        { value: 'notequal', key: syncfusionLocalization.numberOperators.notequal },
        { value: 'greaterthan', key: syncfusionLocalization.numberOperators.greaterthan },
        { value: 'greaterthanorequal', key: syncfusionLocalization.numberOperators.greaterthanorequal },
        { value: 'lessthan', key: syncfusionLocalization.numberOperators.lessthan },
        { value: 'lessthanorequal', key: syncfusionLocalization.numberOperators.lessthanorequal },
        { value: 'between', key: syncfusionLocalization.numberOperators.between },
        { value: 'notbetween', key: syncfusionLocalization.numberOperators.notbetween },
    ];

    const booleanOperators = [
        { value: 'equal', key: syncfusionLocalization.booleanOperators.equal },
        { value: 'notequal', key: syncfusionLocalization.booleanOperators.notequal },
    ];

    // Shared column data for both create and edit QueryBuilders
    const sharedColumnData = [
        // Priority Financial Fields (Most Important)
        {
            field: 'Currency',
            label: 'Tiền tệ (Currency)',
            type: 'string',
            operators: stringOperators,
            template: {
                create: function () {
                    elem = document.createElement('input');
                    elem.setAttribute('type', 'text');
                    return elem;
                },
                destroy: function (args) {
                    var dropdown = ej.base.getComponent(
                        document.getElementById(args.elementId),
                        'dropdownlist'
                    );
                    if (dropdown) {
                        dropdown.destroy();
                    }
                },
                write: function (args) {
                    var currencyData = [
                        {
                            value: 'VND',
                            text: 'VND - Đồng Việt Nam',
                        },
                        {
                            value: 'USD',
                            text: 'USD - Đô la Mỹ',
                        },
                        {
                            value: 'EUR',
                            text: 'EUR - Euro',
                        },
                        {
                            value: 'JPY',
                            text: 'JPY - Yên Nhật',
                        },
                        {
                            value: 'CNY',
                            text: 'CNY - Nhân dân tệ',
                        },
                        {
                            value: 'KRW',
                            text: 'KRW - Won Hàn Quốc',
                        },
                        {
                            value: 'SGD',
                            text: 'SGD - Đô la Singapore',
                        },
                        {
                            value: 'THB',
                            text: 'THB - Baht Thái',
                        },
                        {
                            value: 'MYR',
                            text: 'MYR - Ringgit Malaysia',
                        },
                        {
                            value: 'IDR',
                            text: 'IDR - Rupiah Indonesia',
                        },
                        {
                            value: 'PHP',
                            text: 'PHP - Peso Philippines',
                        },
                        {
                            value: 'INR',
                            text: 'INR - Rupee Ấn Độ',
                        },
                        {
                            value: 'GBP',
                            text: 'GBP - Bảng Anh',
                        },
                        {
                            value: 'AUD',
                            text: 'AUD - Đô la Úc',
                        },
                        {
                            value: 'CAD',
                            text: 'CAD - Đô la Canada',
                        },
                        {
                            value: 'CHF',
                            text: 'CHF - Franc Thụy Sĩ',
                        },
                        {
                            value: 'HKD',
                            text: 'HKD - Đô la Hong Kong',
                        },
                        {
                            value: 'TWD',
                            text: 'TWD - Đô la Đài Loan',
                        },
                    ];

                    var dropDownObj = new ej.dropdowns.DropDownList({
                        dataSource: currencyData,
                        value: args.values ? args.values : '',
                        fields: { text: 'text', value: 'value' },
                        placeholder: 'Chọn tiền tệ',
                        change: function (e) {
                            if (queryBuilder) {
                                queryBuilder.notifyChange(
                                    e.itemData.value,
                                    e.element
                                );
                            } else if (editQueryBuilder) {
                                editQueryBuilder.notifyChange(
                                    e.itemData.value,
                                    e.element
                                );
                            }
                        },
                    });
                    dropDownObj.appendTo('#' + args.elements.id);
                },
            },
        },
        {
            field: 'ValidCashBalance',
            label: 'Số dư tiền mặt hợp lệ (Valid Cash Balance)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'CashBalance',
            label: 'Số dư tiền mặt (Cash Balance)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'AccountBalance',
            label: 'Số dư tài khoản (Account Balance)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'ValidAccountBalance',
            label: 'Số dư tài khoản hợp lệ (Valid Account Balance)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'Budget',
            label: 'Ngân sách (Budget)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'BudgetCost',
            label: 'Chi phí ngân sách (Budget Cost)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'BudgetRemaining',
            label: 'Ngân sách còn lại (Budget Remaining)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'BalanceBudgetRatio',
            label: 'Tỷ lệ Số dư/Ngân sách (%) (Balance/Budget Ratio)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'TransferableAmount',
            label: 'Số tiền có thể chuyển (Transferable Amount)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'GrantBalance',
            label: 'Số dư tài trợ (Grant Balance)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'ValidGrantBalance',
            label: 'Số dư tài trợ hợp lệ (Valid Grant Balance)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'FrozenBalance',
            label: 'Số dư đóng băng (Frozen Balance)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'Tax',
            label: 'Thuế (Tax)',
            type: 'number',
            operators: numberOperators,
        },

        // Advertiser Account Information
        {
            field: 'AdvertiserId',
            label: 'ID TK Quảng cáo (Advertiser ID)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'AdvertiserName',
            label: 'Tên TK Quảng cáo (Advertiser Name)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'AdvertiserStatus',
            label: 'Trạng thái Quảng cáo (Advertiser Status)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'AdvertiserType',
            label: 'Loại Quảng cáo (Advertiser Type)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'BcId',
            label: 'ID Trung tâm kinh doanh (Business Center ID)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'Company',
            label: 'Công ty (Company)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'ContactName',
            label: 'Tên liên hệ (Contact Name)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'ContactEmail',
            label: 'Email liên hệ (Contact Email)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'Timezone',
            label: 'Múi giờ (Timezone)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'BudgetMode',
            label: 'Chế độ ngân sách (Budget Mode)',
            type: 'string',
            operators: stringOperators,
        },
        {
            field: 'AccountOpenDays',
            label: 'Số ngày mở tài khoản (Account Open Days)',
            type: 'number',
            operators: numberOperators,
        },
        {
            field: 'BalanceReminder',
            label: 'Nhắc nhở số dư (Balance Reminder)',
            type: 'boolean',
            operators: booleanOperators,
        },
        {
            field: 'CreateTime',
            label: 'Thời gian tạo (Create Time)',
            type: 'date',
            format: 'dd/MM/yyyy',
            operators: dateOperators,
        },
        {
            field: 'Date',
            label: 'Ngày (Date)',
            type: 'date',
            format: 'dd/MM/yyyy',
            operators: dateOperators,
        },
    ];

    function initializeQueryBuilder() {
        if (queryBuilder) {
            queryBuilder.destroy();
        }

        // Use initial conditions if provided (e.g., when copying a rule)
        const initialCreateRule =
            window.initialCreateConditions && typeof window.initialCreateConditions === 'string'
                ? JSON.parse(window.initialCreateConditions)
                : {};

        const qryBldrObj = new ej.querybuilder.QueryBuilder({
            width: '100%',
            columns: sharedColumnData,
            rule: initialCreateRule,
            allowValidation: true,
            showButtons: {
                ruleDelete: true,
                groupDelete: true,
            },
            locale: currentLang,
        });

        // Add event listener for condition changes
        qryBldrObj.addEventListener('change', function (args) {
            if (args.rule) {
                // Convert the rule to JSON and save to hidden input
                var conditionsJson = JSON.stringify(args.rule);
                $('#conditionsJson').val(conditionsJson);
            }
        });

        qryBldrObj.appendTo('#querybuilder');
        queryBuilder = qryBldrObj; // Store reference to queryBuilder

        // Initialize hidden input if we already have initial conditions
        if (window.initialCreateConditions) {
            $('#conditionsJson').val(window.initialCreateConditions);
        }
    }

    // Initialize Edit QueryBuilder
    function initializeEditQueryBuilder(existingConditions) {
        if (editQueryBuilder) {
            editQueryBuilder.destroy();
        }

        const qryBldrObj = new ej.querybuilder.QueryBuilder({
            width: '100%',
            columns: sharedColumnData,
            rule: existingConditions
                ? JSON.parse(existingConditions)
                : {
                    condition: 'and',
                    rules: [
                        {
                            label: 'Field',
                            field: 'AdvertiserId',
                            type: 'string',
                            operator: 'equal',
                            value: '',
                        },
                    ],
                },
            allowValidation: true,
            showButtons: {
                ruleDelete: true,
                groupDelete: true,
            },
            locale: currentLang,
            ruleChange: function (args) {
                // Update hidden input with conditions JSON
                if (args.rule) {
                    document.getElementById('editConditionsJson').value =
                        JSON.stringify(args.rule);
                }
            },
        });

        qryBldrObj.appendTo('#editQuerybuilder');
        editQueryBuilder = qryBldrObj; // Store reference to editQueryBuilder
    }

    // Open Edit Modal
    window.openEditModal = function openEditModal(ruleData) {
        // Check if user has permission to edit notification rules
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            abp.notify.error('Bạn không có quyền chỉnh sửa quy tắc thông báo.');
            return;
        }

        currentEditRule = ruleData;
        // Populate form fields
        $('#editRuleId').val(ruleData.id);
        $('#editRuleName').val(ruleData.ruleName);
        $('#editTargetEntity').val(ruleData.targetEntity);
        $('#editNotificationFrequency').val(
            ruleData.notificationFrequency || 0
        );
        $('#editIsDefault').prop('checked', ruleData.isDefault || false);
        $('#editIsPublic').prop('checked', ruleData.isPublic || false);
        // Show modal
        $('#editRuleModal').modal('show');
    }

    // Handle New Rule Button Click
    $('#NewRuleButton').click(function (e) {
        e.preventDefault();
        // Check if user has permission to create notification rules
        if (window.notificationRulePermission && window.notificationRulePermission.canCreate) {
            // Clear any copy-related initial data for a fresh create
            window.initialCreateConditions = undefined;
            window.initialCreateNotificationAccountOwnIds = undefined;
            $('#createRuleModal').modal('show');
        } else {
            abp.notify.error('Bạn không có quyền tạo quy tắc thông báo mới.');
        }
    });

    // Initialize QueryBuilder when create modal is shown
    $('#createRuleModal').on('shown.bs.modal', function () {
        initializeQueryBuilder();
    });

    // Initialize Edit QueryBuilder when edit modal is shown
    $('#editRuleModal').on('shown.bs.modal', function () {
        if (currentEditRule && currentEditRule.condition) {
            initializeEditQueryBuilder(currentEditRule.condition);
        } else {
            initializeEditQueryBuilder(null);
        }
    });

    // Handle Save Button Click for Create
    $('#saveRuleButton').click(function () {
        // Check if user has permission to create notification rules
        if (!window.notificationRulePermission || !window.notificationRulePermission.canCreate) {
            abp.notify.error('Bạn không có quyền tạo quy tắc thông báo mới.');
            return;
        }

        let ruleName = $('#ruleName').val();

        // Get conditions directly from queryBuilder
        let conditionsJson = '';

        if (queryBuilder) {
            let rule = queryBuilder.getRules();
            if (rule && rule.rules && rule.rules.length > 0) {
                conditionsJson = JSON.stringify(rule);
            }
        }

        if (!ruleName) {
            abp.notify.error('Vui lòng nhập tên quy tắc.');
            return;
        }

        if (!conditionsJson) {
            abp.notify.error('Vui lòng thêm ít nhất một điều kiện.');
            return;
        }

        let conditions = JSON.parse(conditionsJson);
        if (!conditions.rules || conditions.rules.length === 0) {
            abp.notify.error('Vui lòng thêm ít nhất một điều kiện.');
            return;
        }

        // Validate conditions - check if all fields are valid
        let isValid = true;
        let invalidFields = [];

        conditions.rules.forEach(function (rule) {
            if (rule.field) {
                // Check if field is one of the valid fields
                const validFields = [
                    'AdvertiserId',
                    'AdvertiserName',
                    'AdvertiserStatus',
                    'AdvertiserType',
                    'Timezone',
                    'Currency',
                    'AccountOpenDays',
                    'BalanceReminder',
                    'Company',
                    'ContactName',
                    'ContactEmail',
                    'CreateTime',
                    'AccountBalance',
                    'ValidAccountBalance',
                    'FrozenBalance',
                    'Tax',
                    'CashBalance',
                    'ValidCashBalance',
                    'GrantBalance',
                    'ValidGrantBalance',
                    'TransferableAmount',
                    'BudgetMode',
                    'Budget',
                    'BudgetCost',
                    'BudgetRemaining',
                    'BcId',
                    'BalanceBudgetRatio',
                    'Date',
                ];

                if (!validFields.includes(rule.field)) {
                    isValid = false;
                    invalidFields.push(rule.field);
                }
            }
        });

        if (!isValid) {
            abp.notify.error(
                'Có trường không hợp lệ: ' + invalidFields.join(', ')
            );
            return;
        }

        // Tài khoản thông báo không bắt buộc
        // Get selected notification accounts
        let selectedAccountIds = selectedNotificationAccounts.map(
            (account) => account.ownId
        );

        // if (selectedAccountIds.length === 0) {
        //     abp.notify.error('Vui lòng chọn ít nhất một tài khoản thông báo.');
        //     return;
        // }

        // Get notification frequency
        let notificationFrequency =
            parseInt($('#notificationFrequency').val()) || 0;

        // Get is default status
        let isDefault = $('#isDefault').is(':checked');
        let isPublic = $('#isPublic').is(':checked');

        // Create the rule data matching RuleDto structure
        const ruleData = {
            ruleName: ruleName,
            targetEntity: 1, // Balance entity
            condition: conditionsJson, // Store the JSON string directly
            ownerId: null, // Will be set by the backend
            notificationAccountIds: selectedAccountIds, // Add selected notification account IDs
            notificationFrequency: notificationFrequency, // Add notification frequency
            isDefault: isDefault, // Add is default status
            isPublic: isPublic
        };

        // Send AJAX request to create the rule
        abp.ajax({
            url: abp.appPath + 'api/app/rules',
            type: 'POST',
            data: JSON.stringify(ruleData),
            contentType: 'application/json',
        })
            .then(function () {
                abp.notify.info('Quy tắc đã được tạo thành công!');
                $('#createRuleModal').modal('hide');
                dataTable.ajax.reload();

                // Reset form
                $('#ruleForm')[0].reset();
                selectedNotificationAccounts = [];
                if (queryBuilder) {
                    queryBuilder.destroy();
                    queryBuilder = null;
                }
            })
            .catch(function (error) {
                abp.notify.error('Không thể tạo quy tắc: ' + error.message);
            });
    });

    // Handle Update Button Click for Edit
    $('#updateRuleButton').click(function () {
        // Check if user has permission to edit notification rules
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            abp.notify.error('Bạn không có quyền chỉnh sửa quy tắc thông báo.');
            return;
        }

        let ruleId = $('#editRuleId').val();
        let ruleName = $('#editRuleName').val();

        // Get conditions directly from editQueryBuilder
        let conditionsJson = '';
        if (editQueryBuilder) {
            let rule = editQueryBuilder.getRules();
            if (rule && rule.rules && rule.rules.length > 0) {
                conditionsJson = JSON.stringify(rule);
            }
        }

        if (!ruleName) {
            abp.notify.error('Vui lòng nhập tên quy tắc.');
            return;
        }

        if (!conditionsJson) {
            abp.notify.error('Vui lòng thêm ít nhất một điều kiện.');
            return;
        }

        let conditions = JSON.parse(conditionsJson);
        if (!conditions.rules || conditions.rules.length === 0) {
            abp.notify.error('Vui lòng thêm ít nhất một điều kiện.');
            return;
        }

        // Validate conditions - check if all fields are valid
        let isValid = true;
        let invalidFields = [];

        conditions.rules.forEach(function (rule) {
            if (rule.field) {
                // Check if field is one of the valid fields
                const validFields = [
                    'AdvertiserId',
                    'AdvertiserName',
                    'AdvertiserStatus',
                    'AdvertiserType',
                    'Timezone',
                    'Currency',
                    'AccountOpenDays',
                    'BalanceReminder',
                    'Company',
                    'ContactName',
                    'ContactEmail',
                    'CreateTime',
                    'AccountBalance',
                    'ValidAccountBalance',
                    'FrozenBalance',
                    'Tax',
                    'CashBalance',
                    'ValidCashBalance',
                    'GrantBalance',
                    'ValidGrantBalance',
                    'TransferableAmount',
                    'BudgetMode',
                    'Budget',
                    'BudgetCost',
                    'BudgetRemaining',
                    'BcId',
                    'BalanceBudgetRatio',
                    'Date',
                ];

                if (!validFields.includes(rule.field)) {
                    isValid = false;
                    invalidFields.push(rule.field);
                }
            }
        });

        if (!isValid) {
            abp.notify.error(
                'Có trường không hợp lệ: ' + invalidFields.join(', ')
            );
            return;
        }

        // Get selected notification accounts
        let selectedAccountIds = selectedNotificationAccounts.map(
            (account) => account.ownId
        );

        // if (selectedAccountIds.length === 0) {
        //     abp.notify.error('Vui lòng chọn ít nhất một tài khoản thông báo.');
        //     return;
        // }

        // Get notification frequency
        let notificationFrequency =
            parseInt($('#editNotificationFrequency').val()) || 0;

        // Get is default status
        let isDefault = $('#editIsDefault').is(':checked');
        let isPublic = $('#editIsPublic').is(':checked');

        // Create the rule data for update
        let ruleData = {
            ruleName: ruleName,
            targetEntity: $('#editTargetEntity').val(),
            condition: conditionsJson,
            ownerId: currentEditRule.ownerId,
            notificationAccountIds: selectedAccountIds, // Add selected notification account Own-IDs
            notificationFrequency: notificationFrequency, // Add notification frequency
            isDefault: isDefault, // Add is default status
            isPublic: isPublic
        };

        // Send AJAX request to update the rule
        abp.ajax({
            url: abp.appPath + 'api/app/rules/' + ruleId,
            type: 'PUT',
            data: JSON.stringify(ruleData),
            contentType: 'application/json',
        })
            .then(function () {
                abp.notify.info('Quy tắc đã được cập nhật thành công!');
                $('#editRuleModal').modal('hide');
                dataTable.ajax.reload();

                // Reset form
                $('#editRuleForm')[0].reset();
                selectedNotificationAccounts = [];
                if (editQueryBuilder) {
                    editQueryBuilder.destroy();
                    editQueryBuilder = null;
                }
                currentEditRule = null;
            })
            .catch(function (error) {
                abp.notify.error(
                    'Không thể cập nhật quy tắc: ' + error.message
                );
            });
    });

    // Reset form when create modal is hidden
    $('#createRuleModal').on('hidden.bs.modal', function () {
        $('#ruleForm')[0].reset();
        if (queryBuilder) {
            queryBuilder.destroy();
            queryBuilder = null;
        }
        // Clear any initial data used for copy flow
        window.initialCreateConditions = undefined;
        window.initialCreateNotificationAccountOwnIds = undefined;
    });

    // Reset form when edit modal is hidden
    $('#editRuleModal').on('hidden.bs.modal', function () {
        $('#editRuleForm')[0].reset();
        if (editQueryBuilder) {
            editQueryBuilder.destroy();
            editQueryBuilder = null;
        }
        currentEditRule = null;
    });

    // Handle copy Rule ID button click
    // Handle sự kiện click vào button copy Rule ID
    $('#copyRuleIdBtn').click(function () {
        var ruleId = $('#editRuleId').val();
        if (ruleId) {
            // Use the modern clipboard API
            navigator.clipboard
                .writeText(ruleId)
                .then(function () {
                    abp.notify.success(
                        'ID Quy tắc đã được sao chép vào clipboard!'
                    );
                })
                .catch(function (err) {
                    // Fallback for older browsers
                    var textArea = document.createElement('textarea');
                    textArea.value = ruleId;
                    document.body.appendChild(textArea);
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        abp.notify.success(
                            'ID Quy tắc đã được sao chép vào clipboard!'
                        );
                    } catch (err) {
                        abp.notify.error('Không thể sao chép ID Quy tắc');
                    }
                    document.body.removeChild(textArea);
                });
        } else {
            abp.notify.warn('Không có ID Quy tắc để sao chép');
        }
    });

    // Handle Duplicate Rule action from dropdown
    // Lấy dữ liệu từ bảng để copy
    $(document).on('click', 'a[data-action="duplicate-rule"]', function (e) {
        e.preventDefault();
        try {
            const $tr = $(this).closest('tr');
            const rowData = dataTable.row($tr).data();
            if (rowData && typeof window.copyRule === 'function') {
                // Xử lý Copy Rule ở CopyRuleHandler
                window.copyRule(rowData);
            }
        } catch (err) {
            console.error('Duplicate rule failed:', err);
        }
    });

    // Handle Add Ad Account Button Click
    $('#addAdAccountBtn').click(function () {
        // Check if user has permission to edit notification rules (for adding consumers)
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            abp.notify.error('Bạn không có quyền chỉnh sửa quy tắc thông báo.');
            return;
        }

        openSelectAdAccountsModal();
    });

    let selectedAdAccountsState = [];
    // Function to open ad account selection modal
    function openSelectAdAccountsModal() {
        // Reset modal state
        $('#adAccountSearchInput').val('');
        $('#adAccountSearchDependClientInput').val('');
        $('#adAccountsSearchResults').addClass('d-none');
        $('#adAccountsSearchLoading').removeClass('d-none');
        $('#selectedAdAccountsList').empty();
        $('#selectedAdAccountsCount').text('0');

        // Clear any previous search results
        $('#adAccountsSearchList').empty();

        // Show modal
        $('#selectAdAccountsModal').modal('show');

        // Reset selectedAdAccountsState
        selectedAdAccountsState = [];

        setTimeout(() => {
            getListAdAccounts();
        }, 1000);
    }

    // Handle search button click
    $(document).on('click', '#searchAdAccountsBtn', function () {
        const searchTerm = $('#adAccountSearchInput').val().trim();
        const searchDependClient = $('#adAccountSearchDependClientInput').val().trim();
        searchAdAccounts(searchTerm, searchDependClient);
    });

    // Handle clear search button click
    $(document).on('click', '#clearSearchBtn', function () {
        $('#adAccountSearchInput').val('');
        $('#adAccountsSearchResults').addClass('d-none');
        $('#adAccountsSearchLoading').addClass('d-none');
        $('#adAccountsSearchList').empty();
    });

    $(document).on('click', '#clearSearchDependClientBtn', function () {
        $('#adAccountSearchDependClientInput').val('');
    });

    $(document).on('input', '#adAccountSearchDependClientInput', function () {
        const searchTerm = $('#adAccountSearchInput').val().trim();
        const searchDependClient = $('#adAccountSearchDependClientInput').val().trim();
        searchAdAccounts(searchTerm, searchDependClient);
    });

    // Handle search input enter key
    $(document).on('keypress', '#adAccountSearchInput', function (e) {
        if (e.which === 13) {
            $('#searchAdAccountsBtn').click();
        }
    });

     // Function to search ad accounts
     function getListAdAccounts() {
        // Show loading state
        $('#adAccountsSearchLoading').removeClass('d-none');
        $('#adAccountsSearchResults').addClass('d-none');

        // Prepare search parameters
        const searchInput = {
            maxResultCount: 50, // Limit results for better performance
            skipCount: 0,
            sorting: 'name' // Sort by name for better UX
        };
        // Otherwise, search by name
        searchInput.filterText = "";
        //abp.ui.setBusy('#adAccountsSearchResults')
        // Call the ABP framework client API proxy
        let searchTerm = "";
        tikTok.adAccounts.adAccount.getList(searchInput)
            .then(function (response) {
                //abp.ui.clearBusy('#adAccountsSearchResults');
                if (response && response.items && response.items.length > 0) {
                    // Filter results to match search term more precisely
                    const filteredResults = response.items.filter(account => {
                        const nameMatch = account.name && account.name.toLowerCase().includes(searchTerm.toLowerCase());
                        const advertiserIdMatch = account.advertiserId && account.advertiserId.toLowerCase().includes(searchTerm.toLowerCase());
                        const ownerBcIdMatch = account.ownerBcId && account.ownerBcId.toLowerCase().includes(searchTerm.toLowerCase());

                        return nameMatch || advertiserIdMatch || ownerBcIdMatch;
                    });

                    displaySearchResults(filteredResults);
                } else {
                    // No results found
                    displaySearchResults([]);
                }
            })
            .catch(function (error) {
                //abp.ui.clearBusy('#adAccountsSearchResults');
                console.error('Error searching ad accounts:', error);

                // Hide loading and show error
                $('#adAccountsSearchLoading').addClass('d-none');
                $('#adAccountsSearchResults').removeClass('d-none');
                $('#adAccountsSearchList').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Lỗi khi tìm kiếm tài khoản quảng cáo: ${error.message || 'Không thể kết nối đến máy chủ'}
                    </div>
                `);
            });
    }

    // Function to search ad accounts
    function searchAdAccounts(searchTerm, searchDependClient, paramsSearchInput = null) {
        // Show loading state
        $('#adAccountsSearchLoading').removeClass('d-none');
        $('#adAccountsSearchResults').addClass('d-none');

        // Prepare search parameters
        const searchInput = {
            ...paramsSearchInput,
            maxResultCount: 50, // Limit results for better performance
            skipCount: 0,
            sorting: 'name' // Sort by name for better UX
        };
        // Otherwise, search by name
        searchInput.filterText = searchTerm;
        searchInput.customerFilterText = searchDependClient;
        //abp.ui.setBusy('#adAccountsSearchResults')
        // Call the ABP framework client API proxy
        tikTok.adAccounts.adAccount.getList(searchInput)
            .then(function (response) {
                //abp.ui.clearBusy('#adAccountsSearchResults');
                if (response && response.items && response.items.length > 0) {
                    // Filter results to match search term more precisely
                    const filteredResults = response.items.filter(account => {
                        const nameMatch = account.name && account.name.toLowerCase().includes(searchTerm.toLowerCase());
                        const advertiserIdMatch = account.advertiserId && account.advertiserId.toLowerCase().includes(searchTerm.toLowerCase());
                        const ownerBcIdMatch = account.ownerBcId && account.ownerBcId.toLowerCase().includes(searchTerm.toLowerCase());

                        return nameMatch || advertiserIdMatch || ownerBcIdMatch;
                    });

                    displaySearchResults(filteredResults);
                } else {
                    // No results found
                    displaySearchResults([]);
                }
            })
            .catch(function (error) {
                //abp.ui.clearBusy('#adAccountsSearchResults');
                console.error('Error searching ad accounts:', error);

                // Hide loading and show error
                $('#adAccountsSearchLoading').addClass('d-none');
                $('#adAccountsSearchResults').removeClass('d-none');
                $('#adAccountsSearchList').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Lỗi khi tìm kiếm tài khoản quảng cáo: ${error.message || 'Không thể kết nối đến máy chủ'}
                    </div>
                `);
            });
    }

    // Function to display search results
    function displaySearchResults(results) {
        $('#adAccountsSearchLoading').addClass('d-none');

        if (results.length === 0) {
            $('#adAccountsSearchResults').removeClass('d-none');
            $('#adAccountsSearchList').html(`
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Không tìm thấy tài khoản quảng cáo nào phù hợp với từ khóa tìm kiếm.
                </div>
            `);
            return;
        }

        $('#adAccountsSearchResults').removeClass('d-none');

        let resultsHtml = '';
        results.forEach(account => {
            const isSelected = isAdAccountSelected(account.id);
            const statusBadgeClass = getStatusBadgeClass(account.status);
            const statusText = getStatusText(account.status);

            resultsHtml += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${account.name || 'N/A'}</h6>
                        <small class="text-muted">
                            ID: ${account.advertiserId || 'N/A'} | Owner BC: ${account.ownerBcId || 'N/A'} | 
                            Status: <span class="badge ${statusBadgeClass}">${statusText}</span>
                            ${account.company ? `| Company: ${account.company}<br/><span class="text-muted fw-bold">Khách hàng: ${account.customerName ?? '-'} (${account.customerId ?? '-'})</span>` : ''}
                        </small>
                    </div>
                    <button data-account-id="${account.id}" type="button" class="btn btn-sm ${isSelected ? 'btn-danger' : 'btn-primary'}" 
                            onclick="toggleAdAccountSelection(event, '${account.id}', '${escapeHtml(account.name || 'N/A')}', '${account.advertiserId || 'N/A'}', '${account.ownerBcId || 'N/A'}')">
                        <i class="fas fa-${isSelected ? 'times' : 'plus'}"></i>
                        ${isSelected ? 'Bỏ chọn' : 'Chọn'}
                    </button>
                </div>
            `;
        });

        $('#adAccountsSearchList').html(resultsHtml);
    }

    // Helper function to get status badge class
    function getStatusBadgeClass(status) {
        switch (status) {
            case 1: // ACTIVE
                return 'bg-success';
            case 2: // INACTIVE
                return 'bg-warning';
            case 3: // SUSPENDED
                return 'bg-danger';
            case 4: // PENDING
                return 'bg-info';
            default:
                return 'bg-secondary';
        }
    }

    // Helper function to get status text
    function getStatusText(status) {
        switch (status) {
            case 1:
                return 'ACTIVE';
            case 2:
                return 'INACTIVE';
            case 3:
                return 'SUSPENDED';
            case 4:
                return 'PENDING';
            default:
                return 'UNKNOWN';
        }
    }

    // Helper function to escape HTML to prevent XSS
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Function to check if ad account is already selected
    function isAdAccountSelected(accountId) {
        return $('#selectedAdAccountsList').find(`[data-account-id="${accountId}"]`).length > 0;
    }

    // Function to toggle ad account selection
    window.toggleAdAccountSelection = function (event, accountId, accountName, advertiserIdParam, ownerBcId) {
        // Change text content of button to 'Bỏ chọn'
        if (isAdAccountSelected(accountId)) {
            // Remove from selection
            $(`#selectedAdAccountsList [data-account-id="${accountId}"]`).remove();
            event.target.innerHTML = '<i class="fas fa-plus"></i> Chọn';
            // remove btn-danger class from button
            let sourceButton = $(`#adAccountsSearchList button[data-account-id="${accountId}"]`);
            sourceButton.html('<i class="fas fa-plus"></i> Chọn');
            sourceButton.removeClass('btn-danger');
            // remove from selectedAdAccountsState
            selectedAdAccountsState = selectedAdAccountsState.filter(id => id !== accountId);
        } else {
            // Add to selection 
            const selectedItem = `
                <div class="list-group-item d-flex justify-content-between align-items-center" data-account-id="${accountId}">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${accountName}</h6>
                        <small class="text-muted">
                            ID: ${advertiserIdParam} | Owner BC: ${ownerBcId}
                        </small>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSelectedAdAccount('${accountId}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            $('#selectedAdAccountsList').append(selectedItem);

            let sourceButton = $(`#adAccountsSearchList button[data-account-id="${accountId}"]`);
            sourceButton.html('<i class="fas fa-times"></i> Bỏ chọn');
            sourceButton.addClass('btn-danger');
            // add to selectedAdAccountsState
            selectedAdAccountsState.push(accountId);
        }

        updateSelectedAdAccountsCount();
    }

    // Function to remove selected ad account
    window.removeSelectedAdAccount = function removeSelectedAdAccount(accountId) {
        $(`#selectedAdAccountsList [data-account-id="${accountId}"]`).remove();
        updateSelectedAdAccountsCount();
        // publish a event to update selectedAdAccountsState

        const customEvent = new CustomEvent('removeSelectedAdAccount', {
            detail: {
                accountId: accountId
            },
            bubbles: true,    // Event will bubble up through the DOM
            cancelable: true  // Event can be cancelled
        });
        document.dispatchEvent(customEvent);
    }

    // Handle removeSelectedAdAccount event
    document.addEventListener('removeSelectedAdAccount', function (event) {
        const accountId = event.detail.accountId;
        selectedAdAccountsState = selectedAdAccountsState.filter(id => id !== accountId);
        // reset button state at searchResults
        const button = $(`#adAccountsSearchList button[data-account-id="${accountId}"]`);
        button.html('<i class="fas fa-plus"></i> Chọn');
        button.removeClass('btn-danger');
    });

    // Function to update selected ad accounts count
    function updateSelectedAdAccountsCount() {
        const count = $('#selectedAdAccountsList .list-group-item').length;
        $('#selectedAdAccountsCount').text(count);
    }

    // Handle confirm selection button click
    $(document).on('click', '#confirmAdAccountsSelection', function () {
        // Check if user has permission to edit notification rules (for adding consumers)
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            abp.notify.error('Bạn không có quyền chỉnh sửa quy tắc thông báo.');
            return;
        }

        const selectedAccounts = [];

        $('#selectedAdAccountsList .list-group-item').each(function () {
            const accountId = $(this).data('account-id');
            const accountName = $(this).find('h6').text();
            const advertiserIdText = $(this).find('small').text().match(/ID: ([^|]+)/)?.[1]?.trim();
            const ownerBcId = $(this).find('small').text().match(/Owner BC: ([^|]+)/)?.[1]?.trim();

            selectedAccounts.push({
                id: accountId, // Use account.id (Guid) for the API call
                name: accountName,
                advertiserIdText: advertiserIdText,
                ownerBcId: ownerBcId
            });
        });

        if (selectedAccounts.length === 0) {
            abp.notify.warn('Vui lòng chọn ít nhất một tài khoản quảng cáo');
            return;
        }

        // Check if we have the current rule info
        if (!currentRuleIdForConsumers) {
            abp.notify.error('Không thể xác định quy tắc hiện tại. Vui lòng thử lại.');
            return;
        }

        // Add selected accounts to the rule using ABP client proxy
        tikTok.controllers.rules.addAdAccountsToRule({
            ruleId: currentRuleIdForConsumers,
            adAccountIds: selectedAccounts.map(account => account.id) // Use account.id (Guid) for the API call
        })
            .then(function (response) {
                console.log('Response:', response);

                // Show success message
                abp.notify.success(`Đã thêm ${selectedAccounts.length} tài khoản quảng cáo vào quy tắc`);

                // Close the selection modal
                $('#selectAdAccountsModal').modal('hide');

                // Refresh the main rule consumers modal to show updated ad accounts list
                openRuleConsumersModal(currentRuleIdForConsumers, currentRuleNameForConsumers);
            })
            .catch(function (error) {
                console.error('Error adding ad accounts to rule:', error);
                abp.notify.error('Không thể thêm tài khoản quảng cáo vào quy tắc. Vui lòng thử lại.');
            });
    });

    // Global variables to store current rule info for ad account selection
    let currentRuleIdForConsumers = null;
    let currentRuleNameForConsumers = null;

    let consumersData = null;
    let consumersDataViewMode = localStorage.getItem('consumersDataViewMode') || 'detail';
    // Function to open rule consumers modal
    window.openRuleConsumersModal = function openRuleConsumersModal(ruleId, ruleName) {
        // Store current rule info globally for use in ad account selection
        currentRuleIdForConsumers = ruleId;
        currentRuleNameForConsumers = ruleName;

        // Update modal title with rule name
        $('#ruleConsumersModalLabel').text(
            `Đối tượng sử dụng quy tắc: ${ruleName}`
        );

        // Show loading state
        $('#adAccountsList').html(
            '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Đang tải...</div>'
        );
        $('#businessCentersList').html(
            '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Đang tải...</div>'
        );

        // Fetch rule consumers data
        abp.ajax({
            url: abp.appPath + 'api/app/rules/rule-consumers/' + ruleId,
            type: 'GET',
        })
            .then(function (response) {
                consumersData = response;
                window.toggleViewMode(consumersDataViewMode);
                if (consumersDataViewMode !== 'table'){
                    populateConsumersModal(response);
                }


            })
            .catch(function (error) {
                console.error('Error loading rule consumers:', error);
                $('#adAccountsList').html(
                    '<div class="alert alert-danger">Không thể tải danh sách đối tượng sử dụng.</div>'
                );
                $('#businessCentersList').html(
                    '<div class="alert alert-danger">Không thể tải danh sách đối tượng sử dụng.</div>'
                );
            });

        // Show modal
        $('#ruleConsumersModal').modal('show');
    }

    document.addEventListener('consumersSwitchViewMode', function (event) {
        const viewMode = event.detail.viewMode;
        if (viewMode === 'table') {
            consumersDataViewMode = 'table';
            populateConsumersModalWithTableView(consumersData);
        } else {
            consumersDataViewMode = 'detail';
            populateConsumersModal(consumersData);
        }
    });

    // Function to populate consumers modal
    function populateConsumersModal(data) {
        // Populate Ad Accounts
        const adAccounts = data.adAccounts || [];
        const adAccountsList = $('#adAccountsList');
        const adAccountsCount = $('#adAccountsCount');

        adAccountsCount.text(adAccounts.length);

        if (adAccounts.length === 0) {
            adAccountsList.html(`
                <div class="empty-state">
                    <i class="fas fa-ad"></i>
                    <p>Không có tài khoản quảng cáo nào</p>
                </div>
            `);
        } else {
            let adAccountsHtml = '';
            adAccounts.forEach((account) => {
                adAccountsHtml += `
                <div class="col col-md-6 col-lg-4 h-100 p-1">
                    <div class="consumer-item ${account.isOwner ? 'owner-item' : 'disabled'}" data-consumer-id="${account.advertiserId}" data-consumer-type="adAccount">
                        <div class="consumer-header">
                            <div class="d-flex justify-content-between align-items-start w-100">
                                <div class="flex-grow-1">
                                    <h6 class="consumer-name">${account.name || 'N/A'
                    }</h6>
                                    <span class="consumer-detail-value">ID tài khoản quảng cáo: ${account.advertiserId || 'N/A'
                    }</span>
                                </div>
                                <div class="consumer-actions">
                                    ${window.notificationRulePermission && window.notificationRulePermission.canEdit ?
                        `<button type="button" class="btn btn-sm btn-outline-danger delete-consumer-btn" 
                                                title="Xóa tài khoản này" 
                                                onclick="deleteConsumer('${account.id}', 'adAccount', '${account.name || 'N/A'}', '${currentRuleIdForConsumers}')">
                                            <i class="fas fa-trash"></i>
                                        </button>` : ''
                    }
                                </div>
                            </div>
                        </div>
                        <div class="consumer-details">
                            <div class="consumer-detail-item">
                                <span class="consumer-detail-label">ID BC</span>
                                <span class="consumer-detail-value">${account.ownerBcId || 'N/A'
                    }</span>
                            </div>
                        </div>
                        ${window.notificationRulePermission && window.notificationRulePermission.canEdit ?
                        `<div class="consumer-checkbox">
                                <input type="checkbox" class="form-check-input consumer-select-checkbox" 
                                       id="adAccount_${account.id}" 
                                       data-consumer-id="${account.id}" 
                                       data-consumer-type="adAccount">
                                <label class="form-check-label" for="adAccount_${account.id}">
                                    Chọn để xóa
                                </label>
                            </div>` : ''
                    }
                    </div>
                </div>
                `;
            });
            adAccountsList.html(adAccountsHtml);
        }

        // Populate Business Centers
        const businessCenters = data.businessCenters || [];
        const businessCentersList = $('#businessCentersList');
        const businessCentersCount = $('#businessCentersCount');

        businessCentersCount.text(businessCenters.length);

        if (businessCenters.length === 0) {
            businessCentersList.html(`
                <div class="empty-state">
                    <i class="fas fa-building"></i>
                    <p>Không có trung tâm kinh doanh nào</p>
                </div>
            `);
        } else {
            let businessCentersHtml = '';
            businessCenters.forEach((bc) => {
                businessCentersHtml += `
                    <div class="consumer-item" data-consumer-id="${bc.id}" data-consumer-type="businessCenter">
                        <div class="consumer-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="consumer-name">${bc.name || 'N/A'}</h6>
                                    <span class="consumer-id">ID: ${bc.bcId || 'N/A'
                    }</span>
                                </div>
                                <div class="consumer-actions">
                                    ${window.notificationRulePermission && window.notificationRulePermission.canEdit ?
                        `<button type="button" class="btn btn-sm btn-outline-danger delete-consumer-btn" 
                                                title="Xóa trung tâm kinh doanh này" 
                                                onclick="deleteConsumer('${bc.id}', 'businessCenter', '${bc.name || 'N/A'}', '${currentRuleIdForConsumers}')">
                                            <i class="fas fa-trash"></i>
                                        </button>` : ''
                    }
                                </div>
                            </div>
                        </div>
                        <div class="consumer-details">
                            <div class="consumer-detail-item">
                                <span class="consumer-detail-label">Công ty</span>
                                <span class="consumer-detail-value">${bc.company || 'N/A'
                    }</span>
                            </div>
                            <div class="consumer-detail-item">
                                <span class="consumer-detail-label">Tiền tệ</span>
                                <span class="consumer-detail-value">${bc.currency || 'N/A'
                    }</span>
                            </div>
                            <div class="consumer-detail-item">
                                <span class="consumer-detail-label">Khu vực</span>
                                <span class="consumer-detail-value">${bc.registeredArea || 'N/A'
                    }</span>
                            </div>
                            <div class="consumer-detail-item">
                                <span class="consumer-detail-label">BC ID</span>
                                <span class="consumer-detail-value">${bc.id || 'N/A'
                    }</span>
                            </div>
                        </div>
                        ${window.notificationRulePermission && window.notificationRulePermission.canEdit ?
                        `<div class="consumer-checkbox">
                                <input type="checkbox" class="form-check-input consumer-select-checkbox" 
                                       id="businessCenter_${bc.id}" 
                                       data-consumer-id="${bc.id}" 
                                       data-consumer-type="businessCenter">
                                <label class="form-check-label" for="businessCenter_${bc.id}">
                                    Chọn để xóa
                                </label>
                            </div>` : ''
                    }
                    </div>
                `;
            });
            businessCentersList.html(businessCentersHtml);
        }
    }

    // Function to populate consumers modal with table view
    function populateConsumersModalWithTableView(data, validAdAccountIds) {
        // Populate Ad Accounts Table
        let adAccounts = data.adAccounts || [];
        const adAccountsList = $('#adAccountsList');
        const adAccountsCount = $('#adAccountsCount');
        if (validAdAccountIds) {
            adAccounts = adAccounts.filter((account) => validAdAccountIds.includes(account.advertiserId));
        }

        adAccountsCount.text(adAccounts.length);

        if (adAccounts.length === 0) {
            adAccountsList.html(`
                <div class="empty-state">
                    <i class="fas fa-ad"></i>
                    <p>Không có tài khoản quảng cáo nào</p>
                </div>
            `);
        } else {
            let adAccountsTableHtml = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Chọn</th>
                                <th>Tên tài khoản</th>
                                <th>ID tài khoản quảng cáo</th>
                                <th>ID BC</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            adAccounts.forEach((account) => {
                adAccountsTableHtml += `
                    <tr class="consumer-item ${account.isOwner ? 'table-warning' : ''}" data-consumer-id="${account.advertiserId}" data-consumer-type="adAccount">
                        <td>
                            ${window.notificationRulePermission && window.notificationRulePermission.canEdit ?
                        `<input type="checkbox" class="form-check-input consumer-select-checkbox" 
                                       id="adAccount_${account.id}" 
                                       data-consumer-id="${account.id}" 
                                       data-consumer-type="adAccount">` :
                        '<span class="text-muted">-</span>'
                    }
                        </td>
                        <td>
                            <strong>${account.name || 'N/A'}</strong>
                        </td>
                        <td>${account.advertiserId || 'N/A'}</td>
                        <td>${account.ownerBcId || 'N/A'}</td>
                        <td>
                            ${account.isOwner ?
                        '<span class="badge bg-warning text-dark">Chủ sở hữu</span>' :
                        '<span class="badge bg-secondary">Thành viên</span>'
                    }
                        </td>
                        <td>
                            ${window.notificationRulePermission && window.notificationRulePermission.canEdit ?
                        `<button type="button" class="btn btn-sm btn-outline-danger delete-consumer-btn" 
                                        title="Xóa tài khoản này" 
                                        onclick="deleteConsumer('${account.id}', 'adAccount', '${account.name || 'N/A'}', '${currentRuleIdForConsumers}')">
                                    <i class="fas fa-trash"></i>
                                </button>` :
                        '<span class="text-muted">-</span>'
                    }
                        </td>
                    </tr>
                `;
            });

            adAccountsTableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            adAccountsList.html(adAccountsTableHtml);
        }

        // Populate Business Centers Table
        const businessCenters = data.businessCenters || [];
        const businessCentersList = $('#businessCentersList');
        const businessCentersCount = $('#businessCentersCount');

        businessCentersCount.text(businessCenters.length);

        if (businessCenters.length === 0) {
            businessCentersList.html(`
                <div class="empty-state">
                    <i class="fas fa-building"></i>
                    <p>Không có trung tâm kinh doanh nào</p>
                </div>
            `);
        } else {
            let businessCentersTableHtml = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Chọn</th>
                                <th>Tên trung tâm</th>
                                <th>ID</th>
                                <th>Công ty</th>
                                <th>Tiền tệ</th>
                                <th>Khu vực</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            businessCenters.forEach((bc) => {
                businessCentersTableHtml += `
                    <tr data-consumer-id="${bc.id}" data-consumer-type="businessCenter">
                        <td>
                            ${window.notificationRulePermission && window.notificationRulePermission.canEdit ?
                        `<input type="checkbox" class="form-check-input consumer-select-checkbox" 
                                       id="businessCenter_${bc.id}" 
                                       data-consumer-id="${bc.id}" 
                                       data-consumer-type="businessCenter">` :
                        '<span class="text-muted">-</span>'
                    }
                        </td>
                        <td>
                            <strong>${bc.name || 'N/A'}</strong>
                        </td>
                        <td>${bc.bcId || 'N/A'}</td>
                        <td>${bc.company || 'N/A'}</td>
                        <td>${bc.currency || 'N/A'}</td>
                        <td>${bc.registeredArea || 'N/A'}</td>
                        <td>
                            ${window.notificationRulePermission && window.notificationRulePermission.canEdit ?
                        `<button type="button" class="btn btn-sm btn-outline-danger delete-consumer-btn" 
                                        title="Xóa trung tâm kinh doanh này" 
                                        onclick="deleteConsumer('${bc.id}', 'businessCenter', '${bc.name || 'N/A'}', '${currentRuleIdForConsumers}')">
                                    <i class="fas fa-trash"></i>
                                </button>` :
                        '<span class="text-muted">-</span>'
                    }
                        </td>
                    </tr>
                `;
            });

            businessCentersTableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            businessCentersList.html(businessCentersTableHtml);
        }
    }

    // Function to delete a rule
    window.deleteRule = function deleteRule(ruleId, ruleName) {
        // Check if user has permission to delete notification rules
        if (!window.notificationRulePermission || !window.notificationRulePermission.canDelete) {
            abp.notify.error('Bạn không có quyền xóa quy tắc thông báo.');
            return;
        }

        const confirmMessage = `Bạn có chắc chắn muốn xóa quy tắc "${ruleName}"?`;

        abp.message.confirm(confirmMessage, function (isConfirmed) {
            if (isConfirmed) {
                abp.ajax({
                    url: abp.appPath + 'api/app/rules/' + ruleId,
                    type: 'DELETE',
                }).then(function () {
                    abp.notify.info(l('SuccessfullyDeleted'));
                    dataTable.ajax.reload();
                });
            }
        });
    }

    // Function to delete a single consumer
    window.deleteConsumer = function deleteConsumer(consumerId, consumerType, consumerName, ruleId) {
        // Check if user has permission to edit notification rules (for removing consumers)
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            abp.notify.error('Bạn không có quyền chỉnh sửa quy tắc thông báo.');
            return;
        }

        const typeText = consumerType === 'adAccount' ? 'tài khoản quảng cáo' : 'trung tâm kinh doanh';
        const confirmMessage = `Bạn có chắc chắn muốn xóa ${typeText} "${consumerName}"?`;

        abp.message.confirm(confirmMessage, function (isConfirmed) {
            if (isConfirmed) {
                if (consumerType === 'adAccount') {
                    // Call API to remove ad account from rule
                    if (ruleId) {
                        const adAccountIds = [consumerId];

                        abp.ui.setBusy();
                        tikTok.controllers.rules.deleteAdAccountsFromRule(ruleId, adAccountIds)
                            .done(function () {
                                // Remove from UI on success
                                const consumerElement = document.querySelector(`[data-consumer-id="${consumerId}"][data-consumer-type="${consumerType}"]`);
                                if (consumerElement) {
                                    let removeResponse = consumerElement.closest('.col')?.remove();
                                    if (!removeResponse) {
                                        consumerElement.closest('.consumer-item')?.remove();
                                    }
                                    updateConsumerCounts();
                                    updateDeleteButtons();
                                }
                                // abp.notify.success('Đã xóa tài khoản quảng cáo khỏi quy tắc thành công');
                            })
                            .fail(function (error) {
                                console.error('Error deleting ad account from rule:', error);
                                abp.notify.error('Không thể xóa tài khoản quảng cáo khỏi quy tắc. Vui lòng thử lại.');
                            })
                            .always(function () {
                                abp.ui.clearBusy();
                            });
                    } else {
                        abp.notify.error('Không thể xác định quy tắc để xóa tài khoản quảng cáo');
                    }
                } else if (consumerType === 'businessCenter') {
                    // TODO: Implement business center deletion when API is available
                    // For now, just remove from UI
                    const consumerElement = document.querySelector(`[data-consumer-id="${consumerId}"][data-consumer-type="${consumerType}"]`);
                    if (consumerElement) {
                        consumerElement.closest('.col').remove();
                        updateConsumerCounts();
                        updateDeleteButtons();
                    }
                    abp.notify.info('Đã xóa trung tâm kinh doanh khỏi quy tắc thành công');
                }
            }
        });
    }

    // Function to delete multiple selected consumers
    function deleteSelectedConsumers(consumerType, ruleId) {
        // Check if user has permission to edit notification rules (for removing consumers)
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            abp.notify.error('Bạn không có quyền chỉnh sửa quy tắc thông báo.');
            return;
        }

        const selectedCheckboxes = document.querySelectorAll(`.consumer-select-checkbox[data-consumer-type="${consumerType}"]:checked`);

        if (selectedCheckboxes.length === 0) {
            abp.notify.warn('Vui lòng chọn ít nhất một mục để xóa');
            return;
        }

        const typeText = consumerType === 'adAccount' ? 'tài khoản quảng cáo' : 'trung tâm kinh doanh';
        const confirmMessage = `Bạn có chắc chắn muốn xóa ${selectedCheckboxes.length} ${typeText} đã chọn?`;

        abp.message.confirm(confirmMessage, function (isConfirmed) {
            if (isConfirmed) {
                if (consumerType === 'adAccount') {
                    // Call API to remove multiple ad accounts from rule
                    if (ruleId) {
                        const adAccountIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.dataset.consumerId);

                        abp.ui.setBusy();
                        tikTok.controllers.rules.deleteAdAccountsFromRule(ruleId, adAccountIds)
                            .done(function () {
                                // Remove from UI on success
                                selectedCheckboxes.forEach(checkbox => {
                                    const consumerId = checkbox.dataset.consumerId;
                                    const consumerElement = document.querySelector(`[data-consumer-id="${consumerId}"][data-consumer-type="${consumerType}"]`);
                                    if (consumerElement) {
                                        let removeResponse = consumerElement.closest('.col')?.remove();
                                        if (!removeResponse) {
                                            consumerElement.closest('.consumer-item')?.remove();
                                        }
                                    }
                                });

                                updateConsumerCounts();
                                updateDeleteButtons();
                                // abp.notify.success(`Đã xóa ${selectedCheckboxes.length} tài khoản quảng cáo khỏi quy tắc thành công`);
                            })
                            .fail(function (error) {
                                console.error('Error deleting ad accounts from rule:', error);
                                abp.notify.error('Không thể xóa tài khoản quảng cáo khỏi quy tắc. Vui lòng thử lại.');
                            })
                            .always(function () {
                                abp.ui.clearBusy();
                            });
                    } else {
                        abp.notify.error('Không thể xác định quy tắc để xóa tài khoản quảng cáo');
                    }
                } else if (consumerType === 'businessCenter') {
                    // TODO: Implement business center deletion when API is available
                    // For now, just remove from UI
                    selectedCheckboxes.forEach(checkbox => {
                        const consumerId = checkbox.dataset.consumerId;
                        const consumerElement = document.querySelector(`[data-consumer-id="${consumerId}"][data-consumer-type="${consumerType}"]`);
                        if (consumerElement) {
                            consumerElement.closest('.col').remove();
                        }
                    });

                    updateConsumerCounts();
                    updateDeleteButtons();
                    abp.notify.info(`Đã xóa ${selectedCheckboxes.length} trung tâm kinh doanh khỏi quy tắc thành công`);
                }
            }
        });
    }

    // Function to update consumer counts
    function updateConsumerCounts() {
        let adAccountsCount = document.querySelectorAll('div[data-consumer-type="adAccount"]').length;
        const businessCentersCount = document.querySelectorAll('[data-consumer-type="businessCenter"]').length;

        if (getModeDisplay() == 'table') {
            adAccountsCount = document.querySelectorAll('tr[data-consumer-type="adAccount"]').length;
        }

        $('#adAccountsCount').text(adAccountsCount);
        $('#businessCentersCount').text(businessCentersCount);
    }

    function getModeDisplay() {
        var mode = $('#ruleConsumersViewModeDropdown li a.active').attr('data-mode');
        return mode;
    }

    // Function to update delete buttons visibility
    function updateDeleteButtons() {
        // Check if user has permission to edit notification rules
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            $('#deleteSelectedAdAccountsBtn').addClass('d-none');
            $('#deleteSelectedBusinessCentersBtn').addClass('d-none');
            return;
        }

        const adAccountsSelected = document.querySelectorAll('.consumer-select-checkbox[data-consumer-type="adAccount"]:checked').length;
        const businessCentersSelected = document.querySelectorAll('.consumer-select-checkbox[data-consumer-type="businessCenter"]:checked').length;

        $('#deleteSelectedAdAccountsBtn').toggleClass('d-none', adAccountsSelected === 0);
        $('#deleteSelectedBusinessCentersBtn').toggleClass('d-none', businessCentersSelected === 0);
    }

    // Function to update consumer item selection styling
    function updateConsumerItemSelection() {
        document.querySelectorAll('.consumer-select-checkbox').forEach(checkbox => {
            const consumerItem = checkbox.closest('.consumer-item');
            if (checkbox.checked) {
                consumerItem.classList.add('selected-for-deletion');
            } else {
                consumerItem.classList.remove('selected-for-deletion');
            }
        });
    }

    // Event handlers for checkboxes
    $(document).on('change', '.consumer-select-checkbox', function () {
        updateDeleteButtons();
        updateConsumerItemSelection();
    });

    // Event handlers for delete buttons
    $('#deleteSelectedAdAccountsBtn').on('click', function () {
        // Check if user has permission to edit notification rules (for removing consumers)
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            abp.notify.error('Bạn không có quyền chỉnh sửa quy tắc thông báo.');
            return;
        }
        deleteSelectedConsumers('adAccount', currentRuleIdForConsumers);
    });

    $('#deleteSelectedBusinessCentersBtn').on('click', function () {
        // Check if user has permission to edit notification rules (for removing consumers)
        if (!window.notificationRulePermission || !window.notificationRulePermission.canEdit) {
            abp.notify.error('Bạn không có quyền chỉnh sửa quy tắc thông báo.');
            return;
        }
        deleteSelectedConsumers('businessCenter', currentRuleIdForConsumers);
    });

    // Event handlers for select all buttons
    $('#selectAllAdAccountsBtn').on('click', function () {
        toggleSelectAll('adAccount');
    });

    $('#selectAllBusinessCentersBtn').on('click', function () {
        toggleSelectAll('businessCenter');
    });

    // Function to toggle select all functionality
    function toggleSelectAll(consumerType) {
        const checkboxes = document.querySelectorAll(`.consumer-item:not(.disabled) .consumer-select-checkbox[data-consumer-type="${consumerType}"]`);
        const selectAllBtn = document.getElementById(`selectAll${consumerType.charAt(0).toUpperCase() + consumerType.slice(1)}sBtn`);

        if (checkboxes.length === 0) return;

        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        if (allChecked) {
            // Uncheck all
            checkboxes.forEach(cb => cb.checked = false);
            selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> Chọn tất cả';
            selectAllBtn.classList.remove('btn-secondary');
            selectAllBtn.classList.add('btn-outline-secondary');
        } else {
            // Check all
            checkboxes.forEach(cb => cb.checked = true);
            selectAllBtn.innerHTML = '<i class="fas fa-square"></i> Bỏ chọn tất cả';
            selectAllBtn.classList.remove('btn-outline-secondary');
            selectAllBtn.classList.add('btn-secondary');
        }

        updateDeleteButtons();
        updateConsumerItemSelection();
    }
});
