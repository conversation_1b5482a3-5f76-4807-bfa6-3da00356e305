using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTok.Repositories;
using TikTok.RawGmvMaxProductCreativeReports;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Authorization.Permissions;
using TikTok.Enums;
using TikTok.Permissions;
using TikTok.FactGmvMaxProducts;

namespace TikTok.RawGmvMaxProductCreativeReports
{
    /// <summary>
    /// Application Service implementation cho RawGmvMaxProductCreativeReport
    /// </summary>
    public class RawGmvMaxProductCreativeReportAppService : ApplicationService, IRawGmvMaxProductCreativeReportAppService, ITransientDependency
    {
        private readonly IRawGmvMaxProductCreativeReportDapperRepository _dapperRepository;
        private readonly IPermissionChecker _permissionChecker;
        private readonly PermissionFieldHelper _permissionFieldHelper;

        public RawGmvMaxProductCreativeReportAppService(
            IRawGmvMaxProductCreativeReportDapperRepository dapperRepository,
            IPermissionChecker permissionChecker,
            PermissionFieldHelper permissionFieldHelper)
        {
            _dapperRepository = dapperRepository;
            _permissionChecker = permissionChecker;
            _permissionFieldHelper = permissionFieldHelper;
        }

        /// <summary>
        /// Lấy danh sách RawGmvMaxProductCreativeReport với phân trang và lọc
        /// </summary>
        /// <param name="input">Thông tin phân trang và lọc</param>
        /// <returns>Danh sách DTO với thông tin phân trang</returns>
        public async Task<PagedResultDto<RawGmvMaxProductCreativeReportDto>> GetListAsync(
            GetRawGmvMaxProductCreativeReportListDto input)
        {
            try
            {
                // Check permissions
                var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // Get user's allowed advertiser IDs for filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Getting RawGmvMaxProductCreativeReport list with filters: {@Input}", input);

                // Gọi DapperRepository với advertiser filtering
                var entities = await _dapperRepository.GetListAsync(
                    searchText: input.SearchText,
                    fromDate: input.FromDate,
                    toDate: input.ToDate,
                    campaignId: input.CampaignId,
                    campaignIds: input.CampaignIds,
                    shopContentTypes: input.ShopContentTypes,
                    creativeDeliveryStatuses: input.CreativeDeliveryStatuses,
                    allowedAdvertiserIds: allowedAdvertiserIds,
                    skipCount: input.SkipCount,
                    maxResultCount: input.MaxResultCount,
                    sorting: input.Sorting
                );

                // Lấy tổng số bản ghi với advertiser filtering
                var totalCount = await _dapperRepository.GetCountAsync(
                    searchText: input.SearchText,
                    fromDate: input.FromDate,
                    toDate: input.ToDate,
                    campaignId: input.CampaignId,
                    campaignIds: input.CampaignIds,
                    shopContentTypes: input.ShopContentTypes,
                    creativeDeliveryStatuses: input.CreativeDeliveryStatuses,
                    allowedAdvertiserIds: allowedAdvertiserIds
                );

                // Convert entity sang DTO
                var dtoItems = entities.Select(MapToDto).ToList();

                // Trả về kết quả với DTO
                return new PagedResultDto<RawGmvMaxProductCreativeReportDto>(
                    totalCount,
                    dtoItems
                );
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting RawGmvMaxProductCreativeReport list");
                throw;
            }
        }

        /// <summary>
        /// Lấy thông tin chi tiết RawGmvMaxProductCreativeReport theo ID
        /// </summary>
        /// <param name="id">ID của entity</param>
        /// <returns>DTO hoặc null nếu không tìm thấy</returns>
        public async Task<RawGmvMaxProductCreativeReportDto?> GetAsync(Guid id)
        {
            try
            {
                Logger.LogInformation("Getting RawGmvMaxProductCreativeReport by ID: {Id}", id);

                // Gọi DapperRepository để lấy entity
                var entity = await _dapperRepository.GetAsync(id);

                if (entity == null)
                {
                    Logger.LogWarning("RawGmvMaxProductCreativeReport not found with ID: {Id}", id);
                    return null;
                }

                // Convert entity sang DTO
                return MapToDto(entity);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting RawGmvMaxProductCreativeReport by ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách Campaign IDs để sử dụng trong dropdown filter
        /// </summary>
        /// <returns>Danh sách Campaign IDs</returns>
        public async Task<List<string>> GetCampaignIdsAsync()
        {
            try
            {
                // Check permissions
                var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // Get user's allowed advertiser IDs for filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Getting Campaign IDs for dropdown filter");

                var campaignIds = await _dapperRepository.GetCampaignIdsAsync(allowedAdvertiserIds);

                Logger.LogInformation("Retrieved {Count} Campaign IDs", campaignIds.Count);

                return campaignIds;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting Campaign IDs");
                throw;
            }
        }

        /// <summary>
        /// Map entity sang DTO
        /// </summary>
        /// <param name="entity">Entity cần map</param>
        /// <returns>DTO đã được map</returns>
        private static RawGmvMaxProductCreativeReportDto MapToDto(Entities.RawGmvMaxProductCreativeReportEntity entity)
        {
            return new RawGmvMaxProductCreativeReportDto
            {
                Id = entity.Id,
                CreationTime = entity.CreationTime,
                CreatorId = entity.CreatorId,
                LastModificationTime = entity.LastModificationTime,
                LastModifierId = entity.LastModifierId,
                BcId = entity.BcId,
                AdvertiserId = entity.AdvertiserId,
                StoreId = entity.StoreId,
                CampaignId = entity.CampaignId,
                ItemGroupId = entity.ItemGroupId,
                ItemId = entity.ItemId,
                CreativeType = entity.CreativeType,
                Title = entity.Title,
                TtAccountName = entity.TtAccountName,
                TtAccountProfileImageUrl = entity.TtAccountProfileImageUrl,
                TtAccountAuthorizationType = entity.TtAccountAuthorizationType,
                ShopContentType = entity.ShopContentType,
                Orders = entity.Orders,
                GrossRevenue = entity.GrossRevenue,
                ProductImpressions = entity.ProductImpressions,
                ProductClicks = entity.ProductClicks,
                ProductClickRate = entity.ProductClickRate,
                AdClickRate = entity.AdClickRate,
                AdConversionRate = entity.AdConversionRate,
                AdVideoViewRate2s = entity.AdVideoViewRate2s,
                AdVideoViewRate6s = entity.AdVideoViewRate6s,
                AdVideoViewRateP25 = entity.AdVideoViewRateP25,
                AdVideoViewRateP50 = entity.AdVideoViewRateP50,
                AdVideoViewRateP75 = entity.AdVideoViewRateP75,
                AdVideoViewRateP100 = entity.AdVideoViewRateP100,
                Currency = entity.Currency,
                Date = entity.Date,
                ROI = entity.ROI,
                CostPerOrder = entity.CostPerOrder,
                Cost = entity.Cost,
                CreativeDeliveryStatus = entity.CreativeDeliveryStatus
            };
        }

        /// <summary>
        /// Lấy thống kê tổng hợp video theo trạng thái
        /// </summary>
        /// <param name="input">Thông tin lọc</param>
        /// <returns>Thống kê video theo trạng thái</returns>
        public async Task<VideoStatusStatisticsDto> GetVideoStatusStatisticsAsync(
            GetRawGmvMaxProductCreativeReportListDto input)
        {
            try
            {
                // Check permissions
                var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // Get user's allowed advertiser IDs for filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Getting video status statistics with filters: {@Input}", input);

                // Lấy thống kê từ repository với advertiser filtering
                var statusCounts = await _dapperRepository.GetVideoStatusStatisticsAsync(
                    searchText: input.SearchText,
                    fromDate: input.FromDate,
                    toDate: input.ToDate,
                    campaignIds: input.CampaignIds,
                    shopContentTypes: input.ShopContentTypes,
                    allowedAdvertiserIds: allowedAdvertiserIds
                );

                var totalCount = statusCounts.Sum(x => x.Count);
                var problematicStatuses = new[] { CreativeDeliveryStatus.NOT_DELIVERYIN, CreativeDeliveryStatus.EXCLUDED, 
                                                CreativeDeliveryStatus.UNAVAILABLE, CreativeDeliveryStatus.REJECTED };
                var problematicCount = statusCounts
                    .Where(x => problematicStatuses.Contains(x.Status))
                    .Sum(x => x.Count);
                var healthyCount = totalCount - problematicCount;

                // ✅ Chỉ tạo status cards cho 4 trạng thái problematic
                var statusCards = new List<VideoStatusCardDto>();
                foreach (var problematicStatus in problematicStatuses)
                {
                    var statusCount = statusCounts.FirstOrDefault(x => x.Status == problematicStatus);
                    var count = statusCount?.Count ?? 0;
                    
                    statusCards.Add(new VideoStatusCardDto
                    {
                        Status = (int)problematicStatus,
                        Name = GetStatusDisplayName(problematicStatus),
                        Icon = GetStatusIcon(problematicStatus),
                        Color = GetStatusColor(problematicStatus),
                        Count = count,
                        Percentage = totalCount > 0 ? Math.Round((decimal)count / totalCount * 100, 1) : 0
                    });
                }

                // Tạo summary
                var summary = new VideoStatisticsSummaryDto
                {
                    Total = totalCount,
                    ProblematicCount = problematicCount,
                    ProblematicPercentage = totalCount > 0 ? Math.Round((decimal)problematicCount / totalCount * 100, 1) : 0,
                    HealthyCount = healthyCount,
                    HealthyPercentage = totalCount > 0 ? Math.Round((decimal)healthyCount / totalCount * 100, 1) : 0
                };

                return new VideoStatusStatisticsDto
                {
                    StatusCards = statusCards,
                    Summary = summary
                };
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting video status statistics");
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách video cần xử lý (chỉ các trạng thái problematic)
        /// </summary>
        /// <param name="input">Thông tin phân trang và lọc</param>
        /// <returns>Danh sách video cần xử lý với phân trang</returns>
        public async Task<PagedResultDto<RawGmvMaxProductCreativeReportDto>> GetProblematicVideosAsync(
            GetRawGmvMaxProductCreativeReportListDto input)
        {
            try
            {
                Logger.LogInformation("Getting problematic videos with filters: {@Input}", input);

                // Filter chỉ các trạng thái problematic
                var problematicStatuses = new List<CreativeDeliveryStatus>
                {
                    CreativeDeliveryStatus.NOT_DELIVERYIN,
                    CreativeDeliveryStatus.EXCLUDED,
                    CreativeDeliveryStatus.UNAVAILABLE,
                    CreativeDeliveryStatus.REJECTED
                };

                // Override input với problematic statuses
                var problematicInput = new GetRawGmvMaxProductCreativeReportListDto
                {
                    SearchText = input.SearchText,
                    FromDate = input.FromDate,
                    ToDate = input.ToDate,
                    CampaignId = input.CampaignId,
                    CampaignIds = input.CampaignIds,
                    ShopContentTypes = input.ShopContentTypes,
                    CreativeDeliveryStatuses = problematicStatuses,
                    SkipCount = input.SkipCount,
                    MaxResultCount = input.MaxResultCount,
                    Sorting = input.Sorting ?? "Date desc" // Default sort by date descending
                };

                // Gọi method GetListAsync với filter problematic
                return await GetListAsync(problematicInput);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting problematic videos");
                throw;
            }
        }

        #region Helper Methods

        private static string GetStatusDisplayName(CreativeDeliveryStatus status)
        {
            return status switch
            {
                CreativeDeliveryStatus.IN_QUEUE => "In Queue",
                CreativeDeliveryStatus.LEARNING => "Learning",
                CreativeDeliveryStatus.DELIVERING => "Delivering",
                CreativeDeliveryStatus.NOT_DELIVERYIN => "Not Delivery",
                CreativeDeliveryStatus.AUTHORIZATION_NEEDED => "Authorization Needed",
                CreativeDeliveryStatus.EXCLUDED => "Excluded",
                CreativeDeliveryStatus.UNAVAILABLE => "Unavailable",
                CreativeDeliveryStatus.REJECTED => "Rejected",
                _ => $"Status {(int)status}"
            };
        }

        private static string GetStatusIcon(CreativeDeliveryStatus status)
        {
            return status switch
            {
                CreativeDeliveryStatus.IN_QUEUE => "<i class=\"fas fa-hourglass-half\"></i>",
                CreativeDeliveryStatus.LEARNING => "<i class=\"fas fa-graduation-cap\"></i>",
                CreativeDeliveryStatus.DELIVERING => "<i class=\"fas fa-broadcast-tower\"></i>",
                CreativeDeliveryStatus.NOT_DELIVERYIN => "<i class=\"fas fa-pause-circle\"></i>",
                CreativeDeliveryStatus.AUTHORIZATION_NEEDED => "<i class=\"fas fa-user-check\"></i>",
                CreativeDeliveryStatus.EXCLUDED => "<i class=\"fas fa-times-circle\"></i>",
                CreativeDeliveryStatus.UNAVAILABLE => "<i class=\"fas fa-exclamation-circle\"></i>",
                CreativeDeliveryStatus.REJECTED => "<i class=\"fas fa-ban\"></i>",
                _ => "<i class=\"fas fa-question-circle\"></i>"
            };
        }

        private static string GetStatusColor(CreativeDeliveryStatus status)
        {
            return status switch
            {
                CreativeDeliveryStatus.IN_QUEUE => "warning",
                CreativeDeliveryStatus.LEARNING => "info",
                CreativeDeliveryStatus.DELIVERING => "success",
                CreativeDeliveryStatus.NOT_DELIVERYIN => "danger",
                CreativeDeliveryStatus.AUTHORIZATION_NEEDED => "warning",
                CreativeDeliveryStatus.EXCLUDED => "danger",
                CreativeDeliveryStatus.UNAVAILABLE => "secondary",
                CreativeDeliveryStatus.REJECTED => "danger",
                _ => "secondary"
            };
        }

        #endregion
    }
}
