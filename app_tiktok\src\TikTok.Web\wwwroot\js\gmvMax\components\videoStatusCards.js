/**
 * Video Status Cards Component
 * Handles video status analysis cards display and interactions
 */

class VideoStatusCardsComponent {
    constructor(videoTabManager = null) {
        this.currentData = null;
        this.videoTabManager = videoTabManager; // Reference to video tab for filtering

        // ✅ CONSTANTS: Only these 4 problematic statuses are allowed in UI
        this.ALLOWED_STATUSES = [3, 5, 6, 7]; // NOT_DELIVERYIN, EXCLUDED, UNAVAILABLE, REJECTED
    }

    /**
     * Render video status cards
     */
    async render(statusAnalysis, containerId) {
        try {
            this.currentData = statusAnalysis;
            const container = document.getElementById(containerId);

            if (!container) {
                throw new Error(`Container ${containerId} not found`);
            }

            // Generate status cards HTML
            const cardsHtml = this.generateCardsHtml(statusAnalysis);
            container.innerHTML = cardsHtml;

            // Setup card click handlers
            this.setupCardClickHandlers();
        } catch (error) {
            console.error('❌ Error rendering video status cards:', error);
            throw error;
        }
    }

    /**
     * Generate status cards HTML
     */
    generateCardsHtml(statusAnalysis) {
        const { statusCards, summary } = statusAnalysis;

        if (!statusCards || statusCards.length === 0) {
            return `
                <div class="col-12">
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle"></i>
                        Không có dữ liệu video trong khoảng thời gian này.
                    </div>
                </div>
            `;
        }

        // ✅ DOUBLE ENSURE: Only show allowed problematic statuses
        const filteredStatusCards = statusCards.filter((card) =>
            this.ALLOWED_STATUSES.includes(card.status)
        );

        // Generate individual status cards
        const statusCardsHtml = filteredStatusCards
            .map((card) => {
                const cardClass = this.getStatusCardClass(card.statusKey);
                return `
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="card video-status-card ${cardClass} fade-in-up"
                             data-status="${card.status}"
                             data-status-key="${card.statusKey}"
                             style="cursor: pointer;"
                             title="Nhấn để lọc bảng theo trạng thái ${card.name}">
                            <div class="card-body">
                                <div class="status-icon">${card.icon}</div>
                                <div class="status-count">${card.count}</div>
                                <div class="status-name">${card.name}</div>
                            </div>
                        </div>
                    </div>
                `;
            })
            .join('');

        return `
            <div class="row">
                ${statusCardsHtml}
            </div>
        `;
    }

    /**
     * Get status card CSS class
     */
    getStatusCardClass(statusKey) {
        const classMap = {
            IN_QUEUE: 'status-in-queue',
            LEARNING: 'status-learning',
            DELIVERING: 'status-delivering',
            NOT_DELIVERYIN: 'status-not-delivery',
            AUTHORIZATION_NEEDED: 'status-auth-needed',
            EXCLUDED: 'status-excluded',
            UNAVAILABLE: 'status-unavailable',
            REJECTED: 'status-rejected',
        };

        return classMap[statusKey] || 'status-unknown';
    }

    /**
     * Setup card click handlers
     */
    setupCardClickHandlers() {
        const statusCards = document.querySelectorAll('.video-status-card');
        statusCards.forEach((card) => {
            card.addEventListener('click', () => {
                const status = parseInt(card.dataset.status);
                const statusKey = card.dataset.statusKey;
                this.filterTableByStatus(status, statusKey);
            });
        });
    }

    /**
     * Filter table by status
     */
    filterTableByStatus(status, statusKey) {
        try {
            if (!this.videoTabManager) {
                console.warn('Video tab manager not available for filtering');
                return;
            }

            console.log(
                `🎯 Filtering table by status: ${statusKey} (${status})`
            );

            // Apply status filter to video tab
            this.videoTabManager.filterByStatus(status);

            // Show feedback
            this.showFilterFeedback(statusKey);
        } catch (error) {
            console.error('❌ Error filtering table by status:', error);
        }
    }

    /**
     * Show filter feedback
     */
    showFilterFeedback(statusKey) {
        // Visual feedback on the clicked card
        const clickedCard = document.querySelector(
            `[data-status-key="${statusKey}"]`
        );
        if (clickedCard) {
            clickedCard.style.transform = 'scale(0.95)';
            setTimeout(() => {
                clickedCard.style.transform = 'scale(1)';
            }, 150);
        }

        // Show toast notification
        if (window.showInfoMessage) {
            window.showInfoMessage(
                `Đã lọc bảng theo trạng thái: ${this.getStatusDisplayName(
                    statusKey
                )}`
            );
        }
    }

    /**
     * Get status display name
     */
    getStatusDisplayName(statusKey) {
        const statusNames = {
            NOT_DELIVERYIN: 'Not Delivery',
            EXCLUDED: 'Excluded',
            UNAVAILABLE: 'Unavailable',
            REJECTED: 'Rejected',
        };
        return statusNames[statusKey] || statusKey;
    }

    /**
     * Show status modal with video list
     */
    showStatusModal(statusCard) {
        const modalContent = this.generateStatusModalContent(statusCard);

        // Create or update modal
        let modal = document.getElementById('videoStatusModal');
        if (!modal) {
            modal = this.createStatusModal();
        }

        const modalTitle = modal.querySelector('.modal-title');
        const modalBody = modal.querySelector('.modal-body');

        if (modalTitle) {
            modalTitle.innerHTML = `
                <i class="fas fa-video"></i> 
                ${statusCard.icon} ${statusCard.name} 
                <span class="badge bg-primary">${statusCard.count}</span>
            `;
        }

        if (modalBody) {
            modalBody.innerHTML = modalContent;
        }

        // Show modal
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }

    /**
     * Create status modal if it doesn't exist
     */
    createStatusModal() {
        const modalHtml = `
            <div class="modal fade" id="videoStatusModal" tabindex="-1" aria-labelledby="videoStatusModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="videoStatusModalLabel"></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body"></div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        return document.getElementById('videoStatusModal');
    }

    /**
     * Generate status modal content
     */
    generateStatusModalContent(statusCard) {
        if (!statusCard.items || statusCard.items.length === 0) {
            return `
                <div class="text-center p-4">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h5>Không có video</h5>
                    <p class="text-muted">Không có video nào ở trạng thái này trong khoảng thời gian được chọn.</p>
                </div>
            `;
        }

        const tableRows = statusCard.items
            .slice(0, 50) // Limit to 50 items for performance
            .map((video) => {
                const title = video.title || 'Unknown Title';
                const campaignName = video.campaignId || 'Unknown Campaign';
                const ttAccountName = video.ttAccountName || 'Unknown Account';
                const orders = video.orders || 0;
                const grossRevenue = this.formatCurrency(
                    video.grossRevenue || 0
                );

                return `
                    <tr>
                        <td>
                            <div class="fw-bold">${this.escapeHtml(title)}</div>
                            <small class="text-muted">${this.escapeHtml(
                                ttAccountName
                            )}</small>
                        </td>
                        <td>
                            <div>${this.escapeHtml(campaignName)}</div>
                            <small class="text-muted">${
                                video.campaignId
                            }</small>
                        </td>
                        <td class="text-center">${orders}</td>
                        <td class="text-end">${grossRevenue}</td>
                    </tr>
                `;
            })
            .join('');

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Tiêu đề Video</th>
                            <th>Campaign</th>
                            <th class="text-center">Đơn hàng</th>
                            <th class="text-end">Doanh thu</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
            
            ${
                statusCard.items.length > 50
                    ? `<div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    Hiển thị 50/${statusCard.items.length} video đầu tiên. 
                    Sử dụng bộ lọc để xem chi tiết hơn.
                </div>`
                    : ''
            }
        `;
    }

    /**
     * Format currency
     */
    formatCurrency(amount) {
        if (!amount || amount === 0) return '$0';

        if (amount >= 1000000) {
            return '$' + (amount / 1000000).toFixed(1) + 'M';
        } else if (amount >= 1000) {
            return '$' + (amount / 1000).toFixed(1) + 'K';
        } else {
            return '$' + amount.toFixed(2);
        }
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Update status cards with new data
     */
    update(statusAnalysis) {
        this.currentData = statusAnalysis;

        // ✅ ENSURE: Only update allowed problematic statuses
        // Update card counts only for allowed statuses
        statusAnalysis.statusCards
            .filter((card) => this.ALLOWED_STATUSES.includes(card.status))
            .forEach((card) => {
                const cardElement = document.querySelector(
                    `[data-status="${card.status}"] .status-count`
                );
                if (cardElement) {
                    cardElement.textContent = card.count;
                }
            });
    }

    /**
     * Destroy component
     */
    destroy() {
        this.currentData = null;

        // Remove modal if created
        const modal = document.getElementById('videoStatusModal');
        if (modal) {
            modal.remove();
        }
    }
}
