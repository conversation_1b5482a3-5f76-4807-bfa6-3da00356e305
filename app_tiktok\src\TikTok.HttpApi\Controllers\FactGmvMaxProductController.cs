using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using TikTok.FactGmvMaxProducts;
using TikTok.FactGmvMaxProducts.Dtos;
using TikTok.Facts.FactGmvMaxProduct;
using TikTok.Permissions;

namespace TikTok.HttpApi.Controllers
{
    [Route("api/fact-gmv-max-product")]
    public class FactGmvMaxProductController : AbpControllerBase
    {
        private readonly IFactGmvMaxProductService _factGmvMaxProductService;

        public FactGmvMaxProductController(IFactGmvMaxProductService factGmvMaxProductService)
        {
            _factGmvMaxProductService = factGmvMaxProductService;
        }

        /// <summary>
        /// L<PERSON>y dữ liệu GMV Max Product cho pivot table (với phân quyền)
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="creativeType">Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)</param>
        /// <param name="productId">ID sản phẩm cụ thể</param>
        /// <returns>Dữ liệu GMV Max Product đã được filter theo quyền</returns>
        [HttpGet("data")]
        public async Task<ActionResult<GetFactGmvMaxProductDataResponse>> GetGmvMaxProductData(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? creativeType = null,
            [FromQuery] string? productId = null,
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập dữ liệu GMV Max Product");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service with permissions
                var result = await _factGmvMaxProductService.GetListWithPermissionsAsync(from, to, currency);

                // Apply filters if specified
                if (!string.IsNullOrEmpty(creativeType))
                {
                    result.FactGmvMaxProducts = result.FactGmvMaxProducts.Where(fp =>
                        (fp.CreativeType ?? string.Empty).Equals(creativeType, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                if (!string.IsNullOrEmpty(productId))
                {
                    result.FactGmvMaxProducts = result.FactGmvMaxProducts.Where(fp =>
                        fp.ProductId.Equals(productId, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy thống kê tổng hợp GMV Max Product
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Thống kê tổng hợp</returns>
        [HttpGet("summary")]
        public IActionResult GetGmvMaxProductSummary(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // TODO: Implement server-side summary with Dapper if needed
                return Ok(new { message = "Summary endpoint not implemented." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy danh sách sản phẩm có hiệu suất thấp
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="roasThreshold">Ngưỡng ROAS thấp (mặc định: 1.5)</param>
        /// <param name="tacosThreshold">Ngưỡng TACOS cao (mặc định: 30%)</param>
        /// <returns>Danh sách sản phẩm có hiệu suất thấp</returns>
        [HttpGet("low-performance")]
        public async Task<IActionResult> GetLowPerformanceProducts(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] decimal roasThreshold = 1.5m,
            [FromQuery] decimal tacosThreshold = 30.0m)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem chỉ số hiệu suất GMV Max Product");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductService.GetListAsync(from, to);
                var data = result.FactGmvMaxProducts;

                var lowPerformance = data
                    .Where(p => (p.ROAS < roasThreshold) || (p.TACOS.HasValue && (p.TACOS * 100) > tacosThreshold))
                    .Select(p => new
                    {
                        p.ProductId,
                        p.ProductName,
                        p.StoreId,
                        ROAS = p.ROAS,
                        TACOS = p.TACOS,
                        CostPerOrder = p.CostPerOrderUSD,
                        GrossRevenue = p.GrossRevenueUSD,
                        Orders = p.Orders,
                        Status = p.ROAS < roasThreshold ? "Low ROAS" : "High TACOS"
                    })
                    .OrderBy(p => p.ROAS)
                    .Take(20)
                    .ToList();

                return Ok(lowPerformance);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy danh sách sản phẩm có chi phí cao
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="costPerOrderThreshold">Ngưỡng chi phí mỗi đơn cao (mặc định: 100000 VND)</param>
        /// <returns>Danh sách sản phẩm có chi phí cao</returns>
        [HttpGet("high-cost")]
        public async Task<IActionResult> GetHighCostProducts(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] decimal costPerOrderThreshold = 100000m)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem thông tin chi tiêu GMV Max Product");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductService.GetListAsync(from, to);
                var data = result.FactGmvMaxProducts;

                var highCost = data
                    .Where(p => p.CostPerOrderUSD > costPerOrderThreshold)
                    .Select(p => new
                    {
                        p.ProductId,
                        p.ProductName,
                        p.StoreId,
                        CostPerOrder = p.CostPerOrderUSD,
                        Orders = p.Orders,
                        ROAS = p.ROAS,
                        Status = "High Cost Per Order"
                    })
                    .OrderByDescending(p => p.CostPerOrder)
                    .Take(20)
                    .ToList();

                return Ok(highCost);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy cảnh báo hiệu suất sản phẩm
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Danh sách cảnh báo</returns>
        [HttpGet("alerts")]
        public async Task<IActionResult> GetProductAlerts(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem cảnh báo hiệu suất GMV Max Product");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductService.GetListAsync(from, to);
                var data = result.FactGmvMaxProducts;

                var alerts = new List<object>();

                // ROAS alerts
                var lowRoasProducts = data.Where(p => p.ROAS < 1.5m).Take(5);
                foreach (var product in lowRoasProducts)
                {
                    alerts.Add(new
                    {
                        Type = "Low ROAS",
                        Severity = product.ROAS < 1.0m ? "Critical" : "Warning",
                        ProductId = product.ProductId,
                        ProductName = product.ProductName,
                        StoreId = product.StoreId,
                        Value = product.ROAS,
                        Threshold = 1.5m,
                        Message = $"Sản phẩm {product.ProductName} có ROAS ({product.ROAS:F2}x) dưới ngưỡng 1.5x"
                    });
                }

                // TACOS alerts
                var highTacosProducts = data.Where(p => p.TACOS.HasValue && (p.TACOS * 100) > 30m).Take(5);
                foreach (var product in highTacosProducts)
                {
                    alerts.Add(new
                    {
                        Type = "High TACOS",
                        Severity = (product.TACOS * 100) > 40m ? "Critical" : "Warning",
                        ProductId = product.ProductId,
                        ProductName = product.ProductName,
                        StoreId = product.StoreId,
                        Value = product.TACOS,
                        Threshold = 0.30m,
                        Message = $"Sản phẩm {product.ProductName} có TACOS ({product.TACOS:P1}) vượt ngưỡng 30%"
                    });
                }

                return Ok(alerts.Take(10));
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy xu hướng GMV Max Product theo thời gian
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Xu hướng sản phẩm</returns>
        [HttpGet("trends")]
        public async Task<IActionResult> GetGmvMaxProductTrends(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem xu hướng GMV Max Product");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                var trends = await _factGmvMaxProductService.GetTrendsAsync(from, to);
                return Ok(trends);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy top sản phẩm bán chạy
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="limit">Số lượng sản phẩm trả về (mặc định: 10)</param>
        /// <returns>Top sản phẩm bán chạy</returns>
        [HttpGet("top-selling")]
        public async Task<IActionResult> GetTopSellingProducts(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int limit = 10)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem top sản phẩm bán chạy GMV Max Product");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                var topSelling = await _factGmvMaxProductService.GetTopSellingAsync(from, to, limit);
                return Ok(topSelling);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API chuyên cho Dashboard - Pre-aggregated data using Dapper + Dashboard Summary
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Dashboard data với current month, last month, weekly, yearly và dashboard summary</returns>
        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboardData(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập dashboard GMV Max Product");
                }

                // ✅ Get REAL data from Dapper repository
                var dashboardData = await _factGmvMaxProductService.GetDashboardAsync(currency);
                
                // ✅ Get Dashboard Summary using optimized Dapper query
                var dashboardSummary = await _factGmvMaxProductService.GetDashboardSummaryAsync(currency);
                
                // ✅ Get Detailed Analysis Data for 7 days
                var detailedAnalysisData = await _factGmvMaxProductService.GetDetailedAnalysisDataAsync(currency);

                // Kết hợp dashboard data với dashboard summary và detailed analysis
                var combinedData = new
                {
                    DashboardSummary = dashboardSummary,
                    DetailedAnalysisData = detailedAnalysisData,
                    CurrentMonth = dashboardData.CurrentMonth,
                    LastMonth = dashboardData.LastMonth,
                    WeeklyData = dashboardData.WeeklyData,
                    MonthlyData = dashboardData.MonthlyData,
                    // Revenue rankings (doanh thu)
                    CurrentWeekShopRanking = dashboardData.CurrentWeekShopRanking,
                    TwoWeeksAgoShopRanking = dashboardData.TwoWeeksAgoShopRanking,
                    OneWeekAgoShopRanking = dashboardData.OneWeekAgoShopRanking,
                    // Cost rankings (chi tiêu) - THÊM MỚI
                    CurrentWeekShopCostRanking = dashboardData.CurrentWeekShopCostRanking,
                    TwoWeeksAgoShopCostRanking = dashboardData.TwoWeeksAgoShopCostRanking,
                    OneWeekAgoShopCostRanking = dashboardData.OneWeekAgoShopCostRanking,
                };

                return Ok(combinedData);
            }
            catch (Exception ex)
            {
                // Fallback: return empty dashboard payload to avoid FE 400 handling
                var now = DateTime.Now;
                var emptyResponse = new
                {
                    DashboardSummary = new
                    {
                        TotalCost = 0m,
                        TotalNetCost = 0m,
                        TotalGrossRevenue = 0m,
                        AverageROAS = 0m,
                        AverageTACOS = 0m,
                        TotalOrders = 0,
                        CampaignCount = 0,
                        ActiveStores = 0,
                        ActiveAdvertisers = 0,
                        Month = now.Month,
                        Year = now.Year,
                        MonthName = now.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN")),
                        FromDate = new DateTime(now.Year, now.Month, 1),
                        ToDate = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month), 23, 59, 59),
                        DataPointCount = 0
                    },
                    DetailedAnalysisData = new
                    {
                        FromDate = now.AddDays(-6).Date,
                        ToDate = now.Date,
                        DailyData = new List<object>()
                    },
                    CurrentMonth = new { Year = now.Year, Month = now.Month, TotalRevenue = 0m },
                    LastMonth = new { Year = now.Month == 1 ? now.Year - 1 : now.Year, Month = now.Month == 1 ? 12 : now.Month - 1, TotalRevenue = 0m },
                    WeeklyData = new List<object>(),
                    MonthlyData = new List<object>(),
                    // Revenue rankings (doanh thu)
                    CurrentWeekShopRanking = new List<object>(),
                    TwoWeeksAgoShopRanking = new List<object>(),
                    OneWeekAgoShopRanking = new List<object>(),
                    // Cost rankings (chi tiêu) - THÊM MỚI
                    CurrentWeekShopCostRanking = new List<object>(),
                    TwoWeeksAgoShopCostRanking = new List<object>(),
                    OneWeekAgoShopCostRanking = new List<object>(),
                    Error = ex.Message
                };
                return Ok(emptyResponse);
            }
        }

        /// <summary>
        /// Lấy sản phẩm theo store
        /// </summary>
        /// <param name="storeId">ID của store</param>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Danh sách sản phẩm theo store</returns>
        [HttpGet("by-store/{storeId}")]
        public async Task<IActionResult> GetProductsByStore(
            string storeId,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem sản phẩm theo store GMV Max Product");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductService.GetListAsync(from, to);
                var data = result.FactGmvMaxProducts.Where(p => p.StoreId == storeId);

                var storeProducts = data
                    .GroupBy(p => new { p.ProductId, p.ProductName })
                    .Select(g => new
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.ProductName,
                        TotalRevenue = g.Sum(p => p.GrossRevenueUSD ?? 0),
                        TotalOrders = g.Sum(p => p.Orders),
                        AverageROAS = g.Where(p => p.ROAS.HasValue).Any() ? g.Where(p => p.ROAS.HasValue).Average(p => p.ROAS ?? 0) : 0,
                        AverageTACOS = g.Where(p => p.TACOS.HasValue).Any() ? g.Where(p => p.TACOS.HasValue).Average(p => p.TACOS ?? 0) : 0
                    })
                    .OrderByDescending(p => p.TotalRevenue)
                    .ToList();

                return Ok(storeProducts);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Summary Cards - Dữ liệu tổng hợp cho summary cards
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Summary cards data</returns>
        [HttpGet("summary-cards")]
        public async Task<IActionResult> GetSummaryCards(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập summary cards GMV Max Product");
                }

                var summaryData = await _factGmvMaxProductService.GetSummaryCardsAsync(currency);
                return Ok(summaryData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Overview Section - Dữ liệu tổng quan tháng hiện tại và tháng trước
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Overview section data</returns>
        [HttpGet("overview")]
        public async Task<IActionResult> GetOverviewSection(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập overview GMV Max Product");
                }

                var overviewData = await _factGmvMaxProductService.GetOverviewSectionAsync(currency);
                return Ok(overviewData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Charts Section - Dữ liệu biểu đồ tổng quan và chi tiết (GỘP)
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Combined charts data (overview + detailed)</returns>
        [HttpGet("charts")]
        public async Task<IActionResult> GetChartsData(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập charts GMV Max Product");
                }

                // ✅ OPTIMIZED: Gộp cả charts và detailed charts trong 1 API call
                var chartsData = await _factGmvMaxProductService.GetChartsDataAsync(currency);
                var detailedChartsData = await _factGmvMaxProductService.GetDetailedChartsAsync();

                // ✅ Return combined data structure
                var combinedData = new
                {
                    // Overview charts data
                    weeklyData = chartsData.WeeklyData,
                    monthlyData = chartsData.MonthlyData,
                    currency = chartsData.Currency,
                    generatedAt = chartsData.GeneratedAt,
                    
                    // Detailed charts data
                    financialAnalysis = detailedChartsData.FinancialAnalysis,
                    ordersAnalysis = detailedChartsData.OrdersAnalysis,
                    livePerformance = detailedChartsData.LivePerformance,
                    detailedFromDate = detailedChartsData.FromDate,
                    detailedToDate = detailedChartsData.ToDate,
                    detailedGeneratedAt = detailedChartsData.GeneratedAt
                };

                return Ok(combinedData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Rankings Section - Dữ liệu xếp hạng store
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Rankings data</returns>
        [HttpGet("rankings")]
        public async Task<IActionResult> GetRankingsData(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding - requires ViewAll or ViewAllAdvertisers
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập rankings GMV Max Product");
                }

                var rankingsData = await _factGmvMaxProductService.GetRankingsDataAsync(currency);
                return Ok(rankingsData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

    }
}
