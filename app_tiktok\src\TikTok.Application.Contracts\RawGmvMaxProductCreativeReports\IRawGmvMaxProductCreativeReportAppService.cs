using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.RawGmvMaxProductCreativeReports
{
    /// <summary>
    /// Application Service interface cho RawGmvMaxProductCreativeReport
    /// </summary>
    public interface IRawGmvMaxProductCreativeReportAppService : IApplicationService
    {
        /// <summary>
        /// Lấy danh sách RawGmvMaxProductCreativeReport với phân trang và lọc
        /// </summary>
        /// <param name="input">Thông tin phân trang và lọc</param>
        /// <returns>Danh sách DTO với thông tin phân trang</returns>
        Task<PagedResultDto<RawGmvMaxProductCreativeReportDto>> GetListAsync(
            GetRawGmvMaxProductCreativeReportListDto input);

        /// <summary>
        /// L<PERSON>y thông tin chi tiết RawGmvMaxProductCreativeReport theo ID
        /// </summary>
        /// <param name="id">ID của entity</param>
        /// <returns>DTO hoặc null nếu không tìm thấy</returns>
        Task<RawGmvMaxProductCreativeReportDto?> GetAsync(Guid id);

        /// <summary>
        /// Lấy danh sách Campaign IDs để sử dụng trong dropdown filter
        /// </summary>
        /// <returns>Danh sách Campaign IDs</returns>
        Task<List<string>> GetCampaignIdsAsync();

        /// <summary>
        /// Lấy thống kê tổng hợp video theo trạng thái
        /// </summary>
        /// <param name="input">Thông tin lọc</param>
        /// <returns>Thống kê video theo trạng thái</returns>
        Task<VideoStatusStatisticsDto> GetVideoStatusStatisticsAsync(
            GetRawGmvMaxProductCreativeReportListDto input);

        /// <summary>
        /// Lấy danh sách video cần xử lý (chỉ các trạng thái problematic)
        /// </summary>
        /// <param name="input">Thông tin phân trang và lọc</param>
        /// <returns>Danh sách video cần xử lý với phân trang</returns>
        Task<PagedResultDto<RawGmvMaxProductCreativeReportDto>> GetProblematicVideosAsync(
            GetRawGmvMaxProductCreativeReportListDto input);
    }
}
